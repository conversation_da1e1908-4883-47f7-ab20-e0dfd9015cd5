"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx":
/*!**********************************************************!*\
  !*** ./src/features/editor/components/shape-sidebar.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShapeSidebar: function() { return /* binding */ ShapeSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IoTriangle!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaDiamond!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaCircle,FaSquare,FaSquareFull!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _iconify_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @iconify/react */ \"(app-pages-browser)/./node_modules/@iconify/react/dist/iconify.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/shape-tool */ \"(app-pages-browser)/./src/features/editor/components/shape-tool.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Define available icons from Iconify\nconst iconifyElements = [\n    {\n        name: \"Heart\",\n        icon: \"lucide:heart\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Star\",\n        icon: \"lucide:star\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Arrow Right\",\n        icon: \"lucide:arrow-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Left\",\n        icon: \"lucide:arrow-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Up\",\n        icon: \"lucide:arrow-up\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down\",\n        icon: \"lucide:arrow-down\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Home\",\n        icon: \"lucide:home\",\n        category: \"interface\"\n    },\n    {\n        name: \"User\",\n        icon: \"lucide:user\",\n        category: \"interface\"\n    },\n    {\n        name: \"Settings\",\n        icon: \"lucide:settings\",\n        category: \"interface\"\n    },\n    {\n        name: \"Mail\",\n        icon: \"lucide:mail\",\n        category: \"interface\"\n    },\n    {\n        name: \"Phone\",\n        icon: \"lucide:phone\",\n        category: \"interface\"\n    },\n    {\n        name: \"Calendar\",\n        icon: \"lucide:calendar\",\n        category: \"interface\"\n    },\n    {\n        name: \"Clock\",\n        icon: \"lucide:clock\",\n        category: \"interface\"\n    },\n    {\n        name: \"Camera\",\n        icon: \"lucide:camera\",\n        category: \"interface\"\n    },\n    {\n        name: \"Music\",\n        icon: \"lucide:music\",\n        category: \"media\"\n    },\n    {\n        name: \"Play\",\n        icon: \"lucide:play\",\n        category: \"media\"\n    },\n    {\n        name: \"Pause\",\n        icon: \"lucide:pause\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume\",\n        icon: \"lucide:volume-2\",\n        category: \"media\"\n    },\n    {\n        name: \"Wifi\",\n        icon: \"lucide:wifi\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery\",\n        icon: \"lucide:battery\",\n        category: \"tech\"\n    },\n    {\n        name: \"Bluetooth\",\n        icon: \"lucide:bluetooth\",\n        category: \"tech\"\n    },\n    {\n        name: \"Download\",\n        icon: \"lucide:download\",\n        category: \"actions\"\n    },\n    {\n        name: \"Upload\",\n        icon: \"lucide:upload\",\n        category: \"actions\"\n    },\n    {\n        name: \"Search\",\n        icon: \"lucide:search\",\n        category: \"actions\"\n    },\n    {\n        name: \"Plus\",\n        icon: \"lucide:plus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Minus\",\n        icon: \"lucide:minus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Check\",\n        icon: \"lucide:check\",\n        category: \"actions\"\n    },\n    {\n        name: \"X\",\n        icon: \"lucide:x\",\n        category: \"actions\"\n    }\n];\nconst ShapeSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    // Filter icons based on search term\n    const filteredIcons = iconifyElements.filter((element)=>element.name.toLowerCase().includes(searchTerm.toLowerCase()) || element.category.toLowerCase().includes(searchTerm.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"shapes\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__.ToolSidebarHeader, {\n                title: \"Elements\",\n                description: \"Add elements to your canvas\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                    placeholder: \"Search elements...\",\n                    value: searchTerm,\n                    onChange: (e)=>setSearchTerm(e.target.value),\n                    className: \"w-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-3 text-gray-700\",\n                            children: \"Basic Shapes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addCircle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCircle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addSoftRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquare\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquareFull\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addInverseTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle,\n                                    iconClassName: \"rotate-180\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addDiamond(),\n                                    icon: _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaDiamond\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-3 text-gray-700\",\n                            children: \"Icons & Elements\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4\",\n                            children: filteredIcons.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Add the icon as an SVG element to the canvas\n                                        // This is a placeholder - you might need to implement addIcon method in your editor\n                                        console.log(\"Adding \".concat(element.name, \" icon\"));\n                                    },\n                                    className: \"aspect-square border rounded-md p-3 hover:bg-gray-50 transition-colors flex items-center justify-center group\",\n                                    title: element.name,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iconify_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        icon: element.icon,\n                                        className: \"h-6 w-6 text-gray-700 group-hover:text-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, element.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShapeSidebar, \"+YdqPTpSlp4r5CWiFEQiF/UjThM=\");\n_c = ShapeSidebar;\nvar _c;\n$RefreshReg$(_c, \"ShapeSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@iconify/react/dist/iconify.js":
/*!*****************************************************!*\
  !*** ./node_modules/@iconify/react/dist/iconify.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icon: function() { return /* binding */ Icon; },\n/* harmony export */   InlineIcon: function() { return /* binding */ InlineIcon; },\n/* harmony export */   _api: function() { return /* binding */ _api; },\n/* harmony export */   addAPIProvider: function() { return /* binding */ addAPIProvider; },\n/* harmony export */   addCollection: function() { return /* binding */ addCollection; },\n/* harmony export */   addIcon: function() { return /* binding */ addIcon; },\n/* harmony export */   buildIcon: function() { return /* binding */ iconToSVG; },\n/* harmony export */   calculateSize: function() { return /* binding */ calculateSize; },\n/* harmony export */   getIcon: function() { return /* binding */ getIcon; },\n/* harmony export */   iconLoaded: function() { return /* binding */ iconLoaded; },\n/* harmony export */   listIcons: function() { return /* binding */ listIcons; },\n/* harmony export */   loadIcon: function() { return /* binding */ loadIcon; },\n/* harmony export */   loadIcons: function() { return /* binding */ loadIcons; },\n/* harmony export */   replaceIDs: function() { return /* binding */ replaceIDs; },\n/* harmony export */   setCustomIconLoader: function() { return /* binding */ setCustomIconLoader; },\n/* harmony export */   setCustomIconsLoader: function() { return /* binding */ setCustomIconsLoader; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ Icon,InlineIcon,_api,addAPIProvider,addCollection,addIcon,buildIcon,calculateSize,getIcon,iconLoaded,listIcons,loadIcon,loadIcons,replaceIDs,setCustomIconLoader,setCustomIconsLoader auto */ var _s = $RefreshSig$();\n\nconst defaultIconDimensions = Object.freeze({\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n});\nconst defaultIconTransformations = Object.freeze({\n    rotate: 0,\n    vFlip: false,\n    hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n    ...defaultIconDimensions,\n    ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n    ...defaultIconProps,\n    body: \"\",\n    hidden: false\n});\nfunction mergeIconTransformations(obj1, obj2) {\n    const result = {};\n    if (!obj1.hFlip !== !obj2.hFlip) {\n        result.hFlip = true;\n    }\n    if (!obj1.vFlip !== !obj2.vFlip) {\n        result.vFlip = true;\n    }\n    const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n    if (rotate) {\n        result.rotate = rotate;\n    }\n    return result;\n}\nfunction mergeIconData(parent, child) {\n    const result = mergeIconTransformations(parent, child);\n    for(const key in defaultExtendedIconProps){\n        if (key in defaultIconTransformations) {\n            if (key in parent && !(key in result)) {\n                result[key] = defaultIconTransformations[key];\n            }\n        } else if (key in child) {\n            result[key] = child[key];\n        } else if (key in parent) {\n            result[key] = parent[key];\n        }\n    }\n    return result;\n}\nfunction getIconsTree(data, names) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    const resolved = /* @__PURE__ */ Object.create(null);\n    function resolve(name) {\n        if (icons[name]) {\n            return resolved[name] = [];\n        }\n        if (!(name in resolved)) {\n            resolved[name] = null;\n            const parent = aliases[name] && aliases[name].parent;\n            const value = parent && resolve(parent);\n            if (value) {\n                resolved[name] = [\n                    parent\n                ].concat(value);\n            }\n        }\n        return resolved[name];\n    }\n    Object.keys(icons).concat(Object.keys(aliases)).forEach(resolve);\n    return resolved;\n}\nfunction internalGetIconData(data, name, tree) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    let currentProps = {};\n    function parse(name2) {\n        currentProps = mergeIconData(icons[name2] || aliases[name2], currentProps);\n    }\n    parse(name);\n    tree.forEach(parse);\n    return mergeIconData(data, currentProps);\n}\nfunction parseIconSet(data, callback) {\n    const names = [];\n    if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n        return names;\n    }\n    if (data.not_found instanceof Array) {\n        data.not_found.forEach((name)=>{\n            callback(name, null);\n            names.push(name);\n        });\n    }\n    const tree = getIconsTree(data);\n    for(const name in tree){\n        const item = tree[name];\n        if (item) {\n            callback(name, internalGetIconData(data, name, item));\n            names.push(name);\n        }\n    }\n    return names;\n}\nconst optionalPropertyDefaults = {\n    provider: \"\",\n    aliases: {},\n    not_found: {},\n    ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n    for(const prop in defaults){\n        if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction quicklyValidateIconSet(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n        return null;\n    }\n    const data = obj;\n    if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n        return null;\n    }\n    if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n        return null;\n    }\n    const icons = data.icons;\n    for(const name in icons){\n        const icon = icons[name];\n        if (// Name cannot be empty\n        !name || // Must have body\n        typeof icon.body !== \"string\" || // Check other props\n        !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    for(const name in aliases){\n        const icon = aliases[name];\n        const parent = icon.parent;\n        if (// Name cannot be empty\n        !name || // Parent must be set and point to existing icon\n        typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n        !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    return data;\n}\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = function(value, validate, allowSimpleName) {\n    let provider = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"\";\n    const colonSeparated = value.split(\":\");\n    if (value.slice(0, 1) === \"@\") {\n        if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n            return null;\n        }\n        provider = colonSeparated.shift().slice(1);\n    }\n    if (colonSeparated.length > 3 || !colonSeparated.length) {\n        return null;\n    }\n    if (colonSeparated.length > 1) {\n        const name2 = colonSeparated.pop();\n        const prefix = colonSeparated.pop();\n        const result = {\n            // Allow provider without '@': \"provider:prefix:name\"\n            provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n            prefix,\n            name: name2\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    const name = colonSeparated[0];\n    const dashSeparated = name.split(\"-\");\n    if (dashSeparated.length > 1) {\n        const result = {\n            provider,\n            prefix: dashSeparated.shift(),\n            name: dashSeparated.join(\"-\")\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    if (allowSimpleName && provider === \"\") {\n        const result = {\n            provider,\n            prefix: \"\",\n            name\n        };\n        return validate && !validateIconName(result, allowSimpleName) ? null : result;\n    }\n    return null;\n};\nconst validateIconName = (icon, allowSimpleName)=>{\n    if (!icon) {\n        return false;\n    }\n    return !!// Check name: cannot be empty\n    ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n    return {\n        provider,\n        prefix,\n        icons: /* @__PURE__ */ Object.create(null),\n        missing: /* @__PURE__ */ new Set()\n    };\n}\nfunction getStorage(provider, prefix) {\n    const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n    return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n    if (!quicklyValidateIconSet(data)) {\n        return [];\n    }\n    return parseIconSet(data, (name, icon)=>{\n        if (icon) {\n            storage.icons[name] = icon;\n        } else {\n            storage.missing.add(name);\n        }\n    });\n}\nfunction addIconToStorage(storage, name, icon) {\n    try {\n        if (typeof icon.body === \"string\") {\n            storage.icons[name] = {\n                ...icon\n            };\n            return true;\n        }\n    } catch (err) {}\n    return false;\n}\nfunction listIcons(provider, prefix) {\n    let allIcons = [];\n    const providers = typeof provider === \"string\" ? [\n        provider\n    ] : Object.keys(dataStorage);\n    providers.forEach((provider2)=>{\n        const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [\n            prefix\n        ] : Object.keys(dataStorage[provider2] || {});\n        prefixes.forEach((prefix2)=>{\n            const storage = getStorage(provider2, prefix2);\n            allIcons = allIcons.concat(Object.keys(storage.icons).map((name)=>(provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name));\n        });\n    });\n    return allIcons;\n}\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n    if (typeof allow === \"boolean\") {\n        simpleNames = allow;\n    }\n    return simpleNames;\n}\nfunction getIconData(name) {\n    const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n    if (icon) {\n        const storage = getStorage(icon.provider, icon.prefix);\n        const iconName = icon.name;\n        return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n    }\n}\nfunction addIcon(name, data) {\n    const icon = stringToIcon(name, true, simpleNames);\n    if (!icon) {\n        return false;\n    }\n    const storage = getStorage(icon.provider, icon.prefix);\n    if (data) {\n        return addIconToStorage(storage, icon.name, data);\n    } else {\n        storage.missing.add(icon.name);\n        return true;\n    }\n}\nfunction addCollection(data, provider) {\n    if (typeof data !== \"object\") {\n        return false;\n    }\n    if (typeof provider !== \"string\") {\n        provider = data.provider || \"\";\n    }\n    if (simpleNames && !provider && !data.prefix) {\n        let added = false;\n        if (quicklyValidateIconSet(data)) {\n            data.prefix = \"\";\n            parseIconSet(data, (name, icon)=>{\n                if (addIcon(name, icon)) {\n                    added = true;\n                }\n            });\n        }\n        return added;\n    }\n    const prefix = data.prefix;\n    if (!validateIconName({\n        prefix,\n        name: \"a\"\n    })) {\n        return false;\n    }\n    const storage = getStorage(provider, prefix);\n    return !!addIconSet(storage, data);\n}\nfunction iconLoaded(name) {\n    return !!getIconData(name);\n}\nfunction getIcon(name) {\n    const result = getIconData(name);\n    return result ? {\n        ...defaultIconProps,\n        ...result\n    } : result;\n}\nconst defaultIconSizeCustomisations = Object.freeze({\n    width: null,\n    height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n    // Dimensions\n    ...defaultIconSizeCustomisations,\n    // Transformations\n    ...defaultIconTransformations\n});\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n    if (ratio === 1) {\n        return size;\n    }\n    precision = precision || 100;\n    if (typeof size === \"number\") {\n        return Math.ceil(size * ratio * precision) / precision;\n    }\n    if (typeof size !== \"string\") {\n        return size;\n    }\n    const oldParts = size.split(unitsSplit);\n    if (oldParts === null || !oldParts.length) {\n        return size;\n    }\n    const newParts = [];\n    let code = oldParts.shift();\n    let isNumber = unitsTest.test(code);\n    while(true){\n        if (isNumber) {\n            const num = parseFloat(code);\n            if (isNaN(num)) {\n                newParts.push(code);\n            } else {\n                newParts.push(Math.ceil(num * ratio * precision) / precision);\n            }\n        } else {\n            newParts.push(code);\n        }\n        code = oldParts.shift();\n        if (code === void 0) {\n            return newParts.join(\"\");\n        }\n        isNumber = !isNumber;\n    }\n}\nfunction splitSVGDefs(content) {\n    let tag = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"defs\";\n    let defs = \"\";\n    const index = content.indexOf(\"<\" + tag);\n    while(index >= 0){\n        const start = content.indexOf(\">\", index);\n        const end = content.indexOf(\"</\" + tag);\n        if (start === -1 || end === -1) {\n            break;\n        }\n        const endEnd = content.indexOf(\">\", end);\n        if (endEnd === -1) {\n            break;\n        }\n        defs += content.slice(start + 1, end).trim();\n        content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n    }\n    return {\n        defs,\n        content\n    };\n}\nfunction mergeDefsAndContent(defs, content) {\n    return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n    const split = splitSVGDefs(body);\n    return mergeDefsAndContent(split.defs, start + split.content + end);\n}\nconst isUnsetKeyword = (value)=>value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n    const fullIcon = {\n        ...defaultIconProps,\n        ...icon\n    };\n    const fullCustomisations = {\n        ...defaultIconCustomisations,\n        ...customisations\n    };\n    const box = {\n        left: fullIcon.left,\n        top: fullIcon.top,\n        width: fullIcon.width,\n        height: fullIcon.height\n    };\n    let body = fullIcon.body;\n    [\n        fullIcon,\n        fullCustomisations\n    ].forEach((props)=>{\n        const transformations = [];\n        const hFlip = props.hFlip;\n        const vFlip = props.vFlip;\n        let rotation = props.rotate;\n        if (hFlip) {\n            if (vFlip) {\n                rotation += 2;\n            } else {\n                transformations.push(\"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\");\n                transformations.push(\"scale(-1 1)\");\n                box.top = box.left = 0;\n            }\n        } else if (vFlip) {\n            transformations.push(\"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\");\n            transformations.push(\"scale(1 -1)\");\n            box.top = box.left = 0;\n        }\n        let tempValue;\n        if (rotation < 0) {\n            rotation -= Math.floor(rotation / 4) * 4;\n        }\n        rotation = rotation % 4;\n        switch(rotation){\n            case 1:\n                tempValue = box.height / 2 + box.top;\n                transformations.unshift(\"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n            case 2:\n                transformations.unshift(\"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\");\n                break;\n            case 3:\n                tempValue = box.width / 2 + box.left;\n                transformations.unshift(\"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n        }\n        if (rotation % 2 === 1) {\n            if (box.left !== box.top) {\n                tempValue = box.left;\n                box.left = box.top;\n                box.top = tempValue;\n            }\n            if (box.width !== box.height) {\n                tempValue = box.width;\n                box.width = box.height;\n                box.height = tempValue;\n            }\n        }\n        if (transformations.length) {\n            body = wrapSVGContent(body, '<g transform=\"' + transformations.join(\" \") + '\">', \"</g>\");\n        }\n    });\n    const customisationsWidth = fullCustomisations.width;\n    const customisationsHeight = fullCustomisations.height;\n    const boxWidth = box.width;\n    const boxHeight = box.height;\n    let width;\n    let height;\n    if (customisationsWidth === null) {\n        height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n        width = calculateSize(height, boxWidth / boxHeight);\n    } else {\n        width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n        height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    }\n    const attributes = {};\n    const setAttr = (prop, value)=>{\n        if (!isUnsetKeyword(value)) {\n            attributes[prop] = value.toString();\n        }\n    };\n    setAttr(\"width\", width);\n    setAttr(\"height\", height);\n    const viewBox = [\n        box.left,\n        box.top,\n        boxWidth,\n        boxHeight\n    ];\n    attributes.viewBox = viewBox.join(\" \");\n    return {\n        attributes,\n        viewBox,\n        body\n    };\n}\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body) {\n    let prefix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : randomPrefix;\n    const ids = [];\n    let match;\n    while(match = regex.exec(body)){\n        ids.push(match[1]);\n    }\n    if (!ids.length) {\n        return body;\n    }\n    const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n    ids.forEach((id)=>{\n        const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n        const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n        body = body.replace(// Allowed characters before id: [#;\"]\n        // Allowed characters after id: [)\"], .[a-z]\n        new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"), \"$1\" + newID + suffix + \"$3\");\n    });\n    body = body.replace(new RegExp(suffix, \"g\"), \"\");\n    return body;\n}\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n    storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n    return storage[provider] || storage[\"\"];\n}\nfunction createAPIConfig(source) {\n    let resources;\n    if (typeof source.resources === \"string\") {\n        resources = [\n            source.resources\n        ];\n    } else {\n        resources = source.resources;\n        if (!(resources instanceof Array) || !resources.length) {\n            return null;\n        }\n    }\n    const result = {\n        // API hosts\n        resources,\n        // Root path\n        path: source.path || \"/\",\n        // URL length limit\n        maxURL: source.maxURL || 500,\n        // Timeout before next host is used.\n        rotate: source.rotate || 750,\n        // Timeout before failing query.\n        timeout: source.timeout || 5e3,\n        // Randomise default API end point.\n        random: source.random === true,\n        // Start index\n        index: source.index || 0,\n        // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n        dataAfterTimeout: source.dataAfterTimeout !== false\n    };\n    return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n    \"https://api.simplesvg.com\",\n    \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile(fallBackAPISources.length > 0){\n    if (fallBackAPISources.length === 1) {\n        fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n        if (Math.random() > 0.5) {\n            fallBackAPI.push(fallBackAPISources.shift());\n        } else {\n            fallBackAPI.push(fallBackAPISources.pop());\n        }\n    }\n}\nconfigStorage[\"\"] = createAPIConfig({\n    resources: [\n        \"https://api.iconify.design\"\n    ].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n    const config = createAPIConfig(customConfig);\n    if (config === null) {\n        return false;\n    }\n    configStorage[provider] = config;\n    return true;\n}\nfunction getAPIConfig(provider) {\n    return configStorage[provider];\n}\nfunction listAPIProviders() {\n    return Object.keys(configStorage);\n}\nconst detectFetch = ()=>{\n    let callback;\n    try {\n        callback = fetch;\n        if (typeof callback === \"function\") {\n            return callback;\n        }\n    } catch (err) {}\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n    fetchModule = fetch2;\n}\nfunction getFetch() {\n    return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n        return 0;\n    }\n    let result;\n    if (!config.maxURL) {\n        result = 0;\n    } else {\n        let maxHostLength = 0;\n        config.resources.forEach((item)=>{\n            const host = item;\n            maxHostLength = Math.max(maxHostLength, host.length);\n        });\n        const url = prefix + \".json?icons=\";\n        result = config.maxURL - maxHostLength - config.path.length - url.length;\n    }\n    return result;\n}\nfunction shouldAbort(status) {\n    return status === 404;\n}\nconst prepare = (provider, prefix, icons)=>{\n    const results = [];\n    const maxLength = calculateMaxLength(provider, prefix);\n    const type = \"icons\";\n    let item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n    };\n    let length = 0;\n    icons.forEach((name, index)=>{\n        length += name.length + 1;\n        if (length >= maxLength && index > 0) {\n            results.push(item);\n            item = {\n                type,\n                provider,\n                prefix,\n                icons: []\n            };\n            length = name.length;\n        }\n        item.icons.push(name);\n    });\n    results.push(item);\n    return results;\n};\nfunction getPath(provider) {\n    if (typeof provider === \"string\") {\n        const config = getAPIConfig(provider);\n        if (config) {\n            return config.path;\n        }\n    }\n    return \"/\";\n}\nconst send = (host, params, callback)=>{\n    if (!fetchModule) {\n        callback(\"abort\", 424);\n        return;\n    }\n    let path = getPath(params.provider);\n    switch(params.type){\n        case \"icons\":\n            {\n                const prefix = params.prefix;\n                const icons = params.icons;\n                const iconsList = icons.join(\",\");\n                const urlParams = new URLSearchParams({\n                    icons: iconsList\n                });\n                path += prefix + \".json?\" + urlParams.toString();\n                break;\n            }\n        case \"custom\":\n            {\n                const uri = params.uri;\n                path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n                break;\n            }\n        default:\n            callback(\"abort\", 400);\n            return;\n    }\n    let defaultError = 503;\n    fetchModule(host + path).then((response)=>{\n        const status = response.status;\n        if (status !== 200) {\n            setTimeout(()=>{\n                callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n            });\n            return;\n        }\n        defaultError = 501;\n        return response.json();\n    }).then((data)=>{\n        if (typeof data !== \"object\" || data === null) {\n            setTimeout(()=>{\n                if (data === 404) {\n                    callback(\"abort\", data);\n                } else {\n                    callback(\"next\", defaultError);\n                }\n            });\n            return;\n        }\n        setTimeout(()=>{\n            callback(\"success\", data);\n        });\n    }).catch(()=>{\n        callback(\"next\", defaultError);\n    });\n};\nconst fetchAPIModule = {\n    prepare,\n    send\n};\nfunction sortIcons(icons) {\n    const result = {\n        loaded: [],\n        missing: [],\n        pending: []\n    };\n    const storage = /* @__PURE__ */ Object.create(null);\n    icons.sort((a, b)=>{\n        if (a.provider !== b.provider) {\n            return a.provider.localeCompare(b.provider);\n        }\n        if (a.prefix !== b.prefix) {\n            return a.prefix.localeCompare(b.prefix);\n        }\n        return a.name.localeCompare(b.name);\n    });\n    let lastIcon = {\n        provider: \"\",\n        prefix: \"\",\n        name: \"\"\n    };\n    icons.forEach((icon)=>{\n        if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n            return;\n        }\n        lastIcon = icon;\n        const provider = icon.provider;\n        const prefix = icon.prefix;\n        const name = icon.name;\n        const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n        const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n        let list;\n        if (name in localStorage.icons) {\n            list = result.loaded;\n        } else if (prefix === \"\" || localStorage.missing.has(name)) {\n            list = result.missing;\n        } else {\n            list = result.pending;\n        }\n        const item = {\n            provider,\n            prefix,\n            name\n        };\n        list.push(item);\n    });\n    return result;\n}\nfunction removeCallback(storages, id) {\n    storages.forEach((storage)=>{\n        const items = storage.loaderCallbacks;\n        if (items) {\n            storage.loaderCallbacks = items.filter((row)=>row.id !== id);\n        }\n    });\n}\nfunction updateCallbacks(storage) {\n    if (!storage.pendingCallbacksFlag) {\n        storage.pendingCallbacksFlag = true;\n        setTimeout(()=>{\n            storage.pendingCallbacksFlag = false;\n            const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n            if (!items.length) {\n                return;\n            }\n            let hasPending = false;\n            const provider = storage.provider;\n            const prefix = storage.prefix;\n            items.forEach((item)=>{\n                const icons = item.icons;\n                const oldLength = icons.pending.length;\n                icons.pending = icons.pending.filter((icon)=>{\n                    if (icon.prefix !== prefix) {\n                        return true;\n                    }\n                    const name = icon.name;\n                    if (storage.icons[name]) {\n                        icons.loaded.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else if (storage.missing.has(name)) {\n                        icons.missing.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else {\n                        hasPending = true;\n                        return true;\n                    }\n                    return false;\n                });\n                if (icons.pending.length !== oldLength) {\n                    if (!hasPending) {\n                        removeCallback([\n                            storage\n                        ], item.id);\n                    }\n                    item.callback(icons.loaded.slice(0), icons.missing.slice(0), icons.pending.slice(0), item.abort);\n                }\n            });\n        });\n    }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n    const id = idCounter++;\n    const abort = removeCallback.bind(null, pendingSources, id);\n    if (!icons.pending.length) {\n        return abort;\n    }\n    const item = {\n        id,\n        icons,\n        callback,\n        abort\n    };\n    pendingSources.forEach((storage)=>{\n        (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n    });\n    return abort;\n}\nfunction listToIcons(list) {\n    let validate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true, simpleNames = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    const result = [];\n    list.forEach((item)=>{\n        const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n        if (icon) {\n            result.push(icon);\n        }\n    });\n    return result;\n}\n// src/config.ts\nvar defaultConfig = {\n    resources: [],\n    index: 0,\n    timeout: 2e3,\n    rotate: 750,\n    random: false,\n    dataAfterTimeout: false\n};\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n    const resourcesCount = config.resources.length;\n    const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n    let resources;\n    if (config.random) {\n        let list = config.resources.slice(0);\n        resources = [];\n        while(list.length > 1){\n            const nextIndex = Math.floor(Math.random() * list.length);\n            resources.push(list[nextIndex]);\n            list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n        }\n        resources = resources.concat(list);\n    } else {\n        resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n    }\n    const startTime = Date.now();\n    let status = \"pending\";\n    let queriesSent = 0;\n    let lastError;\n    let timer = null;\n    let queue = [];\n    let doneCallbacks = [];\n    if (typeof done === \"function\") {\n        doneCallbacks.push(done);\n    }\n    function resetTimer() {\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n        }\n    }\n    function abort() {\n        if (status === \"pending\") {\n            status = \"aborted\";\n        }\n        resetTimer();\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function subscribe(callback, overwrite) {\n        if (overwrite) {\n            doneCallbacks = [];\n        }\n        if (typeof callback === \"function\") {\n            doneCallbacks.push(callback);\n        }\n    }\n    function getQueryStatus() {\n        return {\n            startTime,\n            payload,\n            status,\n            queriesSent,\n            queriesPending: queue.length,\n            subscribe,\n            abort\n        };\n    }\n    function failQuery() {\n        status = \"failed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(void 0, lastError);\n        });\n    }\n    function clearQueue() {\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function moduleResponse(item, response, data) {\n        const isError = response !== \"success\";\n        queue = queue.filter((queued)=>queued !== item);\n        switch(status){\n            case \"pending\":\n                break;\n            case \"failed\":\n                if (isError || !config.dataAfterTimeout) {\n                    return;\n                }\n                break;\n            default:\n                return;\n        }\n        if (response === \"abort\") {\n            lastError = data;\n            failQuery();\n            return;\n        }\n        if (isError) {\n            lastError = data;\n            if (!queue.length) {\n                if (!resources.length) {\n                    failQuery();\n                } else {\n                    execNext();\n                }\n            }\n            return;\n        }\n        resetTimer();\n        clearQueue();\n        if (!config.random) {\n            const index = config.resources.indexOf(item.resource);\n            if (index !== -1 && index !== config.index) {\n                config.index = index;\n            }\n        }\n        status = \"completed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(data);\n        });\n    }\n    function execNext() {\n        if (status !== \"pending\") {\n            return;\n        }\n        resetTimer();\n        const resource = resources.shift();\n        if (resource === void 0) {\n            if (queue.length) {\n                timer = setTimeout(()=>{\n                    resetTimer();\n                    if (status === \"pending\") {\n                        clearQueue();\n                        failQuery();\n                    }\n                }, config.timeout);\n                return;\n            }\n            failQuery();\n            return;\n        }\n        const item = {\n            status: \"pending\",\n            resource,\n            callback: (status2, data)=>{\n                moduleResponse(item, status2, data);\n            }\n        };\n        queue.push(item);\n        queriesSent++;\n        timer = setTimeout(execNext, config.rotate);\n        query(resource, payload, item.callback);\n    }\n    setTimeout(execNext);\n    return getQueryStatus;\n}\n// src/index.ts\nfunction initRedundancy(cfg) {\n    const config = {\n        ...defaultConfig,\n        ...cfg\n    };\n    let queries = [];\n    function cleanup() {\n        queries = queries.filter((item)=>item().status === \"pending\");\n    }\n    function query(payload, queryCallback, doneCallback) {\n        const query2 = sendQuery(config, payload, queryCallback, (data, error)=>{\n            cleanup();\n            if (doneCallback) {\n                doneCallback(data, error);\n            }\n        });\n        queries.push(query2);\n        return query2;\n    }\n    function find(callback) {\n        return queries.find((value)=>{\n            return callback(value);\n        }) || null;\n    }\n    const instance = {\n        query,\n        find,\n        setIndex: (index)=>{\n            config.index = index;\n        },\n        getIndex: ()=>config.index,\n        cleanup\n    };\n    return instance;\n}\nfunction emptyCallback$1() {}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n    if (!redundancyCache[provider]) {\n        const config = getAPIConfig(provider);\n        if (!config) {\n            return;\n        }\n        const redundancy = initRedundancy(config);\n        const cachedReundancy = {\n            config,\n            redundancy\n        };\n        redundancyCache[provider] = cachedReundancy;\n    }\n    return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n    let redundancy;\n    let send;\n    if (typeof target === \"string\") {\n        const api = getAPIModule(target);\n        if (!api) {\n            callback(void 0, 424);\n            return emptyCallback$1;\n        }\n        send = api.send;\n        const cached = getRedundancyCache(target);\n        if (cached) {\n            redundancy = cached.redundancy;\n        }\n    } else {\n        const config = createAPIConfig(target);\n        if (config) {\n            redundancy = initRedundancy(config);\n            const moduleKey = target.resources ? target.resources[0] : \"\";\n            const api = getAPIModule(moduleKey);\n            if (api) {\n                send = api.send;\n            }\n        }\n    }\n    if (!redundancy || !send) {\n        callback(void 0, 424);\n        return emptyCallback$1;\n    }\n    return redundancy.query(query, send, callback)().abort;\n}\nfunction emptyCallback() {}\nfunction loadedNewIcons(storage) {\n    if (!storage.iconsLoaderFlag) {\n        storage.iconsLoaderFlag = true;\n        setTimeout(()=>{\n            storage.iconsLoaderFlag = false;\n            updateCallbacks(storage);\n        });\n    }\n}\nfunction checkIconNamesForAPI(icons) {\n    const valid = [];\n    const invalid = [];\n    icons.forEach((name)=>{\n        (name.match(matchIconName) ? valid : invalid).push(name);\n    });\n    return {\n        valid,\n        invalid\n    };\n}\nfunction parseLoaderResponse(storage, icons, data) {\n    function checkMissing() {\n        const pending = storage.pendingIcons;\n        icons.forEach((name)=>{\n            if (pending) {\n                pending.delete(name);\n            }\n            if (!storage.icons[name]) {\n                storage.missing.add(name);\n            }\n        });\n    }\n    if (data && typeof data === \"object\") {\n        try {\n            const parsed = addIconSet(storage, data);\n            if (!parsed.length) {\n                checkMissing();\n                return;\n            }\n        } catch (err) {\n            console.error(err);\n        }\n    }\n    checkMissing();\n    loadedNewIcons(storage);\n}\nfunction parsePossiblyAsyncResponse(response, callback) {\n    if (response instanceof Promise) {\n        response.then((data)=>{\n            callback(data);\n        }).catch(()=>{\n            callback(null);\n        });\n    } else {\n        callback(response);\n    }\n}\nfunction loadNewIcons(storage, icons) {\n    if (!storage.iconsToLoad) {\n        storage.iconsToLoad = icons;\n    } else {\n        storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n    }\n    if (!storage.iconsQueueFlag) {\n        storage.iconsQueueFlag = true;\n        setTimeout(()=>{\n            storage.iconsQueueFlag = false;\n            const { provider, prefix } = storage;\n            const icons2 = storage.iconsToLoad;\n            delete storage.iconsToLoad;\n            if (!icons2 || !icons2.length) {\n                return;\n            }\n            const customIconLoader = storage.loadIcon;\n            if (storage.loadIcons && (icons2.length > 1 || !customIconLoader)) {\n                parsePossiblyAsyncResponse(storage.loadIcons(icons2, prefix, provider), (data)=>{\n                    parseLoaderResponse(storage, icons2, data);\n                });\n                return;\n            }\n            if (customIconLoader) {\n                icons2.forEach((name)=>{\n                    const response = customIconLoader(name, prefix, provider);\n                    parsePossiblyAsyncResponse(response, (data)=>{\n                        const iconSet = data ? {\n                            prefix,\n                            icons: {\n                                [name]: data\n                            }\n                        } : null;\n                        parseLoaderResponse(storage, [\n                            name\n                        ], iconSet);\n                    });\n                });\n                return;\n            }\n            const { valid, invalid } = checkIconNamesForAPI(icons2);\n            if (invalid.length) {\n                parseLoaderResponse(storage, invalid, null);\n            }\n            if (!valid.length) {\n                return;\n            }\n            const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;\n            if (!api) {\n                parseLoaderResponse(storage, valid, null);\n                return;\n            }\n            const params = api.prepare(provider, prefix, valid);\n            params.forEach((item)=>{\n                sendAPIQuery(provider, item, (data)=>{\n                    parseLoaderResponse(storage, item.icons, data);\n                });\n            });\n        });\n    }\n}\nconst loadIcons = (icons, callback)=>{\n    const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n    const sortedIcons = sortIcons(cleanedIcons);\n    if (!sortedIcons.pending.length) {\n        let callCallback = true;\n        if (callback) {\n            setTimeout(()=>{\n                if (callCallback) {\n                    callback(sortedIcons.loaded, sortedIcons.missing, sortedIcons.pending, emptyCallback);\n                }\n            });\n        }\n        return ()=>{\n            callCallback = false;\n        };\n    }\n    const newIcons = /* @__PURE__ */ Object.create(null);\n    const sources = [];\n    let lastProvider, lastPrefix;\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix } = icon;\n        if (prefix === lastPrefix && provider === lastProvider) {\n            return;\n        }\n        lastProvider = provider;\n        lastPrefix = prefix;\n        sources.push(getStorage(provider, prefix));\n        const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n        if (!providerNewIcons[prefix]) {\n            providerNewIcons[prefix] = [];\n        }\n    });\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix, name } = icon;\n        const storage = getStorage(provider, prefix);\n        const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n        if (!pendingQueue.has(name)) {\n            pendingQueue.add(name);\n            newIcons[provider][prefix].push(name);\n        }\n    });\n    sources.forEach((storage)=>{\n        const list = newIcons[storage.provider][storage.prefix];\n        if (list.length) {\n            loadNewIcons(storage, list);\n        }\n    });\n    return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon)=>{\n    return new Promise((fulfill, reject)=>{\n        const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n        if (!iconObj) {\n            reject(icon);\n            return;\n        }\n        loadIcons([\n            iconObj || icon\n        ], (loaded)=>{\n            if (loaded.length && iconObj) {\n                const data = getIconData(iconObj);\n                if (data) {\n                    fulfill({\n                        ...defaultIconProps,\n                        ...data\n                    });\n                    return;\n                }\n            }\n            reject(icon);\n        });\n    });\n};\nfunction setCustomIconsLoader(loader, prefix, provider) {\n    getStorage(provider || \"\", prefix).loadIcons = loader;\n}\nfunction setCustomIconLoader(loader, prefix, provider) {\n    getStorage(provider || \"\", prefix).loadIcon = loader;\n}\nfunction mergeCustomisations(defaults, item) {\n    const result = {\n        ...defaults\n    };\n    for(const key in item){\n        const value = item[key];\n        const valueType = typeof value;\n        if (key in defaultIconSizeCustomisations) {\n            if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n                result[key] = value;\n            }\n        } else if (valueType === typeof result[key]) {\n            result[key] = key === \"rotate\" ? value % 4 : value;\n        }\n    }\n    return result;\n}\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n    flip.split(separator).forEach((str)=>{\n        const value = str.trim();\n        switch(value){\n            case \"horizontal\":\n                custom.hFlip = true;\n                break;\n            case \"vertical\":\n                custom.vFlip = true;\n                break;\n        }\n    });\n}\nfunction rotateFromString(value) {\n    let defaultValue = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n    const units = value.replace(/^-?[0-9.]*/, \"\");\n    function cleanup(value2) {\n        while(value2 < 0){\n            value2 += 4;\n        }\n        return value2 % 4;\n    }\n    if (units === \"\") {\n        const num = parseInt(value);\n        return isNaN(num) ? 0 : cleanup(num);\n    } else if (units !== value) {\n        let split = 0;\n        switch(units){\n            case \"%\":\n                split = 25;\n                break;\n            case \"deg\":\n                split = 90;\n        }\n        if (split) {\n            let num = parseFloat(value.slice(0, value.length - units.length));\n            if (isNaN(num)) {\n                return 0;\n            }\n            num = num / split;\n            return num % 1 === 0 ? cleanup(num) : 0;\n        }\n    }\n    return defaultValue;\n}\nfunction iconToHTML(body, attributes) {\n    let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n    for(const attr in attributes){\n        renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n    }\n    return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\nfunction encodeSVGforURL(svg) {\n    return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n    return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n    return 'url(\"' + svgToData(svg) + '\")';\n}\nlet policy;\nfunction createPolicy() {\n    try {\n        policy = window.trustedTypes.createPolicy(\"iconify\", {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n            createHTML: (s)=>s\n        });\n    } catch (err) {\n        policy = null;\n    }\n}\nfunction cleanUpInnerHTML(html) {\n    if (policy === void 0) {\n        createPolicy();\n    }\n    return policy ? policy.createHTML(html) : html;\n}\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false\n};\n/**\n * Default SVG attributes\n */ const svgDefaults = {\n    \"xmlns\": \"http://www.w3.org/2000/svg\",\n    \"xmlnsXlink\": \"http://www.w3.org/1999/xlink\",\n    \"aria-hidden\": true,\n    \"role\": \"img\"\n};\n/**\n * Style modes\n */ const commonProps = {\n    display: \"inline-block\"\n};\nconst monotoneProps = {\n    backgroundColor: \"currentColor\"\n};\nconst coloredProps = {\n    backgroundColor: \"transparent\"\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: \"var(--svg)\",\n    Repeat: \"no-repeat\",\n    Size: \"100% 100%\"\n};\nconst propsToAddTo = {\n    WebkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps\n};\nfor(const prefix in propsToAddTo){\n    const list = propsToAddTo[prefix];\n    for(const prop in propsToAdd){\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Default values for customisations for inline icon\n */ const inlineDefaults = {\n    ...defaultExtendedIconCustomisations,\n    inline: true\n};\n/**\n * Fix size: add 'px' to numbers\n */ function fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? \"px\" : \"\");\n}\n/**\n * Render icon\n */ const render = (// Icon must be validated before calling this function\nicon, // Partial properties\nprops, // Icon name\nname)=>{\n    // Get default properties\n    const defaultProps = props.inline ? inlineDefaults : defaultExtendedIconCustomisations;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Check mode\n    const mode = props.mode || \"svg\";\n    // Create style\n    const style = {};\n    const customStyle = props.style || {};\n    // Create SVG component properties\n    const componentProps = {\n        ...mode === \"svg\" ? svgDefaults : {}\n    };\n    if (name) {\n        const iconName = stringToIcon(name, false, true);\n        if (iconName) {\n            const classNames = [\n                \"iconify\"\n            ];\n            const props = [\n                \"provider\",\n                \"prefix\"\n            ];\n            for (const prop of props){\n                if (iconName[prop]) {\n                    classNames.push(\"iconify--\" + iconName[prop]);\n                }\n            }\n            componentProps.className = classNames.join(\" \");\n        }\n    }\n    // Get element properties\n    for(let key in props){\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch(key){\n            // Properties to ignore\n            case \"icon\":\n            case \"style\":\n            case \"children\":\n            case \"onLoad\":\n            case \"mode\":\n            case \"ssr\":\n                break;\n            // Forward ref\n            case \"_ref\":\n                componentProps.ref = value;\n                break;\n            // Merge class names\n            case \"className\":\n                componentProps[key] = (componentProps[key] ? componentProps[key] + \" \" : \"\") + value;\n                break;\n            // Boolean attributes\n            case \"inline\":\n            case \"hFlip\":\n            case \"vFlip\":\n                customisations[key] = value === true || value === \"true\" || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case \"flip\":\n                if (typeof value === \"string\") {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case \"color\":\n                style.color = value;\n                break;\n            // Rotation as string\n            case \"rotate\":\n                if (typeof value === \"string\") {\n                    customisations[key] = rotateFromString(value);\n                } else if (typeof value === \"number\") {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case \"ariaHidden\":\n            case \"aria-hidden\":\n                if (value !== true && value !== \"true\") {\n                    delete componentProps[\"aria-hidden\"];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = \"-0.125em\";\n    }\n    if (mode === \"svg\") {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === \"string\") {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, \"_\");\n        }\n        // Add icon stuff\n        componentProps.dangerouslySetInnerHTML = {\n            __html: cleanUpInnerHTML(replaceIDs(item.body, id ? ()=>id + \"ID\" + localCounter++ : \"iconifyReact\"))\n        };\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === \"mask\" || (mode === \"bg\" ? false : body.indexOf(\"currentColor\") !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + \"\",\n        height: height + \"\"\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        \"--svg\": svgToURL(html),\n        \"width\": fixSize(renderAttribs.width),\n        \"height\": fixSize(renderAttribs.height),\n        ...commonProps,\n        ...useMask ? monotoneProps : coloredProps,\n        ...customStyle\n    };\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"span\", componentProps);\n};\n/**\n * Initialise stuff\n */ // Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule(\"\", fetchAPIModule);\n/**\n * Browser stuff\n */ if (typeof document !== \"undefined\" && typeof window !== \"undefined\") {\n    const _window = window;\n    // Load icons from global \"IconifyPreload\"\n    if (_window.IconifyPreload !== void 0) {\n        const preload = _window.IconifyPreload;\n        const err = \"Invalid IconifyPreload syntax.\";\n        if (typeof preload === \"object\" && preload !== null) {\n            (preload instanceof Array ? preload : [\n                preload\n            ]).forEach((item)=>{\n                try {\n                    if (// Check if item is an object and not null/array\n                    typeof item !== \"object\" || item === null || item instanceof Array || // Check for 'icons' and 'prefix'\n                    typeof item.icons !== \"object\" || typeof item.prefix !== \"string\" || // Add icon set\n                    !addCollection(item)) {\n                        console.error(err);\n                    }\n                } catch (e) {\n                    console.error(err);\n                }\n            });\n        }\n    }\n    // Set API from global \"IconifyProviders\"\n    if (_window.IconifyProviders !== void 0) {\n        const providers = _window.IconifyProviders;\n        if (typeof providers === \"object\" && providers !== null) {\n            for(let key in providers){\n                const err = \"IconifyProviders[\" + key + \"] is invalid.\";\n                try {\n                    const value = providers[key];\n                    if (typeof value !== \"object\" || !value || value.resources === void 0) {\n                        continue;\n                    }\n                    if (!addAPIProvider(key, value)) {\n                        console.error(err);\n                    }\n                } catch (e) {\n                    console.error(err);\n                }\n            }\n        }\n    }\n}\nfunction IconComponent(props) {\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!!props.ssr);\n    const [abort, setAbort] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Get initial state\n    function getInitialState(mounted) {\n        if (mounted) {\n            const name = props.icon;\n            if (typeof name === \"object\") {\n                // Icon as object\n                return {\n                    name: \"\",\n                    data: name\n                };\n            }\n            const data = getIconData(name);\n            if (data) {\n                return {\n                    name,\n                    data\n                };\n            }\n        }\n        return {\n            name: \"\"\n        };\n    }\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState(!!props.ssr));\n    // Cancel loading\n    function cleanup() {\n        const callback = abort.callback;\n        if (callback) {\n            callback();\n            setAbort({});\n        }\n    }\n    // Change state if it is different\n    function changeState(newState) {\n        if (JSON.stringify(state) !== JSON.stringify(newState)) {\n            cleanup();\n            setState(newState);\n            return true;\n        }\n    }\n    // Update state\n    function updateState() {\n        var _a;\n        const name = props.icon;\n        if (typeof name === \"object\") {\n            // Icon as object\n            changeState({\n                name: \"\",\n                data: name\n            });\n            return;\n        }\n        // New icon or got icon data\n        const data = getIconData(name);\n        if (changeState({\n            name,\n            data\n        })) {\n            if (data === undefined) {\n                // Load icon, update state when done\n                const callback = loadIcons([\n                    name\n                ], updateState);\n                setAbort({\n                    callback\n                });\n            } else if (data) {\n                // Icon data is available: trigger onLoad callback if present\n                (_a = props.onLoad) === null || _a === void 0 ? void 0 : _a.call(props, name);\n            }\n        }\n    }\n    // Mounted state, cleanup for loader\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setMounted(true);\n        return cleanup;\n    }, []);\n    // Icon changed or component mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (mounted) {\n            updateState();\n        }\n    }, [\n        props.icon,\n        mounted\n    ]);\n    // Render icon\n    const { name, data } = state;\n    if (!data) {\n        return props.children ? props.children : props.fallback ? props.fallback : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"span\", {});\n    }\n    return render({\n        ...defaultIconProps,\n        ...data\n    }, props, name);\n}\n_s(IconComponent, \"n8uZtuOaCY0uD/laG8xRsp/lkB8=\");\n_c = IconComponent;\n/**\n * Block icon\n *\n * @param props - Component properties\n */ const Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c1 = (props, ref)=>IconComponent({\n        ...props,\n        _ref: ref\n    }));\n_c2 = Icon;\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */ const InlineIcon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c3 = (props, ref)=>IconComponent({\n        inline: true,\n        ...props,\n        _ref: ref\n    }));\n_c4 = InlineIcon;\n/**\n * Internal API\n */ const _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders\n};\n\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"IconComponent\");\n$RefreshReg$(_c1, \"Icon$forwardRef\");\n$RefreshReg$(_c2, \"Icon\");\n$RefreshReg$(_c3, \"InlineIcon$forwardRef\");\n$RefreshReg$(_c4, \"InlineIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@iconify/react/dist/iconify.js\n"));

/***/ })

});