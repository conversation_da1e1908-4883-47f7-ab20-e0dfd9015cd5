"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/toolbar.tsx":
/*!****************************************************!*\
  !*** ./src/features/editor/components/toolbar.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toolbar: function() { return /* binding */ Toolbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaBold,FaItalic,FaStrikethrough,FaUnderline!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbColorFilter_react_icons_tb__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=TbColorFilter!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsBorderWidth_react_icons_bs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BsBorderWidth!=!react-icons/bs */ \"(app-pages-browser)/./node_modules/react-icons/bs/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RxTransparencyGrid_react_icons_rx__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=RxTransparencyGrid!=!react-icons/rx */ \"(app-pages-browser)/./node_modules/react-icons/rx/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-center.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-split-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,Settings,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_components_font_size_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/font-size-input */ \"(app-pages-browser)/./src/features/editor/components/font-size-input.tsx\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_hint__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hint */ \"(app-pages-browser)/./src/components/hint.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst Toolbar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    var _editor_selectedObjects_;\n    _s();\n    const initialFillColor = editor === null || editor === void 0 ? void 0 : editor.getActiveFillColor();\n    const initialStrokeColor = editor === null || editor === void 0 ? void 0 : editor.getActiveStrokeColor();\n    const initialFontFamily = editor === null || editor === void 0 ? void 0 : editor.getActiveFontFamily();\n    const initialFontWeight = (editor === null || editor === void 0 ? void 0 : editor.getActiveFontWeight()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_4__.FONT_WEIGHT;\n    const initialFontStyle = editor === null || editor === void 0 ? void 0 : editor.getActiveFontStyle();\n    const initialFontLinethrough = editor === null || editor === void 0 ? void 0 : editor.getActiveFontLinethrough();\n    const initialFontUnderline = editor === null || editor === void 0 ? void 0 : editor.getActiveFontUnderline();\n    const initialTextAlign = editor === null || editor === void 0 ? void 0 : editor.getActiveTextAlign();\n    const initialFontSize = (editor === null || editor === void 0 ? void 0 : editor.getActiveFontSize()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_4__.FONT_SIZE;\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fillColor: initialFillColor,\n        strokeColor: initialStrokeColor,\n        fontFamily: initialFontFamily,\n        fontWeight: initialFontWeight,\n        fontStyle: initialFontStyle,\n        fontLinethrough: initialFontLinethrough,\n        fontUnderline: initialFontUnderline,\n        textAlign: initialTextAlign,\n        fontSize: initialFontSize\n    });\n    const selectedObject = editor === null || editor === void 0 ? void 0 : editor.selectedObjects[0];\n    const selectedObjectType = editor === null || editor === void 0 ? void 0 : (_editor_selectedObjects_ = editor.selectedObjects[0]) === null || _editor_selectedObjects_ === void 0 ? void 0 : _editor_selectedObjects_.type;\n    const isText = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_2__.isTextType)(selectedObjectType);\n    const isImage = selectedObjectType === \"image\";\n    const isIcon = selectedObjectType === \"icon\";\n    const onChangeFontSize = (value)=>{\n        if (!selectedObject) {\n            return;\n        }\n        editor === null || editor === void 0 ? void 0 : editor.changeFontSize(value);\n        setProperties((current)=>({\n                ...current,\n                fontSize: value\n            }));\n    };\n    const onChangeTextAlign = (value)=>{\n        if (!selectedObject) {\n            return;\n        }\n        editor === null || editor === void 0 ? void 0 : editor.changeTextAlign(value);\n        setProperties((current)=>({\n                ...current,\n                textAlign: value\n            }));\n    };\n    const toggleBold = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const newValue = properties.fontWeight > 500 ? 500 : 700;\n        editor === null || editor === void 0 ? void 0 : editor.changeFontWeight(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontWeight: newValue\n            }));\n    };\n    const toggleItalic = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const isItalic = properties.fontStyle === \"italic\";\n        const newValue = isItalic ? \"normal\" : \"italic\";\n        editor === null || editor === void 0 ? void 0 : editor.changeFontStyle(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontStyle: newValue\n            }));\n    };\n    const toggleLinethrough = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const newValue = properties.fontLinethrough ? false : true;\n        editor === null || editor === void 0 ? void 0 : editor.changeFontLinethrough(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontLinethrough: newValue\n            }));\n    };\n    const toggleUnderline = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const newValue = properties.fontUnderline ? false : true;\n        editor === null || editor === void 0 ? void 0 : editor.changeFontUnderline(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontUnderline: newValue\n            }));\n    };\n    if ((editor === null || editor === void 0 ? void 0 : editor.selectedObjects.length) === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"shrink-0 h-[56px] border-b bg-white w-full flex items-center overflow-x-auto z-[49] p-2 gap-x-2\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"shrink-0 h-[56px] border-b bg-white w-full flex items-center overflow-x-auto z-[49] p-2 gap-x-2\",\n        children: [\n            !isImage && !isIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Color\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"fill\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"fill\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-sm size-4 border\",\n                            style: {\n                                backgroundColor: properties.fillColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            isIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Icon Color\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"stroke-color\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"stroke-color\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-sm size-4 border-2 bg-white\",\n                            style: {\n                                borderColor: properties.strokeColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, undefined),\n            !isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Stroke color\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"stroke-color\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"stroke-color\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-sm size-4 border-2 bg-white\",\n                            style: {\n                                borderColor: properties.strokeColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, undefined),\n            !isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Stroke width\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"stroke-width\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"stroke-width\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsBorderWidth_react_icons_bs__WEBPACK_IMPORTED_MODULE_8__.BsBorderWidth, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, undefined),\n            isIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Icon Settings\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"icon-settings\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"icon-settings\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Font\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"font\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-auto px-2 text-sm\", activeTool === \"font\" && \"bg-gray-100\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[100px] truncate\",\n                                children: properties.fontFamily\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"size-4 ml-2 shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Bold\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleBold,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontWeight > 500 && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaBold, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Italic\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleItalic,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontStyle === \"italic\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaItalic, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Underline\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleUnderline,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontUnderline && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaUnderline, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Strike\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleLinethrough,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontLinethrough && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaStrikethrough, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Align left\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeTextAlign(\"left\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.textAlign === \"left\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Align center\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeTextAlign(\"center\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.textAlign === \"center\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Align right\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeTextAlign(\"right\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.textAlign === \"right\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 373,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_size_input__WEBPACK_IMPORTED_MODULE_3__.FontSizeInput, {\n                    value: properties.fontSize,\n                    onChange: onChangeFontSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 10\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 389,\n                columnNumber: 9\n            }, undefined),\n            isImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Filters\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"filter\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"filter\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbColorFilter_react_icons_tb__WEBPACK_IMPORTED_MODULE_15__.TbColorFilter, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, undefined),\n            isImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Remove background\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"remove-bg\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"remove-bg\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Bring forward\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.bringForward(),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Send backwards\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.sendBackwards(),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Opacity\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"opacity\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"opacity\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RxTransparencyGrid_react_icons_rx__WEBPACK_IMPORTED_MODULE_19__.RxTransparencyGrid, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Duplicate\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>{\n                            editor === null || editor === void 0 ? void 0 : editor.onCopy();\n                            editor === null || editor === void 0 ? void 0 : editor.onPaste();\n                        },\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 462,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Delete\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.delete(),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: \"text-red-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_Settings_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Toolbar, \"DmUe6JEyxArHMufs3mALpXmlU1U=\");\n_c = Toolbar;\nvar _c;\n$RefreshReg$(_c, \"Toolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/toolbar.tsx\n"));

/***/ })

});