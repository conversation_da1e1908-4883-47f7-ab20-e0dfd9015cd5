"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@iconify";
exports.ids = ["vendor-chunks/@iconify"];
exports.modules = {

/***/ "(ssr)/./node_modules/@iconify/react/dist/iconify.js":
/*!*****************************************************!*\
  !*** ./node_modules/@iconify/react/dist/iconify.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   InlineIcon: () => (/* binding */ InlineIcon),\n/* harmony export */   _api: () => (/* binding */ _api),\n/* harmony export */   addAPIProvider: () => (/* binding */ addAPIProvider),\n/* harmony export */   addCollection: () => (/* binding */ addCollection),\n/* harmony export */   addIcon: () => (/* binding */ addIcon),\n/* harmony export */   buildIcon: () => (/* binding */ iconToSVG),\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize),\n/* harmony export */   getIcon: () => (/* binding */ getIcon),\n/* harmony export */   iconLoaded: () => (/* binding */ iconLoaded),\n/* harmony export */   listIcons: () => (/* binding */ listIcons),\n/* harmony export */   loadIcon: () => (/* binding */ loadIcon),\n/* harmony export */   loadIcons: () => (/* binding */ loadIcons),\n/* harmony export */   replaceIDs: () => (/* binding */ replaceIDs),\n/* harmony export */   setCustomIconLoader: () => (/* binding */ setCustomIconLoader),\n/* harmony export */   setCustomIconsLoader: () => (/* binding */ setCustomIconsLoader)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Icon,InlineIcon,_api,addAPIProvider,addCollection,addIcon,buildIcon,calculateSize,getIcon,iconLoaded,listIcons,loadIcon,loadIcons,replaceIDs,setCustomIconLoader,setCustomIconsLoader auto */ \nconst defaultIconDimensions = Object.freeze({\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n});\nconst defaultIconTransformations = Object.freeze({\n    rotate: 0,\n    vFlip: false,\n    hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n    ...defaultIconDimensions,\n    ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n    ...defaultIconProps,\n    body: \"\",\n    hidden: false\n});\nfunction mergeIconTransformations(obj1, obj2) {\n    const result = {};\n    if (!obj1.hFlip !== !obj2.hFlip) {\n        result.hFlip = true;\n    }\n    if (!obj1.vFlip !== !obj2.vFlip) {\n        result.vFlip = true;\n    }\n    const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n    if (rotate) {\n        result.rotate = rotate;\n    }\n    return result;\n}\nfunction mergeIconData(parent, child) {\n    const result = mergeIconTransformations(parent, child);\n    for(const key in defaultExtendedIconProps){\n        if (key in defaultIconTransformations) {\n            if (key in parent && !(key in result)) {\n                result[key] = defaultIconTransformations[key];\n            }\n        } else if (key in child) {\n            result[key] = child[key];\n        } else if (key in parent) {\n            result[key] = parent[key];\n        }\n    }\n    return result;\n}\nfunction getIconsTree(data, names) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    const resolved = /* @__PURE__ */ Object.create(null);\n    function resolve(name) {\n        if (icons[name]) {\n            return resolved[name] = [];\n        }\n        if (!(name in resolved)) {\n            resolved[name] = null;\n            const parent = aliases[name] && aliases[name].parent;\n            const value = parent && resolve(parent);\n            if (value) {\n                resolved[name] = [\n                    parent\n                ].concat(value);\n            }\n        }\n        return resolved[name];\n    }\n    Object.keys(icons).concat(Object.keys(aliases)).forEach(resolve);\n    return resolved;\n}\nfunction internalGetIconData(data, name, tree) {\n    const icons = data.icons;\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    let currentProps = {};\n    function parse(name2) {\n        currentProps = mergeIconData(icons[name2] || aliases[name2], currentProps);\n    }\n    parse(name);\n    tree.forEach(parse);\n    return mergeIconData(data, currentProps);\n}\nfunction parseIconSet(data, callback) {\n    const names = [];\n    if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n        return names;\n    }\n    if (data.not_found instanceof Array) {\n        data.not_found.forEach((name)=>{\n            callback(name, null);\n            names.push(name);\n        });\n    }\n    const tree = getIconsTree(data);\n    for(const name in tree){\n        const item = tree[name];\n        if (item) {\n            callback(name, internalGetIconData(data, name, item));\n            names.push(name);\n        }\n    }\n    return names;\n}\nconst optionalPropertyDefaults = {\n    provider: \"\",\n    aliases: {},\n    not_found: {},\n    ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n    for(const prop in defaults){\n        if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction quicklyValidateIconSet(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n        return null;\n    }\n    const data = obj;\n    if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n        return null;\n    }\n    if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n        return null;\n    }\n    const icons = data.icons;\n    for(const name in icons){\n        const icon = icons[name];\n        if (// Name cannot be empty\n        !name || // Must have body\n        typeof icon.body !== \"string\" || // Check other props\n        !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n    for(const name in aliases){\n        const icon = aliases[name];\n        const parent = icon.parent;\n        if (// Name cannot be empty\n        !name || // Parent must be set and point to existing icon\n        typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n        !checkOptionalProps(icon, defaultExtendedIconProps)) {\n            return null;\n        }\n    }\n    return data;\n}\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\")=>{\n    const colonSeparated = value.split(\":\");\n    if (value.slice(0, 1) === \"@\") {\n        if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n            return null;\n        }\n        provider = colonSeparated.shift().slice(1);\n    }\n    if (colonSeparated.length > 3 || !colonSeparated.length) {\n        return null;\n    }\n    if (colonSeparated.length > 1) {\n        const name2 = colonSeparated.pop();\n        const prefix = colonSeparated.pop();\n        const result = {\n            // Allow provider without '@': \"provider:prefix:name\"\n            provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n            prefix,\n            name: name2\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    const name = colonSeparated[0];\n    const dashSeparated = name.split(\"-\");\n    if (dashSeparated.length > 1) {\n        const result = {\n            provider,\n            prefix: dashSeparated.shift(),\n            name: dashSeparated.join(\"-\")\n        };\n        return validate && !validateIconName(result) ? null : result;\n    }\n    if (allowSimpleName && provider === \"\") {\n        const result = {\n            provider,\n            prefix: \"\",\n            name\n        };\n        return validate && !validateIconName(result, allowSimpleName) ? null : result;\n    }\n    return null;\n};\nconst validateIconName = (icon, allowSimpleName)=>{\n    if (!icon) {\n        return false;\n    }\n    return !!// Check name: cannot be empty\n    ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\nconst dataStorage = /* @__PURE__ */ Object.create(null);\nfunction newStorage(provider, prefix) {\n    return {\n        provider,\n        prefix,\n        icons: /* @__PURE__ */ Object.create(null),\n        missing: /* @__PURE__ */ new Set()\n    };\n}\nfunction getStorage(provider, prefix) {\n    const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));\n    return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));\n}\nfunction addIconSet(storage, data) {\n    if (!quicklyValidateIconSet(data)) {\n        return [];\n    }\n    return parseIconSet(data, (name, icon)=>{\n        if (icon) {\n            storage.icons[name] = icon;\n        } else {\n            storage.missing.add(name);\n        }\n    });\n}\nfunction addIconToStorage(storage, name, icon) {\n    try {\n        if (typeof icon.body === \"string\") {\n            storage.icons[name] = {\n                ...icon\n            };\n            return true;\n        }\n    } catch (err) {}\n    return false;\n}\nfunction listIcons(provider, prefix) {\n    let allIcons = [];\n    const providers = typeof provider === \"string\" ? [\n        provider\n    ] : Object.keys(dataStorage);\n    providers.forEach((provider2)=>{\n        const prefixes = typeof provider2 === \"string\" && typeof prefix === \"string\" ? [\n            prefix\n        ] : Object.keys(dataStorage[provider2] || {});\n        prefixes.forEach((prefix2)=>{\n            const storage = getStorage(provider2, prefix2);\n            allIcons = allIcons.concat(Object.keys(storage.icons).map((name)=>(provider2 !== \"\" ? \"@\" + provider2 + \":\" : \"\") + prefix2 + \":\" + name));\n        });\n    });\n    return allIcons;\n}\nlet simpleNames = false;\nfunction allowSimpleNames(allow) {\n    if (typeof allow === \"boolean\") {\n        simpleNames = allow;\n    }\n    return simpleNames;\n}\nfunction getIconData(name) {\n    const icon = typeof name === \"string\" ? stringToIcon(name, true, simpleNames) : name;\n    if (icon) {\n        const storage = getStorage(icon.provider, icon.prefix);\n        const iconName = icon.name;\n        return storage.icons[iconName] || (storage.missing.has(iconName) ? null : void 0);\n    }\n}\nfunction addIcon(name, data) {\n    const icon = stringToIcon(name, true, simpleNames);\n    if (!icon) {\n        return false;\n    }\n    const storage = getStorage(icon.provider, icon.prefix);\n    if (data) {\n        return addIconToStorage(storage, icon.name, data);\n    } else {\n        storage.missing.add(icon.name);\n        return true;\n    }\n}\nfunction addCollection(data, provider) {\n    if (typeof data !== \"object\") {\n        return false;\n    }\n    if (typeof provider !== \"string\") {\n        provider = data.provider || \"\";\n    }\n    if (simpleNames && !provider && !data.prefix) {\n        let added = false;\n        if (quicklyValidateIconSet(data)) {\n            data.prefix = \"\";\n            parseIconSet(data, (name, icon)=>{\n                if (addIcon(name, icon)) {\n                    added = true;\n                }\n            });\n        }\n        return added;\n    }\n    const prefix = data.prefix;\n    if (!validateIconName({\n        prefix,\n        name: \"a\"\n    })) {\n        return false;\n    }\n    const storage = getStorage(provider, prefix);\n    return !!addIconSet(storage, data);\n}\nfunction iconLoaded(name) {\n    return !!getIconData(name);\n}\nfunction getIcon(name) {\n    const result = getIconData(name);\n    return result ? {\n        ...defaultIconProps,\n        ...result\n    } : result;\n}\nconst defaultIconSizeCustomisations = Object.freeze({\n    width: null,\n    height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n    // Dimensions\n    ...defaultIconSizeCustomisations,\n    // Transformations\n    ...defaultIconTransformations\n});\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n    if (ratio === 1) {\n        return size;\n    }\n    precision = precision || 100;\n    if (typeof size === \"number\") {\n        return Math.ceil(size * ratio * precision) / precision;\n    }\n    if (typeof size !== \"string\") {\n        return size;\n    }\n    const oldParts = size.split(unitsSplit);\n    if (oldParts === null || !oldParts.length) {\n        return size;\n    }\n    const newParts = [];\n    let code = oldParts.shift();\n    let isNumber = unitsTest.test(code);\n    while(true){\n        if (isNumber) {\n            const num = parseFloat(code);\n            if (isNaN(num)) {\n                newParts.push(code);\n            } else {\n                newParts.push(Math.ceil(num * ratio * precision) / precision);\n            }\n        } else {\n            newParts.push(code);\n        }\n        code = oldParts.shift();\n        if (code === void 0) {\n            return newParts.join(\"\");\n        }\n        isNumber = !isNumber;\n    }\n}\nfunction splitSVGDefs(content, tag = \"defs\") {\n    let defs = \"\";\n    const index = content.indexOf(\"<\" + tag);\n    while(index >= 0){\n        const start = content.indexOf(\">\", index);\n        const end = content.indexOf(\"</\" + tag);\n        if (start === -1 || end === -1) {\n            break;\n        }\n        const endEnd = content.indexOf(\">\", end);\n        if (endEnd === -1) {\n            break;\n        }\n        defs += content.slice(start + 1, end).trim();\n        content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n    }\n    return {\n        defs,\n        content\n    };\n}\nfunction mergeDefsAndContent(defs, content) {\n    return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n    const split = splitSVGDefs(body);\n    return mergeDefsAndContent(split.defs, start + split.content + end);\n}\nconst isUnsetKeyword = (value)=>value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n    const fullIcon = {\n        ...defaultIconProps,\n        ...icon\n    };\n    const fullCustomisations = {\n        ...defaultIconCustomisations,\n        ...customisations\n    };\n    const box = {\n        left: fullIcon.left,\n        top: fullIcon.top,\n        width: fullIcon.width,\n        height: fullIcon.height\n    };\n    let body = fullIcon.body;\n    [\n        fullIcon,\n        fullCustomisations\n    ].forEach((props)=>{\n        const transformations = [];\n        const hFlip = props.hFlip;\n        const vFlip = props.vFlip;\n        let rotation = props.rotate;\n        if (hFlip) {\n            if (vFlip) {\n                rotation += 2;\n            } else {\n                transformations.push(\"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\");\n                transformations.push(\"scale(-1 1)\");\n                box.top = box.left = 0;\n            }\n        } else if (vFlip) {\n            transformations.push(\"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\");\n            transformations.push(\"scale(1 -1)\");\n            box.top = box.left = 0;\n        }\n        let tempValue;\n        if (rotation < 0) {\n            rotation -= Math.floor(rotation / 4) * 4;\n        }\n        rotation = rotation % 4;\n        switch(rotation){\n            case 1:\n                tempValue = box.height / 2 + box.top;\n                transformations.unshift(\"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n            case 2:\n                transformations.unshift(\"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\");\n                break;\n            case 3:\n                tempValue = box.width / 2 + box.left;\n                transformations.unshift(\"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\");\n                break;\n        }\n        if (rotation % 2 === 1) {\n            if (box.left !== box.top) {\n                tempValue = box.left;\n                box.left = box.top;\n                box.top = tempValue;\n            }\n            if (box.width !== box.height) {\n                tempValue = box.width;\n                box.width = box.height;\n                box.height = tempValue;\n            }\n        }\n        if (transformations.length) {\n            body = wrapSVGContent(body, '<g transform=\"' + transformations.join(\" \") + '\">', \"</g>\");\n        }\n    });\n    const customisationsWidth = fullCustomisations.width;\n    const customisationsHeight = fullCustomisations.height;\n    const boxWidth = box.width;\n    const boxHeight = box.height;\n    let width;\n    let height;\n    if (customisationsWidth === null) {\n        height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n        width = calculateSize(height, boxWidth / boxHeight);\n    } else {\n        width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n        height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    }\n    const attributes = {};\n    const setAttr = (prop, value)=>{\n        if (!isUnsetKeyword(value)) {\n            attributes[prop] = value.toString();\n        }\n    };\n    setAttr(\"width\", width);\n    setAttr(\"height\", height);\n    const viewBox = [\n        box.left,\n        box.top,\n        boxWidth,\n        boxHeight\n    ];\n    attributes.viewBox = viewBox.join(\" \");\n    return {\n        attributes,\n        viewBox,\n        body\n    };\n}\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n    const ids = [];\n    let match;\n    while(match = regex.exec(body)){\n        ids.push(match[1]);\n    }\n    if (!ids.length) {\n        return body;\n    }\n    const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n    ids.forEach((id)=>{\n        const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n        const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n        body = body.replace(// Allowed characters before id: [#;\"]\n        // Allowed characters after id: [)\"], .[a-z]\n        new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"), \"$1\" + newID + suffix + \"$3\");\n    });\n    body = body.replace(new RegExp(suffix, \"g\"), \"\");\n    return body;\n}\nconst storage = /* @__PURE__ */ Object.create(null);\nfunction setAPIModule(provider, item) {\n    storage[provider] = item;\n}\nfunction getAPIModule(provider) {\n    return storage[provider] || storage[\"\"];\n}\nfunction createAPIConfig(source) {\n    let resources;\n    if (typeof source.resources === \"string\") {\n        resources = [\n            source.resources\n        ];\n    } else {\n        resources = source.resources;\n        if (!(resources instanceof Array) || !resources.length) {\n            return null;\n        }\n    }\n    const result = {\n        // API hosts\n        resources,\n        // Root path\n        path: source.path || \"/\",\n        // URL length limit\n        maxURL: source.maxURL || 500,\n        // Timeout before next host is used.\n        rotate: source.rotate || 750,\n        // Timeout before failing query.\n        timeout: source.timeout || 5e3,\n        // Randomise default API end point.\n        random: source.random === true,\n        // Start index\n        index: source.index || 0,\n        // Receive data after time out (used if time out kicks in first, then API module sends data anyway).\n        dataAfterTimeout: source.dataAfterTimeout !== false\n    };\n    return result;\n}\nconst configStorage = /* @__PURE__ */ Object.create(null);\nconst fallBackAPISources = [\n    \"https://api.simplesvg.com\",\n    \"https://api.unisvg.com\"\n];\nconst fallBackAPI = [];\nwhile(fallBackAPISources.length > 0){\n    if (fallBackAPISources.length === 1) {\n        fallBackAPI.push(fallBackAPISources.shift());\n    } else {\n        if (Math.random() > 0.5) {\n            fallBackAPI.push(fallBackAPISources.shift());\n        } else {\n            fallBackAPI.push(fallBackAPISources.pop());\n        }\n    }\n}\nconfigStorage[\"\"] = createAPIConfig({\n    resources: [\n        \"https://api.iconify.design\"\n    ].concat(fallBackAPI)\n});\nfunction addAPIProvider(provider, customConfig) {\n    const config = createAPIConfig(customConfig);\n    if (config === null) {\n        return false;\n    }\n    configStorage[provider] = config;\n    return true;\n}\nfunction getAPIConfig(provider) {\n    return configStorage[provider];\n}\nfunction listAPIProviders() {\n    return Object.keys(configStorage);\n}\nconst detectFetch = ()=>{\n    let callback;\n    try {\n        callback = fetch;\n        if (typeof callback === \"function\") {\n            return callback;\n        }\n    } catch (err) {}\n};\nlet fetchModule = detectFetch();\nfunction setFetch(fetch2) {\n    fetchModule = fetch2;\n}\nfunction getFetch() {\n    return fetchModule;\n}\nfunction calculateMaxLength(provider, prefix) {\n    const config = getAPIConfig(provider);\n    if (!config) {\n        return 0;\n    }\n    let result;\n    if (!config.maxURL) {\n        result = 0;\n    } else {\n        let maxHostLength = 0;\n        config.resources.forEach((item)=>{\n            const host = item;\n            maxHostLength = Math.max(maxHostLength, host.length);\n        });\n        const url = prefix + \".json?icons=\";\n        result = config.maxURL - maxHostLength - config.path.length - url.length;\n    }\n    return result;\n}\nfunction shouldAbort(status) {\n    return status === 404;\n}\nconst prepare = (provider, prefix, icons)=>{\n    const results = [];\n    const maxLength = calculateMaxLength(provider, prefix);\n    const type = \"icons\";\n    let item = {\n        type,\n        provider,\n        prefix,\n        icons: []\n    };\n    let length = 0;\n    icons.forEach((name, index)=>{\n        length += name.length + 1;\n        if (length >= maxLength && index > 0) {\n            results.push(item);\n            item = {\n                type,\n                provider,\n                prefix,\n                icons: []\n            };\n            length = name.length;\n        }\n        item.icons.push(name);\n    });\n    results.push(item);\n    return results;\n};\nfunction getPath(provider) {\n    if (typeof provider === \"string\") {\n        const config = getAPIConfig(provider);\n        if (config) {\n            return config.path;\n        }\n    }\n    return \"/\";\n}\nconst send = (host, params, callback)=>{\n    if (!fetchModule) {\n        callback(\"abort\", 424);\n        return;\n    }\n    let path = getPath(params.provider);\n    switch(params.type){\n        case \"icons\":\n            {\n                const prefix = params.prefix;\n                const icons = params.icons;\n                const iconsList = icons.join(\",\");\n                const urlParams = new URLSearchParams({\n                    icons: iconsList\n                });\n                path += prefix + \".json?\" + urlParams.toString();\n                break;\n            }\n        case \"custom\":\n            {\n                const uri = params.uri;\n                path += uri.slice(0, 1) === \"/\" ? uri.slice(1) : uri;\n                break;\n            }\n        default:\n            callback(\"abort\", 400);\n            return;\n    }\n    let defaultError = 503;\n    fetchModule(host + path).then((response)=>{\n        const status = response.status;\n        if (status !== 200) {\n            setTimeout(()=>{\n                callback(shouldAbort(status) ? \"abort\" : \"next\", status);\n            });\n            return;\n        }\n        defaultError = 501;\n        return response.json();\n    }).then((data)=>{\n        if (typeof data !== \"object\" || data === null) {\n            setTimeout(()=>{\n                if (data === 404) {\n                    callback(\"abort\", data);\n                } else {\n                    callback(\"next\", defaultError);\n                }\n            });\n            return;\n        }\n        setTimeout(()=>{\n            callback(\"success\", data);\n        });\n    }).catch(()=>{\n        callback(\"next\", defaultError);\n    });\n};\nconst fetchAPIModule = {\n    prepare,\n    send\n};\nfunction sortIcons(icons) {\n    const result = {\n        loaded: [],\n        missing: [],\n        pending: []\n    };\n    const storage = /* @__PURE__ */ Object.create(null);\n    icons.sort((a, b)=>{\n        if (a.provider !== b.provider) {\n            return a.provider.localeCompare(b.provider);\n        }\n        if (a.prefix !== b.prefix) {\n            return a.prefix.localeCompare(b.prefix);\n        }\n        return a.name.localeCompare(b.name);\n    });\n    let lastIcon = {\n        provider: \"\",\n        prefix: \"\",\n        name: \"\"\n    };\n    icons.forEach((icon)=>{\n        if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {\n            return;\n        }\n        lastIcon = icon;\n        const provider = icon.provider;\n        const prefix = icon.prefix;\n        const name = icon.name;\n        const providerStorage = storage[provider] || (storage[provider] = /* @__PURE__ */ Object.create(null));\n        const localStorage = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));\n        let list;\n        if (name in localStorage.icons) {\n            list = result.loaded;\n        } else if (prefix === \"\" || localStorage.missing.has(name)) {\n            list = result.missing;\n        } else {\n            list = result.pending;\n        }\n        const item = {\n            provider,\n            prefix,\n            name\n        };\n        list.push(item);\n    });\n    return result;\n}\nfunction removeCallback(storages, id) {\n    storages.forEach((storage)=>{\n        const items = storage.loaderCallbacks;\n        if (items) {\n            storage.loaderCallbacks = items.filter((row)=>row.id !== id);\n        }\n    });\n}\nfunction updateCallbacks(storage) {\n    if (!storage.pendingCallbacksFlag) {\n        storage.pendingCallbacksFlag = true;\n        setTimeout(()=>{\n            storage.pendingCallbacksFlag = false;\n            const items = storage.loaderCallbacks ? storage.loaderCallbacks.slice(0) : [];\n            if (!items.length) {\n                return;\n            }\n            let hasPending = false;\n            const provider = storage.provider;\n            const prefix = storage.prefix;\n            items.forEach((item)=>{\n                const icons = item.icons;\n                const oldLength = icons.pending.length;\n                icons.pending = icons.pending.filter((icon)=>{\n                    if (icon.prefix !== prefix) {\n                        return true;\n                    }\n                    const name = icon.name;\n                    if (storage.icons[name]) {\n                        icons.loaded.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else if (storage.missing.has(name)) {\n                        icons.missing.push({\n                            provider,\n                            prefix,\n                            name\n                        });\n                    } else {\n                        hasPending = true;\n                        return true;\n                    }\n                    return false;\n                });\n                if (icons.pending.length !== oldLength) {\n                    if (!hasPending) {\n                        removeCallback([\n                            storage\n                        ], item.id);\n                    }\n                    item.callback(icons.loaded.slice(0), icons.missing.slice(0), icons.pending.slice(0), item.abort);\n                }\n            });\n        });\n    }\n}\nlet idCounter = 0;\nfunction storeCallback(callback, icons, pendingSources) {\n    const id = idCounter++;\n    const abort = removeCallback.bind(null, pendingSources, id);\n    if (!icons.pending.length) {\n        return abort;\n    }\n    const item = {\n        id,\n        icons,\n        callback,\n        abort\n    };\n    pendingSources.forEach((storage)=>{\n        (storage.loaderCallbacks || (storage.loaderCallbacks = [])).push(item);\n    });\n    return abort;\n}\nfunction listToIcons(list, validate = true, simpleNames = false) {\n    const result = [];\n    list.forEach((item)=>{\n        const icon = typeof item === \"string\" ? stringToIcon(item, validate, simpleNames) : item;\n        if (icon) {\n            result.push(icon);\n        }\n    });\n    return result;\n}\n// src/config.ts\nvar defaultConfig = {\n    resources: [],\n    index: 0,\n    timeout: 2e3,\n    rotate: 750,\n    random: false,\n    dataAfterTimeout: false\n};\n// src/query.ts\nfunction sendQuery(config, payload, query, done) {\n    const resourcesCount = config.resources.length;\n    const startIndex = config.random ? Math.floor(Math.random() * resourcesCount) : config.index;\n    let resources;\n    if (config.random) {\n        let list = config.resources.slice(0);\n        resources = [];\n        while(list.length > 1){\n            const nextIndex = Math.floor(Math.random() * list.length);\n            resources.push(list[nextIndex]);\n            list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));\n        }\n        resources = resources.concat(list);\n    } else {\n        resources = config.resources.slice(startIndex).concat(config.resources.slice(0, startIndex));\n    }\n    const startTime = Date.now();\n    let status = \"pending\";\n    let queriesSent = 0;\n    let lastError;\n    let timer = null;\n    let queue = [];\n    let doneCallbacks = [];\n    if (typeof done === \"function\") {\n        doneCallbacks.push(done);\n    }\n    function resetTimer() {\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n        }\n    }\n    function abort() {\n        if (status === \"pending\") {\n            status = \"aborted\";\n        }\n        resetTimer();\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function subscribe(callback, overwrite) {\n        if (overwrite) {\n            doneCallbacks = [];\n        }\n        if (typeof callback === \"function\") {\n            doneCallbacks.push(callback);\n        }\n    }\n    function getQueryStatus() {\n        return {\n            startTime,\n            payload,\n            status,\n            queriesSent,\n            queriesPending: queue.length,\n            subscribe,\n            abort\n        };\n    }\n    function failQuery() {\n        status = \"failed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(void 0, lastError);\n        });\n    }\n    function clearQueue() {\n        queue.forEach((item)=>{\n            if (item.status === \"pending\") {\n                item.status = \"aborted\";\n            }\n        });\n        queue = [];\n    }\n    function moduleResponse(item, response, data) {\n        const isError = response !== \"success\";\n        queue = queue.filter((queued)=>queued !== item);\n        switch(status){\n            case \"pending\":\n                break;\n            case \"failed\":\n                if (isError || !config.dataAfterTimeout) {\n                    return;\n                }\n                break;\n            default:\n                return;\n        }\n        if (response === \"abort\") {\n            lastError = data;\n            failQuery();\n            return;\n        }\n        if (isError) {\n            lastError = data;\n            if (!queue.length) {\n                if (!resources.length) {\n                    failQuery();\n                } else {\n                    execNext();\n                }\n            }\n            return;\n        }\n        resetTimer();\n        clearQueue();\n        if (!config.random) {\n            const index = config.resources.indexOf(item.resource);\n            if (index !== -1 && index !== config.index) {\n                config.index = index;\n            }\n        }\n        status = \"completed\";\n        doneCallbacks.forEach((callback)=>{\n            callback(data);\n        });\n    }\n    function execNext() {\n        if (status !== \"pending\") {\n            return;\n        }\n        resetTimer();\n        const resource = resources.shift();\n        if (resource === void 0) {\n            if (queue.length) {\n                timer = setTimeout(()=>{\n                    resetTimer();\n                    if (status === \"pending\") {\n                        clearQueue();\n                        failQuery();\n                    }\n                }, config.timeout);\n                return;\n            }\n            failQuery();\n            return;\n        }\n        const item = {\n            status: \"pending\",\n            resource,\n            callback: (status2, data)=>{\n                moduleResponse(item, status2, data);\n            }\n        };\n        queue.push(item);\n        queriesSent++;\n        timer = setTimeout(execNext, config.rotate);\n        query(resource, payload, item.callback);\n    }\n    setTimeout(execNext);\n    return getQueryStatus;\n}\n// src/index.ts\nfunction initRedundancy(cfg) {\n    const config = {\n        ...defaultConfig,\n        ...cfg\n    };\n    let queries = [];\n    function cleanup() {\n        queries = queries.filter((item)=>item().status === \"pending\");\n    }\n    function query(payload, queryCallback, doneCallback) {\n        const query2 = sendQuery(config, payload, queryCallback, (data, error)=>{\n            cleanup();\n            if (doneCallback) {\n                doneCallback(data, error);\n            }\n        });\n        queries.push(query2);\n        return query2;\n    }\n    function find(callback) {\n        return queries.find((value)=>{\n            return callback(value);\n        }) || null;\n    }\n    const instance = {\n        query,\n        find,\n        setIndex: (index)=>{\n            config.index = index;\n        },\n        getIndex: ()=>config.index,\n        cleanup\n    };\n    return instance;\n}\nfunction emptyCallback$1() {}\nconst redundancyCache = /* @__PURE__ */ Object.create(null);\nfunction getRedundancyCache(provider) {\n    if (!redundancyCache[provider]) {\n        const config = getAPIConfig(provider);\n        if (!config) {\n            return;\n        }\n        const redundancy = initRedundancy(config);\n        const cachedReundancy = {\n            config,\n            redundancy\n        };\n        redundancyCache[provider] = cachedReundancy;\n    }\n    return redundancyCache[provider];\n}\nfunction sendAPIQuery(target, query, callback) {\n    let redundancy;\n    let send;\n    if (typeof target === \"string\") {\n        const api = getAPIModule(target);\n        if (!api) {\n            callback(void 0, 424);\n            return emptyCallback$1;\n        }\n        send = api.send;\n        const cached = getRedundancyCache(target);\n        if (cached) {\n            redundancy = cached.redundancy;\n        }\n    } else {\n        const config = createAPIConfig(target);\n        if (config) {\n            redundancy = initRedundancy(config);\n            const moduleKey = target.resources ? target.resources[0] : \"\";\n            const api = getAPIModule(moduleKey);\n            if (api) {\n                send = api.send;\n            }\n        }\n    }\n    if (!redundancy || !send) {\n        callback(void 0, 424);\n        return emptyCallback$1;\n    }\n    return redundancy.query(query, send, callback)().abort;\n}\nfunction emptyCallback() {}\nfunction loadedNewIcons(storage) {\n    if (!storage.iconsLoaderFlag) {\n        storage.iconsLoaderFlag = true;\n        setTimeout(()=>{\n            storage.iconsLoaderFlag = false;\n            updateCallbacks(storage);\n        });\n    }\n}\nfunction checkIconNamesForAPI(icons) {\n    const valid = [];\n    const invalid = [];\n    icons.forEach((name)=>{\n        (name.match(matchIconName) ? valid : invalid).push(name);\n    });\n    return {\n        valid,\n        invalid\n    };\n}\nfunction parseLoaderResponse(storage, icons, data) {\n    function checkMissing() {\n        const pending = storage.pendingIcons;\n        icons.forEach((name)=>{\n            if (pending) {\n                pending.delete(name);\n            }\n            if (!storage.icons[name]) {\n                storage.missing.add(name);\n            }\n        });\n    }\n    if (data && typeof data === \"object\") {\n        try {\n            const parsed = addIconSet(storage, data);\n            if (!parsed.length) {\n                checkMissing();\n                return;\n            }\n        } catch (err) {\n            console.error(err);\n        }\n    }\n    checkMissing();\n    loadedNewIcons(storage);\n}\nfunction parsePossiblyAsyncResponse(response, callback) {\n    if (response instanceof Promise) {\n        response.then((data)=>{\n            callback(data);\n        }).catch(()=>{\n            callback(null);\n        });\n    } else {\n        callback(response);\n    }\n}\nfunction loadNewIcons(storage, icons) {\n    if (!storage.iconsToLoad) {\n        storage.iconsToLoad = icons;\n    } else {\n        storage.iconsToLoad = storage.iconsToLoad.concat(icons).sort();\n    }\n    if (!storage.iconsQueueFlag) {\n        storage.iconsQueueFlag = true;\n        setTimeout(()=>{\n            storage.iconsQueueFlag = false;\n            const { provider, prefix } = storage;\n            const icons2 = storage.iconsToLoad;\n            delete storage.iconsToLoad;\n            if (!icons2 || !icons2.length) {\n                return;\n            }\n            const customIconLoader = storage.loadIcon;\n            if (storage.loadIcons && (icons2.length > 1 || !customIconLoader)) {\n                parsePossiblyAsyncResponse(storage.loadIcons(icons2, prefix, provider), (data)=>{\n                    parseLoaderResponse(storage, icons2, data);\n                });\n                return;\n            }\n            if (customIconLoader) {\n                icons2.forEach((name)=>{\n                    const response = customIconLoader(name, prefix, provider);\n                    parsePossiblyAsyncResponse(response, (data)=>{\n                        const iconSet = data ? {\n                            prefix,\n                            icons: {\n                                [name]: data\n                            }\n                        } : null;\n                        parseLoaderResponse(storage, [\n                            name\n                        ], iconSet);\n                    });\n                });\n                return;\n            }\n            const { valid, invalid } = checkIconNamesForAPI(icons2);\n            if (invalid.length) {\n                parseLoaderResponse(storage, invalid, null);\n            }\n            if (!valid.length) {\n                return;\n            }\n            const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;\n            if (!api) {\n                parseLoaderResponse(storage, valid, null);\n                return;\n            }\n            const params = api.prepare(provider, prefix, valid);\n            params.forEach((item)=>{\n                sendAPIQuery(provider, item, (data)=>{\n                    parseLoaderResponse(storage, item.icons, data);\n                });\n            });\n        });\n    }\n}\nconst loadIcons = (icons, callback)=>{\n    const cleanedIcons = listToIcons(icons, true, allowSimpleNames());\n    const sortedIcons = sortIcons(cleanedIcons);\n    if (!sortedIcons.pending.length) {\n        let callCallback = true;\n        if (callback) {\n            setTimeout(()=>{\n                if (callCallback) {\n                    callback(sortedIcons.loaded, sortedIcons.missing, sortedIcons.pending, emptyCallback);\n                }\n            });\n        }\n        return ()=>{\n            callCallback = false;\n        };\n    }\n    const newIcons = /* @__PURE__ */ Object.create(null);\n    const sources = [];\n    let lastProvider, lastPrefix;\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix } = icon;\n        if (prefix === lastPrefix && provider === lastProvider) {\n            return;\n        }\n        lastProvider = provider;\n        lastPrefix = prefix;\n        sources.push(getStorage(provider, prefix));\n        const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));\n        if (!providerNewIcons[prefix]) {\n            providerNewIcons[prefix] = [];\n        }\n    });\n    sortedIcons.pending.forEach((icon)=>{\n        const { provider, prefix, name } = icon;\n        const storage = getStorage(provider, prefix);\n        const pendingQueue = storage.pendingIcons || (storage.pendingIcons = /* @__PURE__ */ new Set());\n        if (!pendingQueue.has(name)) {\n            pendingQueue.add(name);\n            newIcons[provider][prefix].push(name);\n        }\n    });\n    sources.forEach((storage)=>{\n        const list = newIcons[storage.provider][storage.prefix];\n        if (list.length) {\n            loadNewIcons(storage, list);\n        }\n    });\n    return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;\n};\nconst loadIcon = (icon)=>{\n    return new Promise((fulfill, reject)=>{\n        const iconObj = typeof icon === \"string\" ? stringToIcon(icon, true) : icon;\n        if (!iconObj) {\n            reject(icon);\n            return;\n        }\n        loadIcons([\n            iconObj || icon\n        ], (loaded)=>{\n            if (loaded.length && iconObj) {\n                const data = getIconData(iconObj);\n                if (data) {\n                    fulfill({\n                        ...defaultIconProps,\n                        ...data\n                    });\n                    return;\n                }\n            }\n            reject(icon);\n        });\n    });\n};\nfunction setCustomIconsLoader(loader, prefix, provider) {\n    getStorage(provider || \"\", prefix).loadIcons = loader;\n}\nfunction setCustomIconLoader(loader, prefix, provider) {\n    getStorage(provider || \"\", prefix).loadIcon = loader;\n}\nfunction mergeCustomisations(defaults, item) {\n    const result = {\n        ...defaults\n    };\n    for(const key in item){\n        const value = item[key];\n        const valueType = typeof value;\n        if (key in defaultIconSizeCustomisations) {\n            if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n                result[key] = value;\n            }\n        } else if (valueType === typeof result[key]) {\n            result[key] = key === \"rotate\" ? value % 4 : value;\n        }\n    }\n    return result;\n}\nconst separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n    flip.split(separator).forEach((str)=>{\n        const value = str.trim();\n        switch(value){\n            case \"horizontal\":\n                custom.hFlip = true;\n                break;\n            case \"vertical\":\n                custom.vFlip = true;\n                break;\n        }\n    });\n}\nfunction rotateFromString(value, defaultValue = 0) {\n    const units = value.replace(/^-?[0-9.]*/, \"\");\n    function cleanup(value2) {\n        while(value2 < 0){\n            value2 += 4;\n        }\n        return value2 % 4;\n    }\n    if (units === \"\") {\n        const num = parseInt(value);\n        return isNaN(num) ? 0 : cleanup(num);\n    } else if (units !== value) {\n        let split = 0;\n        switch(units){\n            case \"%\":\n                split = 25;\n                break;\n            case \"deg\":\n                split = 90;\n        }\n        if (split) {\n            let num = parseFloat(value.slice(0, value.length - units.length));\n            if (isNaN(num)) {\n                return 0;\n            }\n            num = num / split;\n            return num % 1 === 0 ? cleanup(num) : 0;\n        }\n    }\n    return defaultValue;\n}\nfunction iconToHTML(body, attributes) {\n    let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n    for(const attr in attributes){\n        renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n    }\n    return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\nfunction encodeSVGforURL(svg) {\n    return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n    return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n    return 'url(\"' + svgToData(svg) + '\")';\n}\nlet policy;\nfunction createPolicy() {\n    try {\n        policy = window.trustedTypes.createPolicy(\"iconify\", {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n            createHTML: (s)=>s\n        });\n    } catch (err) {\n        policy = null;\n    }\n}\nfunction cleanUpInnerHTML(html) {\n    if (policy === void 0) {\n        createPolicy();\n    }\n    return policy ? policy.createHTML(html) : html;\n}\nconst defaultExtendedIconCustomisations = {\n    ...defaultIconCustomisations,\n    inline: false\n};\n/**\n * Default SVG attributes\n */ const svgDefaults = {\n    \"xmlns\": \"http://www.w3.org/2000/svg\",\n    \"xmlnsXlink\": \"http://www.w3.org/1999/xlink\",\n    \"aria-hidden\": true,\n    \"role\": \"img\"\n};\n/**\n * Style modes\n */ const commonProps = {\n    display: \"inline-block\"\n};\nconst monotoneProps = {\n    backgroundColor: \"currentColor\"\n};\nconst coloredProps = {\n    backgroundColor: \"transparent\"\n};\n// Dynamically add common props to variables above\nconst propsToAdd = {\n    Image: \"var(--svg)\",\n    Repeat: \"no-repeat\",\n    Size: \"100% 100%\"\n};\nconst propsToAddTo = {\n    WebkitMask: monotoneProps,\n    mask: monotoneProps,\n    background: coloredProps\n};\nfor(const prefix in propsToAddTo){\n    const list = propsToAddTo[prefix];\n    for(const prop in propsToAdd){\n        list[prefix + prop] = propsToAdd[prop];\n    }\n}\n/**\n * Default values for customisations for inline icon\n */ const inlineDefaults = {\n    ...defaultExtendedIconCustomisations,\n    inline: true\n};\n/**\n * Fix size: add 'px' to numbers\n */ function fixSize(value) {\n    return value + (value.match(/^[-0-9.]+$/) ? \"px\" : \"\");\n}\n/**\n * Render icon\n */ const render = (// Icon must be validated before calling this function\nicon, // Partial properties\nprops, // Icon name\nname)=>{\n    // Get default properties\n    const defaultProps = props.inline ? inlineDefaults : defaultExtendedIconCustomisations;\n    // Get all customisations\n    const customisations = mergeCustomisations(defaultProps, props);\n    // Check mode\n    const mode = props.mode || \"svg\";\n    // Create style\n    const style = {};\n    const customStyle = props.style || {};\n    // Create SVG component properties\n    const componentProps = {\n        ...mode === \"svg\" ? svgDefaults : {}\n    };\n    if (name) {\n        const iconName = stringToIcon(name, false, true);\n        if (iconName) {\n            const classNames = [\n                \"iconify\"\n            ];\n            const props = [\n                \"provider\",\n                \"prefix\"\n            ];\n            for (const prop of props){\n                if (iconName[prop]) {\n                    classNames.push(\"iconify--\" + iconName[prop]);\n                }\n            }\n            componentProps.className = classNames.join(\" \");\n        }\n    }\n    // Get element properties\n    for(let key in props){\n        const value = props[key];\n        if (value === void 0) {\n            continue;\n        }\n        switch(key){\n            // Properties to ignore\n            case \"icon\":\n            case \"style\":\n            case \"children\":\n            case \"onLoad\":\n            case \"mode\":\n            case \"ssr\":\n                break;\n            // Forward ref\n            case \"_ref\":\n                componentProps.ref = value;\n                break;\n            // Merge class names\n            case \"className\":\n                componentProps[key] = (componentProps[key] ? componentProps[key] + \" \" : \"\") + value;\n                break;\n            // Boolean attributes\n            case \"inline\":\n            case \"hFlip\":\n            case \"vFlip\":\n                customisations[key] = value === true || value === \"true\" || value === 1;\n                break;\n            // Flip as string: 'horizontal,vertical'\n            case \"flip\":\n                if (typeof value === \"string\") {\n                    flipFromString(customisations, value);\n                }\n                break;\n            // Color: copy to style\n            case \"color\":\n                style.color = value;\n                break;\n            // Rotation as string\n            case \"rotate\":\n                if (typeof value === \"string\") {\n                    customisations[key] = rotateFromString(value);\n                } else if (typeof value === \"number\") {\n                    customisations[key] = value;\n                }\n                break;\n            // Remove aria-hidden\n            case \"ariaHidden\":\n            case \"aria-hidden\":\n                if (value !== true && value !== \"true\") {\n                    delete componentProps[\"aria-hidden\"];\n                }\n                break;\n            // Copy missing property if it does not exist in customisations\n            default:\n                if (defaultProps[key] === void 0) {\n                    componentProps[key] = value;\n                }\n        }\n    }\n    // Generate icon\n    const item = iconToSVG(icon, customisations);\n    const renderAttribs = item.attributes;\n    // Inline display\n    if (customisations.inline) {\n        style.verticalAlign = \"-0.125em\";\n    }\n    if (mode === \"svg\") {\n        // Add style\n        componentProps.style = {\n            ...style,\n            ...customStyle\n        };\n        // Add icon stuff\n        Object.assign(componentProps, renderAttribs);\n        // Counter for ids based on \"id\" property to render icons consistently on server and client\n        let localCounter = 0;\n        let id = props.id;\n        if (typeof id === \"string\") {\n            // Convert '-' to '_' to avoid errors in animations\n            id = id.replace(/-/g, \"_\");\n        }\n        // Add icon stuff\n        componentProps.dangerouslySetInnerHTML = {\n            __html: cleanUpInnerHTML(replaceIDs(item.body, id ? ()=>id + \"ID\" + localCounter++ : \"iconifyReact\"))\n        };\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", componentProps);\n    }\n    // Render <span> with style\n    const { body, width, height } = icon;\n    const useMask = mode === \"mask\" || (mode === \"bg\" ? false : body.indexOf(\"currentColor\") !== -1);\n    // Generate SVG\n    const html = iconToHTML(body, {\n        ...renderAttribs,\n        width: width + \"\",\n        height: height + \"\"\n    });\n    // Generate style\n    componentProps.style = {\n        ...style,\n        \"--svg\": svgToURL(html),\n        \"width\": fixSize(renderAttribs.width),\n        \"height\": fixSize(renderAttribs.height),\n        ...commonProps,\n        ...useMask ? monotoneProps : coloredProps,\n        ...customStyle\n    };\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"span\", componentProps);\n};\n/**\n * Initialise stuff\n */ // Enable short names\nallowSimpleNames(true);\n// Set API module\nsetAPIModule(\"\", fetchAPIModule);\n/**\n * Browser stuff\n */ if (typeof document !== \"undefined\" && \"undefined\" !== \"undefined\") {}\nfunction IconComponent(props) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!!props.ssr);\n    const [abort, setAbort] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Get initial state\n    function getInitialState(mounted) {\n        if (mounted) {\n            const name = props.icon;\n            if (typeof name === \"object\") {\n                // Icon as object\n                return {\n                    name: \"\",\n                    data: name\n                };\n            }\n            const data = getIconData(name);\n            if (data) {\n                return {\n                    name,\n                    data\n                };\n            }\n        }\n        return {\n            name: \"\"\n        };\n    }\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState(!!props.ssr));\n    // Cancel loading\n    function cleanup() {\n        const callback = abort.callback;\n        if (callback) {\n            callback();\n            setAbort({});\n        }\n    }\n    // Change state if it is different\n    function changeState(newState) {\n        if (JSON.stringify(state) !== JSON.stringify(newState)) {\n            cleanup();\n            setState(newState);\n            return true;\n        }\n    }\n    // Update state\n    function updateState() {\n        var _a;\n        const name = props.icon;\n        if (typeof name === \"object\") {\n            // Icon as object\n            changeState({\n                name: \"\",\n                data: name\n            });\n            return;\n        }\n        // New icon or got icon data\n        const data = getIconData(name);\n        if (changeState({\n            name,\n            data\n        })) {\n            if (data === undefined) {\n                // Load icon, update state when done\n                const callback = loadIcons([\n                    name\n                ], updateState);\n                setAbort({\n                    callback\n                });\n            } else if (data) {\n                // Icon data is available: trigger onLoad callback if present\n                (_a = props.onLoad) === null || _a === void 0 ? void 0 : _a.call(props, name);\n            }\n        }\n    }\n    // Mounted state, cleanup for loader\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setMounted(true);\n        return cleanup;\n    }, []);\n    // Icon changed or component mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (mounted) {\n            updateState();\n        }\n    }, [\n        props.icon,\n        mounted\n    ]);\n    // Render icon\n    const { name, data } = state;\n    if (!data) {\n        return props.children ? props.children : props.fallback ? props.fallback : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"span\", {});\n    }\n    return render({\n        ...defaultIconProps,\n        ...data\n    }, props, name);\n}\n/**\n * Block icon\n *\n * @param props - Component properties\n */ const Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>IconComponent({\n        ...props,\n        _ref: ref\n    }));\n/**\n * Inline icon (has negative verticalAlign that makes it behave like icon font)\n *\n * @param props - Component properties\n */ const InlineIcon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>IconComponent({\n        inline: true,\n        ...props,\n        _ref: ref\n    }));\n/**\n * Internal API\n */ const _api = {\n    getAPIConfig,\n    setAPIModule,\n    sendAPIQuery,\n    setFetch,\n    getFetch,\n    listAPIProviders\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/react/dist/iconify.js\n");

/***/ })

};
;