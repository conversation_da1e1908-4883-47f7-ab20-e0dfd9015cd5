"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/editor.tsx":
/*!***************************************************!*\
  !*** ./src/features/editor/components/editor.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Editor: function() { return /* binding */ Editor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/./node_modules/lodash.debounce/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/projects/api/use-update-project */ \"(app-pages-browser)/./src/features/projects/api/use-update-project.ts\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/components/navbar */ \"(app-pages-browser)/./src/features/editor/components/navbar.tsx\");\n/* harmony import */ var _features_editor_components_footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/components/footer */ \"(app-pages-browser)/./src/features/editor/components/footer.tsx\");\n/* harmony import */ var _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-editor */ \"(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\");\n/* harmony import */ var _features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/components/sidebar */ \"(app-pages-browser)/./src/features/editor/components/sidebar.tsx\");\n/* harmony import */ var _features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/components/toolbar */ \"(app-pages-browser)/./src/features/editor/components/toolbar.tsx\");\n/* harmony import */ var _features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/components/shape-sidebar */ \"(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/features/editor/components/fill-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/fill-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/editor/components/stroke-color-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/features/editor/components/stroke-width-sidebar */ \"(app-pages-browser)/./src/features/editor/components/stroke-width-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/editor/components/opacity-sidebar */ \"(app-pages-browser)/./src/features/editor/components/opacity-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/features/editor/components/text-sidebar */ \"(app-pages-browser)/./src/features/editor/components/text-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/features/editor/components/font-sidebar */ \"(app-pages-browser)/./src/features/editor/components/font-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/features/editor/components/image-sidebar */ \"(app-pages-browser)/./src/features/editor/components/image-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/features/editor/components/filter-sidebar */ \"(app-pages-browser)/./src/features/editor/components/filter-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/features/editor/components/draw-sidebar */ \"(app-pages-browser)/./src/features/editor/components/draw-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/features/editor/components/ai-sidebar */ \"(app-pages-browser)/./src/features/editor/components/ai-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/features/editor/components/template-sidebar */ \"(app-pages-browser)/./src/features/editor/components/template-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/features/editor/components/remove-bg-sidebar */ \"(app-pages-browser)/./src/features/editor/components/remove-bg-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/features/editor/components/settings-sidebar */ \"(app-pages-browser)/./src/features/editor/components/settings-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_icon_sidebar__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/editor/components/icon-sidebar */ \"(app-pages-browser)/./src/features/editor/components/icon-sidebar.tsx\");\n/* harmony import */ var _features_editor_components_alignment_indicators__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/editor/components/alignment-indicators */ \"(app-pages-browser)/./src/features/editor/components/alignment-indicators.tsx\");\n/* __next_internal_client_entry_do_not_use__ Editor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Editor = (param)=>{\n    let { initialData } = param;\n    _s();\n    const { mutate } = (0,_features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject)(initialData.id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const debouncedSave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(lodash_debounce__WEBPACK_IMPORTED_MODULE_2___default()((values)=>{\n        mutate(values);\n    }, 500), [\n        mutate\n    ]);\n    const [activeTool, setActiveTool] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"select\");\n    const [canvasIsSelected, setCanvasIsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const onClearSelection = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        if (_features_editor_types__WEBPACK_IMPORTED_MODULE_5__.selectionDependentTools.includes(activeTool)) {\n            setActiveTool(\"select\");\n        }\n    }, [\n        activeTool\n    ]);\n    const { init, editor } = (0,_features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__.useEditor)({\n        defaultState: initialData.json,\n        defaultWidth: initialData.width,\n        defaultHeight: initialData.height,\n        clearSelectionCallback: onClearSelection,\n        saveCallback: debouncedSave,\n        setCanvasIsSelected\n    });\n    const onChangeActiveTool = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((tool)=>{\n        if (tool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.enableDrawingMode();\n        }\n        if (activeTool === \"draw\") {\n            editor === null || editor === void 0 ? void 0 : editor.disableDrawingMode();\n        }\n        if (tool === activeTool) {\n            return setActiveTool(\"select\");\n        }\n        setActiveTool(tool);\n    }, [\n        activeTool,\n        editor\n    ]);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const canvas = new fabric__WEBPACK_IMPORTED_MODULE_1__.fabric.Canvas(canvasRef.current, {\n            controlsAboveOverlay: true,\n            preserveObjectStacking: true\n        });\n        init({\n            initialCanvas: canvas,\n            initialContainer: containerRef.current\n        });\n        return ()=>{\n            canvas.dispose();\n        };\n    }, [\n        init\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_navbar__WEBPACK_IMPORTED_MODULE_6__.Navbar, {\n                id: initialData.id,\n                editor: editor,\n                activeTool: activeTool,\n                onChangeActiveTool: onChangeActiveTool,\n                canvasIsSelected: canvasIsSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute h-[calc(100%-68px)] w-full top-[68px] flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_sidebar__WEBPACK_IMPORTED_MODULE_9__.Sidebar, {\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_sidebar__WEBPACK_IMPORTED_MODULE_11__.ShapeSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_fill_color_sidebar__WEBPACK_IMPORTED_MODULE_12__.FillColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_color_sidebar__WEBPACK_IMPORTED_MODULE_13__.StrokeColorSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_stroke_width_sidebar__WEBPACK_IMPORTED_MODULE_14__.StrokeWidthSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_opacity_sidebar__WEBPACK_IMPORTED_MODULE_15__.OpacitySidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_text_sidebar__WEBPACK_IMPORTED_MODULE_16__.TextSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_sidebar__WEBPACK_IMPORTED_MODULE_17__.FontSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_image_sidebar__WEBPACK_IMPORTED_MODULE_18__.ImageSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_template_sidebar__WEBPACK_IMPORTED_MODULE_22__.TemplateSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_filter_sidebar__WEBPACK_IMPORTED_MODULE_19__.FilterSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_ai_sidebar__WEBPACK_IMPORTED_MODULE_21__.AiSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_remove_bg_sidebar__WEBPACK_IMPORTED_MODULE_23__.RemoveBgSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_draw_sidebar__WEBPACK_IMPORTED_MODULE_20__.DrawSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_settings_sidebar__WEBPACK_IMPORTED_MODULE_24__.SettingsSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_icon_sidebar__WEBPACK_IMPORTED_MODULE_25__.IconSidebar, {\n                        editor: editor,\n                        activeTool: activeTool,\n                        onChangeActiveTool: onChangeActiveTool\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"bg-muted flex-1 overflow-auto relative flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_toolbar__WEBPACK_IMPORTED_MODULE_10__.Toolbar, {\n                                editor: editor,\n                                activeTool: activeTool,\n                                onChangeActiveTool: onChangeActiveTool\n                            }, JSON.stringify(editor === null || editor === void 0 ? void 0 : editor.canvas.getActiveObject()), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 h-[calc(100%-124px)] bg-muted relative\",\n                                ref: containerRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                        ref: canvasRef\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_alignment_indicators__WEBPACK_IMPORTED_MODULE_26__.AlignmentIndicators, {\n                                        canvas: (editor === null || editor === void 0 ? void 0 : editor.canvas) || null,\n                                        selectedObjects: (editor === null || editor === void 0 ? void 0 : editor.selectedObjects) || []\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_footer__WEBPACK_IMPORTED_MODULE_7__.Footer, {\n                                editor: editor\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\editor.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Editor, \"n/S7kUBrgsra2E9lpJNgEzHIIBc=\", false, function() {\n    return [\n        _features_projects_api_use_update_project__WEBPACK_IMPORTED_MODULE_4__.useUpdateProject,\n        _features_editor_hooks_use_editor__WEBPACK_IMPORTED_MODULE_8__.useEditor\n    ];\n});\n_c = Editor;\nvar _c;\n$RefreshReg$(_c, \"Editor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/editor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/editor/components/icon-sidebar.tsx":
/*!*********************************************************!*\
  !*** ./src/features/editor/components/icon-sidebar.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IconSidebar: function() { return /* binding */ IconSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst IconSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    _s();\n    const selectedObject = editor === null || editor === void 0 ? void 0 : editor.selectedObjects[0];\n    const initialSize = (editor === null || editor === void 0 ? void 0 : editor.getActiveIconSize()) || {\n        width: 80,\n        height: 80\n    };\n    const [width, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialSize.width.toString());\n    const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialSize.height.toString());\n    const [maintainAspectRatio, setMaintainAspectRatio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedObject && selectedObject.type === \"icon\") {\n            const size = (editor === null || editor === void 0 ? void 0 : editor.getActiveIconSize()) || {\n                width: 80,\n                height: 80\n            };\n            setWidth(size.width.toString());\n            setHeight(size.height.toString());\n        }\n    }, [\n        selectedObject,\n        editor\n    ]);\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const handleWidthChange = (value)=>{\n        setWidth(value);\n        if (maintainAspectRatio && value) {\n            const aspectRatio = initialSize.height / initialSize.width;\n            const newHeight = Math.round(parseInt(value) * aspectRatio);\n            setHeight(newHeight.toString());\n        }\n    };\n    const handleHeightChange = (value)=>{\n        setHeight(value);\n        if (maintainAspectRatio && value) {\n            const aspectRatio = initialSize.width / initialSize.height;\n            const newWidth = Math.round(parseInt(value) * aspectRatio);\n            setWidth(newWidth.toString());\n        }\n    };\n    const applySize = ()=>{\n        const widthValue = parseInt(width) || 80;\n        const heightValue = parseInt(height) || 80;\n        editor === null || editor === void 0 ? void 0 : editor.changeIconSize(widthValue, heightValue);\n    };\n    const resetSize = ()=>{\n        setWidth(\"80\");\n        setHeight(\"80\");\n        editor === null || editor === void 0 ? void 0 : editor.changeIconSize(80, 80);\n    };\n    // Preset sizes\n    const presetSizes = [\n        {\n            label: \"Small\",\n            width: 40,\n            height: 40\n        },\n        {\n            label: \"Medium\",\n            width: 80,\n            height: 80\n        },\n        {\n            label: \"Large\",\n            width: 120,\n            height: 120\n        },\n        {\n            label: \"Extra Large\",\n            width: 160,\n            height: 160\n        }\n    ];\n    const applyPresetSize = (presetWidth, presetHeight)=>{\n        setWidth(presetWidth.toString());\n        setHeight(presetHeight.toString());\n        editor === null || editor === void 0 ? void 0 : editor.changeIconSize(presetWidth, presetHeight);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"icon-settings\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_3__.ToolSidebarHeader, {\n                title: \"Icon Settings\",\n                description: \"Adjust icon size and properties\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Size\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: presetSizes.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>applyPresetSize(preset.width, preset.height),\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                preset.label,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-1 text-gray-500\",\n                                                    children: [\n                                                        preset.width,\n                                                        \"\\xd7\",\n                                                        preset.height\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, preset.label, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Width\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            type: \"number\",\n                                                            value: width,\n                                                            onChange: (e)=>handleWidthChange(e.target.value),\n                                                            placeholder: \"Width\",\n                                                            min: \"10\",\n                                                            max: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Height\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            type: \"number\",\n                                                            value: height,\n                                                            onChange: (e)=>handleHeightChange(e.target.value),\n                                                            placeholder: \"Height\",\n                                                            min: \"10\",\n                                                            max: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    id: \"aspect-ratio\",\n                                                    checked: maintainAspectRatio,\n                                                    onChange: (e)=>setMaintainAspectRatio(e.target.checked),\n                                                    className: \"rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"aspect-ratio\",\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Maintain aspect ratio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    onClick: applySize,\n                                                    size: \"sm\",\n                                                    className: \"flex-1\",\n                                                    children: \"Apply Size\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    onClick: resetSize,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex-1\",\n                                                    children: \"Reset\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 border-t pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>{\n                                                const currentSize = (editor === null || editor === void 0 ? void 0 : editor.getActiveIconSize()) || {\n                                                    width: 80,\n                                                    height: 80\n                                                };\n                                                const newWidth = Math.round(currentSize.width * 1.5);\n                                                const newHeight = Math.round(currentSize.height * 1.5);\n                                                setWidth(newWidth.toString());\n                                                setHeight(newHeight.toString());\n                                                editor === null || editor === void 0 ? void 0 : editor.changeIconSize(newWidth, newHeight);\n                                            },\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"Scale Up 1.5\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: ()=>{\n                                                const currentSize = (editor === null || editor === void 0 ? void 0 : editor.getActiveIconSize()) || {\n                                                    width: 80,\n                                                    height: 80\n                                                };\n                                                const newWidth = Math.round(currentSize.width * 0.75);\n                                                const newHeight = Math.round(currentSize.height * 0.75);\n                                                setWidth(newWidth.toString());\n                                                setHeight(newHeight.toString());\n                                                editor === null || editor === void 0 ? void 0 : editor.changeIconSize(newWidth, newHeight);\n                                            },\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"Scale Down 0.75\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_2__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\icon-sidebar.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n_s(IconSidebar, \"s0HkJbs/9W1QeCTi7LEXfaW3Hhg=\");\n_c = IconSidebar;\nvar _c;\n$RefreshReg$(_c, \"IconSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/icon-sidebar.tsx\n"));

/***/ })

});