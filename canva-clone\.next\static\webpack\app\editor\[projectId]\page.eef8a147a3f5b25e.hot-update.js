"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_60__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_61__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-open.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_62__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_63__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_64__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_65__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_66__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_67__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_68__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_69__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_70__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_71__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_72__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_73__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_74__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_75__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_76__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_77__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_78__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_79__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_80__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_81__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_82__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_83__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_84__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_85__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_86__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_87__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/umbrella.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_88__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/snowflake.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_89__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_90__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_91__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_92__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trees.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_93__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flower.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_94__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_95__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pizza.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_96__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_97__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wine.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_98__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_99__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_100__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_101__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_102__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_103__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/keyboard.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_104__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_105__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_106__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/usb.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_107__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bluetooth.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_108__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_109__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tv.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_110__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_111__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tram-front.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_112__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_113__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_114__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ship.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_115__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/anchor.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_116__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_117__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_118__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/navigation.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_119__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_120__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_121__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hospital.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_122__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_123__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_124__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tent.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_125__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_126__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/waves.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_127__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sunrise.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_128__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sunset.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Comprehensive icon mapping using Lucide React icons\nconst ICON_COMPONENTS = {\n    \"lucide:heart\": lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"lucide:star\": lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"lucide:arrow-right\": lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"lucide:arrow-left\": lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"lucide:arrow-up\": lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"lucide:arrow-down\": lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"lucide:home\": lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"lucide:user\": lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"lucide:settings\": lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    \"lucide:mail\": lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    \"lucide:phone\": lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    \"lucide:car\": lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    \"lucide:camera\": lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    \"lucide:music\": lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    \"lucide:video\": lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n    \"lucide:image\": lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n    \"lucide:file\": lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n    \"lucide:folder\": lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n    \"lucide:search\": lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n    \"lucide:plus\": lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n    \"lucide:minus\": lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n    \"lucide:x\": lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n    \"lucide:check\": lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n    \"lucide:edit\": lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n    \"lucide:trash\": lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n    \"lucide:download\": lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n    \"lucide:upload\": lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n    \"lucide:share\": lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"],\n    \"lucide:copy\": lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n    \"lucide:save\": lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n    \"lucide:print\": lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    \"lucide:calendar\": lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n    \"lucide:clock\": lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"],\n    \"lucide:map\": lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"],\n    \"lucide:globe\": lucide_react__WEBPACK_IMPORTED_MODULE_46__[\"default\"],\n    \"lucide:wifi\": lucide_react__WEBPACK_IMPORTED_MODULE_47__[\"default\"],\n    \"lucide:battery\": lucide_react__WEBPACK_IMPORTED_MODULE_48__[\"default\"],\n    \"lucide:volume\": lucide_react__WEBPACK_IMPORTED_MODULE_49__[\"default\"],\n    \"lucide:play\": lucide_react__WEBPACK_IMPORTED_MODULE_50__[\"default\"],\n    \"lucide:pause\": lucide_react__WEBPACK_IMPORTED_MODULE_51__[\"default\"],\n    \"lucide:stop\": lucide_react__WEBPACK_IMPORTED_MODULE_52__[\"default\"],\n    \"lucide:skip-forward\": lucide_react__WEBPACK_IMPORTED_MODULE_53__[\"default\"],\n    \"lucide:skip-back\": lucide_react__WEBPACK_IMPORTED_MODULE_54__[\"default\"],\n    \"lucide:refresh\": lucide_react__WEBPACK_IMPORTED_MODULE_55__[\"default\"],\n    \"lucide:maximize\": lucide_react__WEBPACK_IMPORTED_MODULE_56__[\"default\"],\n    \"lucide:minimize\": lucide_react__WEBPACK_IMPORTED_MODULE_57__[\"default\"],\n    \"lucide:eye\": lucide_react__WEBPACK_IMPORTED_MODULE_58__[\"default\"],\n    \"lucide:eye-off\": lucide_react__WEBPACK_IMPORTED_MODULE_59__[\"default\"],\n    \"lucide:lock\": lucide_react__WEBPACK_IMPORTED_MODULE_60__[\"default\"],\n    \"lucide:unlock\": lucide_react__WEBPACK_IMPORTED_MODULE_61__[\"default\"],\n    \"lucide:shield\": lucide_react__WEBPACK_IMPORTED_MODULE_62__[\"default\"],\n    \"lucide:alert\": lucide_react__WEBPACK_IMPORTED_MODULE_63__[\"default\"],\n    \"lucide:info\": lucide_react__WEBPACK_IMPORTED_MODULE_64__[\"default\"],\n    \"lucide:help\": lucide_react__WEBPACK_IMPORTED_MODULE_65__[\"default\"],\n    \"lucide:bookmark\": lucide_react__WEBPACK_IMPORTED_MODULE_66__[\"default\"],\n    \"lucide:tag\": lucide_react__WEBPACK_IMPORTED_MODULE_67__[\"default\"],\n    \"lucide:flag\": lucide_react__WEBPACK_IMPORTED_MODULE_68__[\"default\"],\n    \"lucide:thumbs-up\": lucide_react__WEBPACK_IMPORTED_MODULE_69__[\"default\"],\n    \"lucide:thumbs-down\": lucide_react__WEBPACK_IMPORTED_MODULE_70__[\"default\"],\n    \"lucide:message\": lucide_react__WEBPACK_IMPORTED_MODULE_71__[\"default\"],\n    \"lucide:send\": lucide_react__WEBPACK_IMPORTED_MODULE_72__[\"default\"],\n    \"lucide:bell\": lucide_react__WEBPACK_IMPORTED_MODULE_73__[\"default\"],\n    \"lucide:gift\": lucide_react__WEBPACK_IMPORTED_MODULE_74__[\"default\"],\n    \"lucide:shopping-cart\": lucide_react__WEBPACK_IMPORTED_MODULE_75__[\"default\"],\n    \"lucide:credit-card\": lucide_react__WEBPACK_IMPORTED_MODULE_76__[\"default\"],\n    \"lucide:dollar-sign\": lucide_react__WEBPACK_IMPORTED_MODULE_77__[\"default\"],\n    \"lucide:trending-up\": lucide_react__WEBPACK_IMPORTED_MODULE_78__[\"default\"],\n    \"lucide:trending-down\": lucide_react__WEBPACK_IMPORTED_MODULE_79__[\"default\"],\n    \"lucide:bar-chart\": lucide_react__WEBPACK_IMPORTED_MODULE_80__[\"default\"],\n    \"lucide:pie-chart\": lucide_react__WEBPACK_IMPORTED_MODULE_81__[\"default\"],\n    \"lucide:activity\": lucide_react__WEBPACK_IMPORTED_MODULE_82__[\"default\"],\n    \"lucide:zap\": lucide_react__WEBPACK_IMPORTED_MODULE_83__[\"default\"],\n    \"lucide:sun\": lucide_react__WEBPACK_IMPORTED_MODULE_84__[\"default\"],\n    \"lucide:moon\": lucide_react__WEBPACK_IMPORTED_MODULE_85__[\"default\"],\n    \"lucide:cloud\": lucide_react__WEBPACK_IMPORTED_MODULE_86__[\"default\"],\n    \"lucide:umbrella\": lucide_react__WEBPACK_IMPORTED_MODULE_87__[\"default\"],\n    \"lucide:snowflake\": lucide_react__WEBPACK_IMPORTED_MODULE_88__[\"default\"],\n    \"lucide:droplet\": lucide_react__WEBPACK_IMPORTED_MODULE_89__[\"default\"],\n    \"lucide:flame\": lucide_react__WEBPACK_IMPORTED_MODULE_90__[\"default\"],\n    \"lucide:leaf\": lucide_react__WEBPACK_IMPORTED_MODULE_91__[\"default\"],\n    \"lucide:tree\": lucide_react__WEBPACK_IMPORTED_MODULE_92__[\"default\"],\n    \"lucide:flower\": lucide_react__WEBPACK_IMPORTED_MODULE_93__[\"default\"],\n    \"lucide:coffee\": lucide_react__WEBPACK_IMPORTED_MODULE_94__[\"default\"],\n    \"lucide:pizza\": lucide_react__WEBPACK_IMPORTED_MODULE_95__[\"default\"],\n    \"lucide:utensils\": lucide_react__WEBPACK_IMPORTED_MODULE_96__[\"default\"],\n    \"lucide:wine\": lucide_react__WEBPACK_IMPORTED_MODULE_97__[\"default\"],\n    \"lucide:gamepad\": lucide_react__WEBPACK_IMPORTED_MODULE_98__[\"default\"],\n    \"lucide:headphones\": lucide_react__WEBPACK_IMPORTED_MODULE_99__[\"default\"],\n    \"lucide:smartphone\": lucide_react__WEBPACK_IMPORTED_MODULE_100__[\"default\"],\n    \"lucide:laptop\": lucide_react__WEBPACK_IMPORTED_MODULE_101__[\"default\"],\n    \"lucide:monitor\": lucide_react__WEBPACK_IMPORTED_MODULE_102__[\"default\"],\n    \"lucide:keyboard\": lucide_react__WEBPACK_IMPORTED_MODULE_103__[\"default\"],\n    \"lucide:mouse\": lucide_react__WEBPACK_IMPORTED_MODULE_104__[\"default\"],\n    \"lucide:printer\": lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    \"lucide:hard-drive\": lucide_react__WEBPACK_IMPORTED_MODULE_105__[\"default\"],\n    \"lucide:usb\": lucide_react__WEBPACK_IMPORTED_MODULE_106__[\"default\"],\n    \"lucide:bluetooth\": lucide_react__WEBPACK_IMPORTED_MODULE_107__[\"default\"],\n    \"lucide:radio\": lucide_react__WEBPACK_IMPORTED_MODULE_108__[\"default\"],\n    \"lucide:tv\": lucide_react__WEBPACK_IMPORTED_MODULE_109__[\"default\"],\n    \"lucide:plane\": lucide_react__WEBPACK_IMPORTED_MODULE_110__[\"default\"],\n    \"lucide:train\": lucide_react__WEBPACK_IMPORTED_MODULE_111__[\"default\"],\n    \"lucide:bus\": lucide_react__WEBPACK_IMPORTED_MODULE_112__[\"default\"],\n    \"lucide:bike\": lucide_react__WEBPACK_IMPORTED_MODULE_113__[\"default\"],\n    \"lucide:ship\": lucide_react__WEBPACK_IMPORTED_MODULE_114__[\"default\"],\n    \"lucide:anchor\": lucide_react__WEBPACK_IMPORTED_MODULE_115__[\"default\"],\n    \"lucide:compass\": lucide_react__WEBPACK_IMPORTED_MODULE_116__[\"default\"],\n    \"lucide:map-pin\": lucide_react__WEBPACK_IMPORTED_MODULE_117__[\"default\"],\n    \"lucide:navigation\": lucide_react__WEBPACK_IMPORTED_MODULE_118__[\"default\"],\n    \"lucide:building\": lucide_react__WEBPACK_IMPORTED_MODULE_119__[\"default\"],\n    \"lucide:school\": lucide_react__WEBPACK_IMPORTED_MODULE_120__[\"default\"],\n    \"lucide:hospital\": lucide_react__WEBPACK_IMPORTED_MODULE_121__[\"default\"],\n    \"lucide:store\": lucide_react__WEBPACK_IMPORTED_MODULE_122__[\"default\"],\n    \"lucide:factory\": lucide_react__WEBPACK_IMPORTED_MODULE_123__[\"default\"],\n    \"lucide:tent\": lucide_react__WEBPACK_IMPORTED_MODULE_124__[\"default\"],\n    \"lucide:mountain\": lucide_react__WEBPACK_IMPORTED_MODULE_125__[\"default\"],\n    \"lucide:waves\": lucide_react__WEBPACK_IMPORTED_MODULE_126__[\"default\"],\n    \"lucide:sunrise\": lucide_react__WEBPACK_IMPORTED_MODULE_127__[\"default\"],\n    \"lucide:sunset\": lucide_react__WEBPACK_IMPORTED_MODULE_128__[\"default\"]\n};\n// Helper function to get SVG paths for icons (static approach for reliability)\nconst getIconSVGPaths = (iconName)=>{\n    const iconPaths = {\n        \"lucide:heart\": '<path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"/>',\n        \"lucide:star\": '<polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"/>',\n        \"lucide:arrow-right\": '<line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"/><polyline points=\"12,5 19,12 12,19\"/>',\n        \"lucide:arrow-left\": '<line x1=\"19\" y1=\"12\" x2=\"5\" y2=\"12\"/><polyline points=\"12,19 5,12 12,5\"/>',\n        \"lucide:arrow-up\": '<line x1=\"12\" y1=\"19\" x2=\"12\" y2=\"5\"/><polyline points=\"5,12 12,5 19,12\"/>',\n        \"lucide:arrow-down\": '<line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"/><polyline points=\"19,12 12,19 5,12\"/>',\n        \"lucide:home\": '<path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/><polyline points=\"9,22 9,12 15,12 15,22\"/>',\n        \"lucide:user\": '<path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/><circle cx=\"12\" cy=\"7\" r=\"4\"/>',\n        \"lucide:settings\": '<circle cx=\"12\" cy=\"12\" r=\"3\"/><path d=\"M12 1v6m0 6v6m11-7h-6m-6 0H1\"/>',\n        \"lucide:mail\": '<path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"/><polyline points=\"22,6 12,13 2,6\"/>',\n        \"lucide:phone\": '<path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"/>',\n        \"lucide:car\": '<path d=\"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18.4 10H5.6L3.5 11.1C2.7 11.3 2 12.1 2 13v3c0 .6.4 1 1 1h2\"/><circle cx=\"7\" cy=\"17\" r=\"2\"/><path d=\"M9 17h6\"/><circle cx=\"17\" cy=\"17\" r=\"2\"/>',\n        \"lucide:camera\": '<path d=\"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z\"/><circle cx=\"12\" cy=\"13\" r=\"3\"/>',\n        \"lucide:music\": '<path d=\"M9 18V5l12-2v13\"/><circle cx=\"6\" cy=\"18\" r=\"3\"/><circle cx=\"18\" cy=\"16\" r=\"3\"/>',\n        \"lucide:video\": '<path d=\"M23 7l-7 5 7 5V7z\"/><rect x=\"1\" y=\"5\" width=\"15\" height=\"14\" rx=\"2\" ry=\"2\"/>',\n        \"lucide:image\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><circle cx=\"9\" cy=\"9\" r=\"2\"/><path d=\"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\"/>',\n        \"lucide:file\": '<path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/>',\n        \"lucide:folder\": '<path d=\"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\"/>',\n        \"lucide:search\": '<circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/>',\n        \"lucide:plus\": '<path d=\"M5 12h14\"/><path d=\"M12 5v14\"/>',\n        \"lucide:minus\": '<path d=\"M5 12h14\"/>',\n        \"lucide:x\": '<path d=\"M18 6 6 18\"/><path d=\"m6 6 12 12\"/>',\n        \"lucide:check\": '<polyline points=\"20,6 9,17 4,12\"/>',\n        \"lucide:edit\": '<path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"/><path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"/>',\n        \"lucide:trash\": '<path d=\"M3 6h18\"/><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/><path d=\"M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2\"/>',\n        \"lucide:download\": '<path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/><polyline points=\"7,10 12,15 17,10\"/><line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"/>',\n        \"lucide:upload\": '<path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/><polyline points=\"17,8 12,3 7,8\"/><line x1=\"12\" y1=\"3\" x2=\"12\" y2=\"15\"/>',\n        \"lucide:share\": '<path d=\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\"/><polyline points=\"16,6 12,2 8,6\"/><line x1=\"12\" y1=\"2\" x2=\"12\" y2=\"15\"/>',\n        \"lucide:copy\": '<rect x=\"9\" y=\"9\" width=\"13\" height=\"13\" rx=\"2\" ry=\"2\"/><path d=\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"/>',\n        \"lucide:save\": '<path d=\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\"/><polyline points=\"17,21 17,13 7,13 7,21\"/><polyline points=\"7,3 7,8 15,8\"/>',\n        \"lucide:print\": '<polyline points=\"6,9 6,2 18,2 18,9\"/><path d=\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\"/><rect x=\"6\" y=\"14\" width=\"12\" height=\"8\"/>',\n        \"lucide:calendar\": '<rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"/><line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"/><line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"/>',\n        \"lucide:clock\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><polyline points=\"12,6 12,12 16,14\"/>',\n        \"lucide:map\": '<polygon points=\"1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2\"/>',\n        \"lucide:globe\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"/><path d=\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\"/>',\n        \"lucide:wifi\": '<path d=\"M5 12.55a11 11 0 0 1 14.08 0\"/><path d=\"M1.42 9a16 16 0 0 1 21.16 0\"/><path d=\"M8.53 16.11a6 6 0 0 1 6.95 0\"/><line x1=\"12\" y1=\"20\" x2=\"12.01\" y2=\"20\"/>',\n        \"lucide:battery\": '<rect x=\"1\" y=\"6\" width=\"18\" height=\"12\" rx=\"2\" ry=\"2\"/><line x1=\"23\" y1=\"13\" x2=\"23\" y2=\"11\"/>',\n        \"lucide:volume\": '<polygon points=\"11,5 6,9 2,9 2,15 6,15 11,19\"/><path d=\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\"/>',\n        \"lucide:play\": '<polygon points=\"5,3 19,12 5,21\"/>',\n        \"lucide:pause\": '<rect x=\"6\" y=\"4\" width=\"4\" height=\"16\"/><rect x=\"14\" y=\"4\" width=\"4\" height=\"16\"/>',\n        \"lucide:stop\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>',\n        \"lucide:skip-forward\": '<polygon points=\"5,4 15,12 5,20\"/><line x1=\"19\" y1=\"5\" x2=\"19\" y2=\"19\"/>',\n        \"lucide:skip-back\": '<polygon points=\"19,20 9,12 19,4\"/><line x1=\"5\" y1=\"19\" x2=\"5\" y2=\"5\"/>',\n        \"lucide:refresh\": '<polyline points=\"23,4 23,10 17,10\"/><polyline points=\"1,20 1,14 7,14\"/><path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4-4.64 4.36A9 9 0 0 1 3.51 15\"/>',\n        // Additional icons\n        \"lucide:circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/>',\n        \"lucide:triangle\": '<path d=\"M12 2 22 20H2z\"/>',\n        \"lucide:diamond\": '<path d=\"M12 2 22 12 12 22 2 12z\"/>',\n        \"lucide:hexagon\": '<path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"/>',\n        \"lucide:octagon\": '<polygon points=\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86\"/>',\n        \"lucide:arrow-up-right\": '<line x1=\"7\" y1=\"17\" x2=\"17\" y2=\"7\"/><polyline points=\"7,7 17,7 17,17\"/>',\n        \"lucide:arrow-down-left\": '<line x1=\"17\" y1=\"7\" x2=\"7\" y2=\"17\"/><polyline points=\"17,17 7,17 7,7\"/>',\n        \"lucide:chevron-right\": '<polyline points=\"9,18 15,12 9,6\"/>',\n        \"lucide:chevron-left\": '<polyline points=\"15,18 9,12 15,6\"/>',\n        \"lucide:chevron-up\": '<polyline points=\"18,15 12,9 6,15\"/>',\n        \"lucide:chevron-down\": '<polyline points=\"6,9 12,15 18,9\"/>',\n        \"lucide:move\": '<polyline points=\"5,9 2,12 5,15\"/><polyline points=\"9,5 12,2 15,5\"/><polyline points=\"15,19 12,22 9,19\"/><polyline points=\"19,9 22,12 19,15\"/><line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"/><line x1=\"12\" y1=\"2\" x2=\"12\" y2=\"22\"/>',\n        \"lucide:corner-down-right\": '<polyline points=\"15,10 20,15 15,20\"/><path d=\"M4 4v7a4 4 0 0 0 4 4h12\"/>',\n        \"lucide:menu\": '<line x1=\"4\" y1=\"12\" x2=\"20\" y2=\"12\"/><line x1=\"4\" y1=\"6\" x2=\"20\" y2=\"6\"/><line x1=\"4\" y1=\"18\" x2=\"20\" y2=\"18\"/>',\n        \"lucide:more-horizontal\": '<circle cx=\"12\" cy=\"12\" r=\"1\"/><circle cx=\"19\" cy=\"12\" r=\"1\"/><circle cx=\"5\" cy=\"12\" r=\"1\"/>',\n        \"lucide:more-vertical\": '<circle cx=\"12\" cy=\"12\" r=\"1\"/><circle cx=\"12\" cy=\"5\" r=\"1\"/><circle cx=\"12\" cy=\"19\" r=\"1\"/>',\n        \"lucide:filter\": '<polygon points=\"22,3 2,3 10,12.46 10,19 14,21 14,12.46\"/>',\n        \"lucide:grid\": '<rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"/><rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"/><rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"/><rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"/>',\n        \"lucide:list\": '<line x1=\"8\" y1=\"6\" x2=\"21\" y2=\"6\"/><line x1=\"8\" y1=\"12\" x2=\"21\" y2=\"12\"/><line x1=\"8\" y1=\"18\" x2=\"21\" y2=\"18\"/><line x1=\"3\" y1=\"6\" x2=\"3.01\" y2=\"6\"/><line x1=\"3\" y1=\"12\" x2=\"3.01\" y2=\"12\"/><line x1=\"3\" y1=\"18\" x2=\"3.01\" y2=\"18\"/>',\n        \"lucide:layout\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><line x1=\"3\" y1=\"9\" x2=\"21\" y2=\"9\"/><line x1=\"9\" y1=\"21\" x2=\"9\" y2=\"9\"/>',\n        \"lucide:check-circle\": '<path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"/><polyline points=\"22,4 12,14.01 9,11.01\"/>',\n        \"lucide:x-circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"/><line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"/>',\n        \"lucide:alert-circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"/><line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"/>',\n        \"lucide:info\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"12\"/><line x1=\"12\" y1=\"8\" x2=\"12.01\" y2=\"8\"/>',\n        \"lucide:help-circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\"/><line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\"/>',\n        \"lucide:rotate-ccw\": '<polyline points=\"1,4 1,10 7,10\"/><path d=\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\"/>',\n        \"lucide:undo\": '<polyline points=\"1,4 1,10 7,10\"/><path d=\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\"/>',\n        \"lucide:redo\": '<polyline points=\"23,4 23,10 17,10\"/><path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10\"/>',\n        \"lucide:file-text\": '<path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/><line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"/><line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"/><line x1=\"10\" y1=\"9\" x2=\"8\" y2=\"9\"/>',\n        \"lucide:folder-open\": '<path d=\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\"/><path d=\"M2 7h20\"/>',\n        \"lucide:clipboard\": '<rect x=\"8\" y=\"2\" width=\"8\" height=\"4\" rx=\"1\" ry=\"1\"/><path d=\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"/>',\n        \"lucide:paperclip\": '<path d=\"M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49\"/>',\n        // Communication icons\n        \"lucide:message-circle\": '<path d=\"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\"/>',\n        \"lucide:message-square\": '<path d=\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"/>',\n        \"lucide:phone-call\": '<path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"/><path d=\"M14.05 2a9 9 0 0 1 8 7.94\"/><path d=\"M14.05 6A5 5 0 0 1 18 10\"/>',\n        \"lucide:send\": '<line x1=\"22\" y1=\"2\" x2=\"11\" y2=\"13\"/><polygon points=\"22,2 15,22 11,13 2,9\"/>',\n        // Media icons\n        \"lucide:fast-forward\": '<polygon points=\"13,19 22,12 13,5\"/><polygon points=\"2,19 11,12 2,5\"/>',\n        \"lucide:rewind\": '<polygon points=\"11,19 2,12 11,5\"/><polygon points=\"22,19 13,12 22,5\"/>',\n        \"lucide:volume-x\": '<polygon points=\"11,5 6,9 2,9 2,15 6,15 11,19\"/><line x1=\"23\" y1=\"9\" x2=\"17\" y2=\"15\"/><line x1=\"17\" y1=\"9\" x2=\"23\" y2=\"15\"/>',\n        \"lucide:film\": '<rect x=\"2\" y=\"3\" width=\"20\" height=\"18\" rx=\"2\" ry=\"2\"/><line x1=\"7\" y1=\"3\" x2=\"7\" y2=\"21\"/><line x1=\"17\" y1=\"3\" x2=\"17\" y2=\"21\"/><line x1=\"2\" y1=\"9\" x2=\"7\" y2=\"9\"/><line x1=\"2\" y1=\"15\" x2=\"7\" y2=\"15\"/><line x1=\"17\" y1=\"9\" x2=\"22\" y2=\"9\"/><line x1=\"17\" y1=\"15\" x2=\"22\" y2=\"15\"/>',\n        // People icons\n        \"lucide:users\": '<path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"/><circle cx=\"9\" cy=\"7\" r=\"4\"/><path d=\"M22 21v-2a4 4 0 0 0-3-3.87\"/><path d=\"M16 3.13a4 4 0 0 1 0 7.75\"/>',\n        \"lucide:user-plus\": '<path d=\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/><circle cx=\"8.5\" cy=\"7\" r=\"4\"/><line x1=\"20\" y1=\"8\" x2=\"20\" y2=\"14\"/><line x1=\"23\" y1=\"11\" x2=\"17\" y2=\"11\"/>',\n        \"lucide:user-minus\": '<path d=\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/><circle cx=\"8.5\" cy=\"7\" r=\"4\"/><line x1=\"23\" y1=\"11\" x2=\"17\" y2=\"11\"/>',\n        \"lucide:user-check\": '<path d=\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/><circle cx=\"8.5\" cy=\"7\" r=\"4\"/><polyline points=\"17,11 19,13 23,9\"/>',\n        \"lucide:user-x\": '<path d=\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"/><circle cx=\"8.5\" cy=\"7\" r=\"4\"/><line x1=\"18\" y1=\"8\" x2=\"23\" y2=\"13\"/><line x1=\"23\" y1=\"8\" x2=\"18\" y2=\"13\"/>',\n        \"lucide:crown\": '<path d=\"M2 4l3 12h14l3-12-6 7-4-7-4 7-6-7zm0 16h20\"/>',\n        \"lucide:smile\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M8 14s1.5 2 4 2 4-2 4-2\"/><line x1=\"9\" y1=\"9\" x2=\"9.01\" y2=\"9\"/><line x1=\"15\" y1=\"9\" x2=\"15.01\" y2=\"9\"/>',\n        // Time icons\n        \"lucide:timer\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><polyline points=\"12,6 12,12 16,14\"/>',\n        \"lucide:alarm-clock\": '<circle cx=\"12\" cy=\"13\" r=\"8\"/><path d=\"M5 3 2 6\"/><path d=\"M22 6l-3-3\"/><path d=\"M6.38 18.7 4 21\"/><path d=\"M17.64 18.67 20 21\"/><path d=\"M9 13h6\"/>',\n        \"lucide:watch\": '<circle cx=\"12\" cy=\"12\" r=\"6\"/><polyline points=\"12,10 12,12 13,13\"/><path d=\"M16.13 7.66l-.81-4.05a2 2 0 0 0-2-1.61h-2.68a2 2 0 0 0-2 1.61l-.78 4.05\"/><path d=\"M7.88 16.36l.8 4a2 2 0 0 0 2 1.61h2.72a2 2 0 0 0 2-1.61l.81-4.05\"/>',\n        // Location icons\n        \"lucide:map-pin\": '<path d=\"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\"/><circle cx=\"12\" cy=\"10\" r=\"3\"/>',\n        \"lucide:navigation\": '<polygon points=\"3,11 22,2 13,21 11,13\"/>',\n        \"lucide:compass\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><polygon points=\"16.24,7.76 14.12,14.12 7.76,16.24 9.88,9.88\"/>',\n        // Transport icons\n        \"lucide:plane\": '<path d=\"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z\"/>',\n        \"lucide:train\": '<rect x=\"4\" y=\"6\" width=\"16\" height=\"10\" rx=\"2\" ry=\"2\"/><circle cx=\"8\" cy=\"14\" r=\"2\"/><circle cx=\"16\" cy=\"14\" r=\"2\"/><path d=\"M4 6V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v2\"/><path d=\"M2 18h2\"/><path d=\"M20 18h2\"/>',\n        \"lucide:bike\": '<circle cx=\"18.5\" cy=\"17.5\" r=\"3.5\"/><circle cx=\"5.5\" cy=\"17.5\" r=\"3.5\"/><circle cx=\"15\" cy=\"5\" r=\"1\"/><path d=\"M12 17.5V14l-3-3 4-3 2 3h2\"/>',\n        // Technology icons\n        \"lucide:bluetooth\": '<path d=\"M6.5 6.5 12 12l5.5-5.5L12 1 6.5 6.5zM17.5 17.5 12 12l-5.5 5.5L12 23l5.5-5.5z\"/>',\n        \"lucide:smartphone\": '<rect x=\"5\" y=\"2\" width=\"14\" height=\"20\" rx=\"2\" ry=\"2\"/><line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\"/>',\n        \"lucide:laptop\": '<path d=\"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16\"/>',\n        \"lucide:monitor\": '<rect x=\"2\" y=\"3\" width=\"20\" height=\"14\" rx=\"2\" ry=\"2\"/><line x1=\"8\" y1=\"21\" x2=\"16\" y2=\"21\"/><line x1=\"12\" y1=\"17\" x2=\"12\" y2=\"21\"/>',\n        \"lucide:printer\": '<polyline points=\"6,9 6,2 18,2 18,9\"/><path d=\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\"/><rect x=\"6\" y=\"14\" width=\"12\" height=\"8\"/>',\n        \"lucide:hard-drive\": '<line x1=\"22\" y1=\"12\" x2=\"2\" y2=\"12\"/><path d=\"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z\"/><line x1=\"6\" y1=\"16\" x2=\"6.01\" y2=\"16\"/><line x1=\"10\" y1=\"16\" x2=\"10.01\" y2=\"16\"/>',\n        \"lucide:server\": '<rect x=\"2\" y=\"2\" width=\"20\" height=\"8\" rx=\"2\" ry=\"2\"/><rect x=\"2\" y=\"14\" width=\"20\" height=\"8\" rx=\"2\" ry=\"2\"/><line x1=\"6\" y1=\"6\" x2=\"6.01\" y2=\"6\"/><line x1=\"6\" y1=\"18\" x2=\"6.01\" y2=\"18\"/>',\n        \"lucide:database\": '<ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"/><path d=\"M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5\"/><path d=\"M3 12c0 1.66 4.03 3 9 3s9-1.34 9-3\"/>',\n        // Commerce icons\n        \"lucide:shopping-cart\": '<circle cx=\"8\" cy=\"21\" r=\"1\"/><circle cx=\"19\" cy=\"21\" r=\"1\"/><path d=\"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12\"/>',\n        \"lucide:shopping-bag\": '<path d=\"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z\"/><line x1=\"3\" y1=\"6\" x2=\"21\" y2=\"6\"/><path d=\"M16 10a4 4 0 0 1-8 0\"/>',\n        \"lucide:credit-card\": '<rect x=\"1\" y=\"4\" width=\"22\" height=\"16\" rx=\"2\" ry=\"2\"/><line x1=\"1\" y1=\"10\" x2=\"23\" y2=\"10\"/>',\n        \"lucide:dollar-sign\": '<line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\"/><path d=\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"/>',\n        \"lucide:tag\": '<path d=\"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z\"/><path d=\"M7 7h.01\"/>',\n        \"lucide:gift\": '<polyline points=\"20,12 20,22 4,22 4,12\"/><rect x=\"2\" y=\"7\" width=\"20\" height=\"5\"/><line x1=\"12\" y1=\"22\" x2=\"12\" y2=\"7\"/><path d=\"M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z\"/><path d=\"M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z\"/>',\n        // Weather icons\n        \"lucide:sun\": '<circle cx=\"12\" cy=\"12\" r=\"5\"/><line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"3\"/><line x1=\"12\" y1=\"21\" x2=\"12\" y2=\"23\"/><line x1=\"4.22\" y1=\"4.22\" x2=\"5.64\" y2=\"5.64\"/><line x1=\"18.36\" y1=\"18.36\" x2=\"19.78\" y2=\"19.78\"/><line x1=\"1\" y1=\"12\" x2=\"3\" y2=\"12\"/><line x1=\"21\" y1=\"12\" x2=\"23\" y2=\"12\"/><line x1=\"4.22\" y1=\"19.78\" x2=\"5.64\" y2=\"18.36\"/><line x1=\"18.36\" y1=\"5.64\" x2=\"19.78\" y2=\"4.22\"/>',\n        \"lucide:moon\": '<path d=\"M12 3a6.364 6.364 0 0 0 9 9 9 9 0 1 1-9-9Z\"/>',\n        \"lucide:cloud\": '<path d=\"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z\"/>',\n        \"lucide:cloud-rain\": '<path d=\"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\"/><path d=\"M16 14v6m-4-6v6m-4-6v6\"/>',\n        \"lucide:cloud-snow\": '<path d=\"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\"/><path d=\"M8 15h.01M8 19h.01M12 17h.01M12 21h.01M16 15h.01M16 19h.01\"/>',\n        \"lucide:zap\": '<polygon points=\"13,2 3,14 12,14 11,22 21,10 12,10\"/>',\n        \"lucide:tree-pine\": '<path d=\"M17 14h.01M17 3v4.5l-2-2L13 7V3\"/><path d=\"M2 17h20l-2-4V9.5l-2.5-1.5L16 9.5V6l-2-2-2 2v3.5L10.5 8 8 9.5V13l-2 4Z\"/>',\n        \"lucide:flower\": '<path d=\"M12 7.5a4.5 4.5 0 1 1 4.5 4.5M12 7.5A4.5 4.5 0 1 0 7.5 12M12 7.5V9a3 3 0 0 0 3 3h1.5M12 7.5V9a3 3 0 0 1-3 3H7.5\"/><circle cx=\"12\" cy=\"12\" r=\"3\"/><path d=\"M12 16.5a4.5 4.5 0 1 1-4.5-4.5M12 16.5a4.5 4.5 0 1 0 4.5-4.5M12 16.5V15a3 3 0 0 0-3-3H7.5M12 16.5V15a3 3 0 0 1 3-3h1.5\"/>',\n        // Tools icons\n        \"lucide:scissors\": '<circle cx=\"6\" cy=\"6\" r=\"3\"/><circle cx=\"6\" cy=\"18\" r=\"3\"/><line x1=\"20\" y1=\"4\" x2=\"8.12\" y2=\"15.88\"/><line x1=\"14.47\" y1=\"14.48\" x2=\"20\" y2=\"20\"/><line x1=\"8.12\" y1=\"8.12\" x2=\"12\" y2=\"12\"/>',\n        \"lucide:wrench\": '<path d=\"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\"/>',\n        \"lucide:hammer\": '<path d=\"M15 12l-8.5-8.5c-.83-.83-2.17-.83-3 0 0 0 0 0 0 0l-1.5 1.5c-.83.83-.83 2.17 0 3l8.5 8.5\"/><path d=\"M17.64 15L22 10.64\"/><path d=\"M20.91 11.7l-1.25-1.25L21.91 8.2l2.5 2.5-3.5 1z\"/>',\n        \"lucide:key\": '<circle cx=\"7.5\" cy=\"15.5\" r=\"5.5\"/><path d=\"M21 2 17.6 5.4a7.5 7.5 0 1 1-3.4 3.4L2 21\"/>',\n        \"lucide:lock\": '<rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"/><circle cx=\"12\" cy=\"16\" r=\"1\"/><path d=\"M7 11V7a5 5 0 0 1 10 0v4\"/>',\n        \"lucide:unlock\": '<rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"/><circle cx=\"12\" cy=\"16\" r=\"1\"/><path d=\"M7 11V7a5 5 0 0 1 9.9-1\"/>',\n        \"lucide:shield\": '<path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>',\n        \"lucide:eye\": '<path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"/><circle cx=\"12\" cy=\"12\" r=\"3\"/>',\n        \"lucide:eye-off\": '<path d=\"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\"/><line x1=\"1\" y1=\"1\" x2=\"23\" y2=\"23\"/>'\n    };\n    return iconPaths[iconName] || '<rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2\" ry=\"2\" stroke-dasharray=\"2,2\"/>';\n};\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        addIcon: (iconName)=>{\n            if (!canvas) return;\n            // Check if the icon exists in our comprehensive library\n            const IconComponent = ICON_COMPONENTS[iconName];\n            if (!IconComponent) {\n                console.warn(\"Icon \".concat(iconName, \" not found in library\"));\n                return;\n            }\n            // Create SVG string using the proper Lucide icon\n            const iconSize = 80;\n            const iconColor = strokeColor || \"#000000\";\n            // Create a more reliable SVG generation approach\n            const svgString = \"data:image/svg+xml;charset=utf-8,\".concat(encodeURIComponent('\\n        <svg width=\"'.concat(iconSize, '\" height=\"').concat(iconSize, '\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"').concat(iconColor, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" xmlns=\"http://www.w3.org/2000/svg\">\\n          ').concat(getIconSVGPaths(iconName), \"\\n        </svg>\\n      \")));\n            // Create fabric image from the SVG data URL\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(svgString, (img)=>{\n                if (img && canvas) {\n                    img.set({\n                        left: 100,\n                        top: 100,\n                        scaleX: 1,\n                        scaleY: 1,\n                        // Allow full control over the icon\n                        lockScalingX: false,\n                        lockScalingY: false,\n                        lockUniScaling: false,\n                        lockMovementX: false,\n                        lockMovementY: false,\n                        lockRotation: false,\n                        // Ensure it's selectable and movable\n                        selectable: true,\n                        evented: true,\n                        // Add metadata for identification\n                        type: \"icon\",\n                        iconName: iconName,\n                        // Store original color for color changes\n                        originalColor: iconColor\n                    });\n                    addToCanvas(img);\n                }\n            });\n        },\n        // Icon-specific methods\n        changeIconColor: (color)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if (object.type === \"icon\" && object.iconName) {\n                    // Regenerate the SVG with new color\n                    const iconSize = Math.max(object.width || 80, object.height || 80);\n                    const svgString = \"data:image/svg+xml;charset=utf-8,\".concat(encodeURIComponent('\\n            <svg width=\"'.concat(iconSize, '\" height=\"').concat(iconSize, '\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"').concat(color, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" xmlns=\"http://www.w3.org/2000/svg\">\\n              ').concat(getIconSVGPaths(object.iconName), \"\\n            </svg>\\n          \")));\n                    // Update the image source\n                    fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(svgString, (newImg)=>{\n                        if (newImg && canvas) {\n                            // Preserve the current transformation\n                            const currentTransform = {\n                                left: object.left,\n                                top: object.top,\n                                scaleX: object.scaleX,\n                                scaleY: object.scaleY,\n                                angle: object.angle,\n                                flipX: object.flipX,\n                                flipY: object.flipY\n                            };\n                            // Remove old object and add new one\n                            canvas.remove(object);\n                            newImg.set({\n                                ...currentTransform,\n                                type: \"icon\",\n                                iconName: object.iconName,\n                                originalColor: color,\n                                selectable: true,\n                                evented: true\n                            });\n                            canvas.add(newImg);\n                            canvas.setActiveObject(newImg);\n                            canvas.renderAll();\n                            save();\n                        }\n                    });\n                }\n            });\n        },\n        changeIconSize: (width, height)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if (object.type === \"icon\") {\n                    object.set({\n                        scaleX: width / (object.width || 80),\n                        scaleY: height / (object.height || 80)\n                    });\n                }\n            });\n            canvas.renderAll();\n            save();\n        },\n        getActiveIconColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (selectedObject && selectedObject.type === \"icon\") {\n                return selectedObject.originalColor || strokeColor;\n            }\n            return strokeColor;\n        },\n        getActiveIconSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (selectedObject && selectedObject.type === \"icon\") {\n                const width = (selectedObject.width || 80) * (selectedObject.scaleX || 1);\n                const height = (selectedObject.height || 80) * (selectedObject.scaleY || 1);\n                return {\n                    width: Math.round(width),\n                    height: Math.round(height)\n                };\n            }\n            return {\n                width: 80,\n                height: 80\n            };\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback, setCanvasIsSelected } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback,\n        setCanvasIsSelected\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWVkaXRvci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDK0I7QUFtQjlCO0FBQ2dDO0FBTWhDO0FBQ2dDO0FBQ0s7QUFDRTtBQUNJO0FBQ0o7QUFDSTtBQUNOO0FBQzFCO0FBRTVDLHNEQUFzRDtBQUN0RCxNQUFNK0Isa0JBQTREO0lBQ2hFLGdCQUFnQkQscURBQWlCO0lBQ2pDLGVBQWVBLHFEQUFnQjtJQUMvQixzQkFBc0JBLHFEQUFzQjtJQUM1QyxxQkFBcUJBLHFEQUFxQjtJQUMxQyxtQkFBbUJBLHFEQUFtQjtJQUN0QyxxQkFBcUJBLHFEQUFxQjtJQUMxQyxlQUFlQSxxREFBZ0I7SUFDL0IsZUFBZUEscURBQWdCO0lBQy9CLG1CQUFtQkEscURBQW9CO0lBQ3ZDLGVBQWVBLHFEQUFnQjtJQUMvQixnQkFBZ0JBLHFEQUFpQjtJQUNqQyxjQUFjQSxxREFBZTtJQUM3QixpQkFBaUJBLHFEQUFrQjtJQUNuQyxnQkFBZ0JBLHFEQUFpQjtJQUNqQyxnQkFBZ0JBLHFEQUFpQjtJQUNqQyxnQkFBZ0JBLHFEQUFpQjtJQUNqQyxlQUFlQSxxREFBZ0I7SUFDL0IsaUJBQWlCQSxxREFBa0I7SUFDbkMsaUJBQWlCQSxxREFBa0I7SUFDbkMsZUFBZUEscURBQWdCO0lBQy9CLGdCQUFnQkEscURBQWlCO0lBQ2pDLFlBQVlBLHFEQUFhO0lBQ3pCLGdCQUFnQkEscURBQWlCO0lBQ2pDLGVBQWVBLHFEQUFnQjtJQUMvQixnQkFBZ0JBLHFEQUFrQjtJQUNsQyxtQkFBbUJBLHFEQUFvQjtJQUN2QyxpQkFBaUJBLHFEQUFrQjtJQUNuQyxnQkFBZ0JBLHFEQUFpQjtJQUNqQyxlQUFlQSxxREFBZ0I7SUFDL0IsZUFBZUEscURBQWdCO0lBQy9CLGdCQUFnQkEscURBQW1CO0lBQ25DLG1CQUFtQkEscURBQW9CO0lBQ3ZDLGdCQUFnQkEscURBQWlCO0lBQ2pDLGNBQWNBLHFEQUFlO0lBQzdCLGdCQUFnQkEscURBQWlCO0lBQ2pDLGVBQWVBLHFEQUFnQjtJQUMvQixrQkFBa0JBLHFEQUFtQjtJQUNyQyxpQkFBaUJBLHFEQUFtQjtJQUNwQyxlQUFlQSxxREFBZ0I7SUFDL0IsZ0JBQWdCQSxxREFBaUI7SUFDakMsZUFBZUEscURBQWtCO0lBQ2pDLHVCQUF1QkEscURBQXVCO0lBQzlDLG9CQUFvQkEscURBQW9CO0lBQ3hDLGtCQUFrQkEscURBQXFCO0lBQ3ZDLG1CQUFtQkEscURBQW9CO0lBQ3ZDLG1CQUFtQkEscURBQW9CO0lBQ3ZDLGNBQWNBLHFEQUFlO0lBQzdCLGtCQUFrQkEscURBQWtCO0lBQ3BDLGVBQWVBLHFEQUFnQjtJQUMvQixpQkFBaUJBLHFEQUFrQjtJQUNuQyxpQkFBaUJBLHFEQUFrQjtJQUNuQyxnQkFBZ0JBLHFEQUF5QjtJQUN6QyxlQUFlQSxxREFBZ0I7SUFDL0IsZUFBZUEscURBQXNCO0lBQ3JDLG1CQUFtQkEscURBQW9CO0lBQ3ZDLGNBQWNBLHFEQUFlO0lBQzdCLGVBQWVBLHFEQUFnQjtJQUMvQixvQkFBb0JBLHFEQUFvQjtJQUN4QyxzQkFBc0JBLHFEQUFzQjtJQUM1QyxrQkFBa0JBLHFEQUF5QjtJQUMzQyxlQUFlQSxxREFBZ0I7SUFDL0IsZUFBZUEscURBQWdCO0lBQy9CLGVBQWVBLHFEQUFnQjtJQUMvQix3QkFBd0JBLHFEQUF3QjtJQUNoRCxzQkFBc0JBLHFEQUFzQjtJQUM1QyxzQkFBc0JBLHFEQUFzQjtJQUM1QyxzQkFBc0JBLHFEQUFzQjtJQUM1Qyx3QkFBd0JBLHFEQUF3QjtJQUNoRCxvQkFBb0JBLHFEQUFxQjtJQUN6QyxvQkFBb0JBLHFEQUFvQjtJQUN4QyxtQkFBbUJBLHFEQUFvQjtJQUN2QyxjQUFjQSxxREFBZTtJQUM3QixjQUFjQSxxREFBZTtJQUM3QixlQUFlQSxxREFBZ0I7SUFDL0IsZ0JBQWdCQSxxREFBaUI7SUFDakMsbUJBQW1CQSxxREFBb0I7SUFDdkMsb0JBQW9CQSxxREFBcUI7SUFDekMsa0JBQWtCQSxxREFBb0I7SUFDdEMsZ0JBQWdCQSxxREFBaUI7SUFDakMsZUFBZUEscURBQWdCO0lBQy9CLGVBQWVBLHFEQUFpQjtJQUNoQyxpQkFBaUJBLHFEQUFrQjtJQUNuQyxpQkFBaUJBLHFEQUFrQjtJQUNuQyxnQkFBZ0JBLHFEQUFpQjtJQUNqQyxtQkFBbUJBLHFEQUFvQjtJQUN2QyxlQUFlQSxxREFBZ0I7SUFDL0Isa0JBQWtCQSxxREFBb0I7SUFDdEMscUJBQXFCQSxxREFBc0I7SUFDM0MscUJBQXFCQSxzREFBc0I7SUFDM0MsaUJBQWlCQSxzREFBa0I7SUFDbkMsa0JBQWtCQSxzREFBbUI7SUFDckMsbUJBQW1CQSxzREFBb0I7SUFDdkMsZ0JBQWdCQSxzREFBaUI7SUFDakMsa0JBQWtCQSxxREFBbUI7SUFDckMscUJBQXFCQSxzREFBcUI7SUFDMUMsY0FBY0Esc0RBQWU7SUFDN0Isb0JBQW9CQSxzREFBcUI7SUFDekMsZ0JBQWdCQSxzREFBaUI7SUFDakMsYUFBYUEsc0RBQWM7SUFDM0IsZ0JBQWdCQSxzREFBaUI7SUFDakMsZ0JBQWdCQSxzREFBaUI7SUFDakMsY0FBY0Esc0RBQWU7SUFDN0IsZUFBZUEsc0RBQWdCO0lBQy9CLGVBQWVBLHNEQUFnQjtJQUMvQixpQkFBaUJBLHNEQUFrQjtJQUNuQyxrQkFBa0JBLHNEQUFtQjtJQUNyQyxrQkFBa0JBLHNEQUFrQjtJQUNwQyxxQkFBcUJBLHNEQUFzQjtJQUMzQyxtQkFBbUJBLHNEQUFvQjtJQUN2QyxpQkFBaUJBLHNEQUFrQjtJQUNuQyxtQkFBbUJBLHNEQUFvQjtJQUN2QyxnQkFBZ0JBLHNEQUFpQjtJQUNqQyxrQkFBa0JBLHNEQUFtQjtJQUNyQyxlQUFlQSxzREFBZ0I7SUFDL0IsbUJBQW1CQSxzREFBb0I7SUFDdkMsZ0JBQWdCQSxzREFBaUI7SUFDakMsa0JBQWtCQSxzREFBbUI7SUFDckMsaUJBQWlCQSxzREFBa0I7QUFDckM7QUFFQSwrRUFBK0U7QUFDL0UsTUFBTXVILGtCQUFrQixDQUFDQztJQUN2QixNQUFNQyxZQUFvQztRQUN4QyxnQkFBaUI7UUFDakIsZUFBZ0I7UUFDaEIsc0JBQXVCO1FBQ3ZCLHFCQUFzQjtRQUN0QixtQkFBb0I7UUFDcEIscUJBQXNCO1FBQ3RCLGVBQWdCO1FBQ2hCLGVBQWdCO1FBQ2hCLG1CQUFvQjtRQUNwQixlQUFnQjtRQUNoQixnQkFBaUI7UUFDakIsY0FBZTtRQUNmLGlCQUFrQjtRQUNsQixnQkFBaUI7UUFDakIsZ0JBQWlCO1FBQ2pCLGdCQUFpQjtRQUNqQixlQUFnQjtRQUNoQixpQkFBa0I7UUFDbEIsaUJBQWtCO1FBQ2xCLGVBQWdCO1FBQ2hCLGdCQUFpQjtRQUNqQixZQUFhO1FBQ2IsZ0JBQWlCO1FBQ2pCLGVBQWdCO1FBQ2hCLGdCQUFpQjtRQUNqQixtQkFBb0I7UUFDcEIsaUJBQWtCO1FBQ2xCLGdCQUFpQjtRQUNqQixlQUFnQjtRQUNoQixlQUFnQjtRQUNoQixnQkFBaUI7UUFDakIsbUJBQW9CO1FBQ3BCLGdCQUFpQjtRQUNqQixjQUFlO1FBQ2YsZ0JBQWlCO1FBQ2pCLGVBQWdCO1FBQ2hCLGtCQUFtQjtRQUNuQixpQkFBa0I7UUFDbEIsZUFBZ0I7UUFDaEIsZ0JBQWlCO1FBQ2pCLGVBQWdCO1FBQ2hCLHVCQUF3QjtRQUN4QixvQkFBcUI7UUFDckIsa0JBQW1CO1FBQ25CLG1CQUFtQjtRQUNuQixpQkFBa0I7UUFDbEIsbUJBQW9CO1FBQ3BCLGtCQUFtQjtRQUNuQixrQkFBbUI7UUFDbkIsa0JBQW1CO1FBQ25CLHlCQUEwQjtRQUMxQiwwQkFBMkI7UUFDM0Isd0JBQXlCO1FBQ3pCLHVCQUF3QjtRQUN4QixxQkFBc0I7UUFDdEIsdUJBQXdCO1FBQ3hCLGVBQWdCO1FBQ2hCLDRCQUE2QjtRQUM3QixlQUFnQjtRQUNoQiwwQkFBMkI7UUFDM0Isd0JBQXlCO1FBQ3pCLGlCQUFrQjtRQUNsQixlQUFnQjtRQUNoQixlQUFnQjtRQUNoQixpQkFBa0I7UUFDbEIsdUJBQXdCO1FBQ3hCLG1CQUFvQjtRQUNwQix1QkFBd0I7UUFDeEIsZUFBZ0I7UUFDaEIsc0JBQXVCO1FBQ3ZCLHFCQUFzQjtRQUN0QixlQUFnQjtRQUNoQixlQUFnQjtRQUNoQixvQkFBcUI7UUFDckIsc0JBQXVCO1FBQ3ZCLG9CQUFxQjtRQUNyQixvQkFBcUI7UUFDckIsc0JBQXNCO1FBQ3RCLHlCQUEwQjtRQUMxQix5QkFBMEI7UUFDMUIscUJBQXNCO1FBQ3RCLGVBQWdCO1FBQ2hCLGNBQWM7UUFDZCx1QkFBd0I7UUFDeEIsaUJBQWtCO1FBQ2xCLG1CQUFvQjtRQUNwQixlQUFnQjtRQUNoQixlQUFlO1FBQ2YsZ0JBQWlCO1FBQ2pCLG9CQUFxQjtRQUNyQixxQkFBc0I7UUFDdEIscUJBQXNCO1FBQ3RCLGlCQUFrQjtRQUNsQixnQkFBaUI7UUFDakIsZ0JBQWlCO1FBQ2pCLGFBQWE7UUFDYixnQkFBaUI7UUFDakIsc0JBQXVCO1FBQ3ZCLGdCQUFpQjtRQUNqQixpQkFBaUI7UUFDakIsa0JBQW1CO1FBQ25CLHFCQUFzQjtRQUN0QixrQkFBbUI7UUFDbkIsa0JBQWtCO1FBQ2xCLGdCQUFpQjtRQUNqQixnQkFBaUI7UUFDakIsZUFBZ0I7UUFDaEIsbUJBQW1CO1FBQ25CLG9CQUFxQjtRQUNyQixxQkFBc0I7UUFDdEIsaUJBQWtCO1FBQ2xCLGtCQUFtQjtRQUNuQixrQkFBbUI7UUFDbkIscUJBQXNCO1FBQ3RCLGlCQUFrQjtRQUNsQixtQkFBb0I7UUFDcEIsaUJBQWlCO1FBQ2pCLHdCQUF5QjtRQUN6Qix1QkFBd0I7UUFDeEIsc0JBQXVCO1FBQ3ZCLHNCQUF1QjtRQUN2QixjQUFlO1FBQ2YsZUFBZ0I7UUFDaEIsZ0JBQWdCO1FBQ2hCLGNBQWU7UUFDZixlQUFnQjtRQUNoQixnQkFBaUI7UUFDakIscUJBQXNCO1FBQ3RCLHFCQUFzQjtRQUN0QixjQUFlO1FBQ2Ysb0JBQXFCO1FBQ3JCLGlCQUFrQjtRQUNsQixjQUFjO1FBQ2QsbUJBQW9CO1FBQ3BCLGlCQUFrQjtRQUNsQixpQkFBa0I7UUFDbEIsY0FBZTtRQUNmLGVBQWdCO1FBQ2hCLGlCQUFrQjtRQUNsQixpQkFBa0I7UUFDbEIsY0FBZTtRQUNmLGtCQUFtQjtJQUNyQjtJQUVBLE9BQU9BLFNBQVMsQ0FBQ0QsU0FBUyxJQUFLO0FBQ2pDO0FBRUEsTUFBTUUsY0FBYztRQUFDLEVBQ25CQyxJQUFJLEVBQ0pDLElBQUksRUFDSkMsSUFBSSxFQUNKQyxPQUFPLEVBQ1BDLE9BQU8sRUFDUEMsUUFBUSxFQUNSQyxJQUFJLEVBQ0pDLEtBQUssRUFDTEMsTUFBTSxFQUNOQyxTQUFTLEVBQ1RDLFVBQVUsRUFDVkMsYUFBYSxFQUNiQyxZQUFZLEVBQ1pDLFdBQVcsRUFDWEMsY0FBYyxFQUNkQyxXQUFXLEVBQ1hDLGNBQWMsRUFDZEMsZUFBZSxFQUNmQyxlQUFlLEVBQ2ZDLGtCQUFrQixFQUNEO0lBQ2pCLE1BQU1DLHNCQUFzQjtRQUMxQixNQUFNLEVBQUVDLEtBQUssRUFBRUMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEdBQUcsRUFBRSxHQUFHQztRQUVyQyxPQUFPO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RQO1lBQ0FDO1lBQ0FDO1lBQ0FDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1LLFVBQVU7UUFDZCxNQUFNQyxVQUFVVjtRQUVoQlosT0FBT3VCLG9CQUFvQixDQUFDO1lBQUM7WUFBRztZQUFHO1lBQUc7WUFBRztZQUFHO1NBQUU7UUFDOUMsTUFBTUMsVUFBVXhCLE9BQU95QixTQUFTLENBQUNIO1FBRWpDbkssb0VBQVlBLENBQUNxSyxTQUFTO1FBQ3RCM0I7SUFDRjtJQUVBLE1BQU02QixVQUFVO1FBQ2QsTUFBTUosVUFBVVY7UUFFaEJaLE9BQU91QixvQkFBb0IsQ0FBQztZQUFDO1lBQUc7WUFBRztZQUFHO1lBQUc7WUFBRztTQUFFO1FBQzlDLE1BQU1DLFVBQVV4QixPQUFPeUIsU0FBUyxDQUFDSDtRQUVqQ25LLG9FQUFZQSxDQUFDcUssU0FBUztRQUN0QjNCO0lBQ0Y7SUFFQSxNQUFNOEIsVUFBVTtRQUNkLE1BQU1MLFVBQVVWO1FBRWhCWixPQUFPdUIsb0JBQW9CLENBQUM7WUFBQztZQUFHO1lBQUc7WUFBRztZQUFHO1lBQUc7U0FBRTtRQUM5QyxNQUFNQyxVQUFVeEIsT0FBT3lCLFNBQVMsQ0FBQ0g7UUFFakNuSyxvRUFBWUEsQ0FBQ3FLLFNBQVM7UUFDdEIzQjtJQUNGO0lBRUEsTUFBTStCLFdBQVc7UUFDZixNQUFNSixVQUFVeEIsT0FBTzZCLE1BQU0sQ0FBQzdLLDZEQUFTQTtRQUV2QyxNQUFNSyxxRUFBYUEsQ0FBQ21LLFFBQVFNLE9BQU87UUFDbkMsTUFBTUMsYUFBYSxnQ0FFakIsT0FGaURDLG1CQUNqREMsS0FBS0MsU0FBUyxDQUFDVixTQUFTLE1BQU07UUFFaENySyxvRUFBWUEsQ0FBQzRLLFlBQVk7SUFDM0I7SUFFQSxNQUFNSSxXQUFXLENBQUNDO1FBQ2hCLE1BQU1DLE9BQU9KLEtBQUtLLEtBQUssQ0FBQ0Y7UUFFeEJwQyxPQUFPdUMsWUFBWSxDQUFDRixNQUFNO1lBQ3hCeEM7UUFDRjtJQUNGO0lBRUEsTUFBTW9CLGVBQWU7UUFDbkIsT0FBT2pCLE9BQ053QyxVQUFVLEdBQ1ZDLElBQUksQ0FBQyxDQUFDQyxTQUFXQSxPQUFPeEIsSUFBSSxLQUFLO0lBQ3BDO0lBRUEsTUFBTXlCLFNBQVMsQ0FBQ0Q7UUFDZCxNQUFNRSxZQUFZM0I7UUFDbEIsTUFBTTBCLFNBQVNDLHNCQUFBQSxnQ0FBQUEsVUFBV0MsY0FBYztRQUV4QyxJQUFJLENBQUNGLFFBQVE7UUFFYixhQUFhO1FBQ2IzQyxPQUFPOEMsYUFBYSxDQUFDSixRQUFRQztJQUMvQjtJQUVBLE1BQU1JLGNBQWMsQ0FBQ0w7UUFDbkJDLE9BQU9EO1FBQ1AxQyxPQUFPZ0QsR0FBRyxDQUFDTjtRQUNYMUMsT0FBT2lELGVBQWUsQ0FBQ1A7SUFDekI7SUFFQSxPQUFPO1FBQ0xyQjtRQUNBTTtRQUNBRDtRQUNBRTtRQUNBTztRQUNBdkM7UUFDQUQ7UUFDQUU7UUFDQW9CO1FBQ0FpQyxRQUFRO1lBQ04sSUFBSUMsWUFBWW5ELE9BQU9vRCxPQUFPO1lBQzlCRCxhQUFhO1lBQ2IsTUFBTVIsU0FBUzNDLE9BQU9xRCxTQUFTO1lBQy9CckQsT0FBT3NELFdBQVcsQ0FDaEIsSUFBSXZOLDBDQUFNQSxDQUFDd04sS0FBSyxDQUFDWixPQUFPNUIsSUFBSSxFQUFFNEIsT0FBTzNCLEdBQUcsR0FDeENtQyxZQUFZLElBQUksSUFBSUE7UUFFeEI7UUFDQUssU0FBUztZQUNQLElBQUlMLFlBQVluRCxPQUFPb0QsT0FBTztZQUM5QkQsYUFBYTtZQUNiLE1BQU1SLFNBQVMzQyxPQUFPcUQsU0FBUztZQUMvQnJELE9BQU9zRCxXQUFXLENBQ2hCLElBQUl2TiwwQ0FBTUEsQ0FBQ3dOLEtBQUssQ0FBQ1osT0FBTzVCLElBQUksRUFBRTRCLE9BQU8zQixHQUFHLEdBQ3hDbUMsWUFBWSxNQUFNLE1BQU1BO1FBRTVCO1FBQ0FNLFlBQVksQ0FBQ0M7WUFDWCxNQUFNZCxZQUFZM0I7WUFFbEIyQixzQkFBQUEsZ0NBQUFBLFVBQVdlLEdBQUcsQ0FBQ0Q7WUFDZjdEO1lBQ0FMO1FBQ0Y7UUFDQW9FLGtCQUFrQixDQUFDRjtZQUNqQixNQUFNZCxZQUFZM0I7WUFDbEIyQixzQkFBQUEsZ0NBQUFBLFVBQVdlLEdBQUcsQ0FBQztnQkFBRUUsTUFBTUg7WUFBTTtZQUM3QjFELE9BQU84RCxTQUFTO1lBQ2hCdEU7UUFDRjtRQUNBdUUsbUJBQW1CO1lBQ2pCL0QsT0FBT2dFLG1CQUFtQjtZQUMxQmhFLE9BQU84RCxTQUFTO1lBQ2hCOUQsT0FBT2lFLGFBQWEsR0FBRztZQUN2QmpFLE9BQU9rRSxnQkFBZ0IsQ0FBQ3JELEtBQUssR0FBR047WUFDaENQLE9BQU9rRSxnQkFBZ0IsQ0FBQ0MsS0FBSyxHQUFHOUQ7UUFDbEM7UUFDQStELG9CQUFvQjtZQUNsQnBFLE9BQU9pRSxhQUFhLEdBQUc7UUFDekI7UUFDQUksUUFBUSxJQUFNNUU7UUFDZDZFLFFBQVEsSUFBTTVFO1FBQ2Q2RSxRQUFRLElBQU16RTtRQUNkMEUsU0FBUyxJQUFNekU7UUFDZjBFLG1CQUFtQixDQUFDZjtZQUNsQixNQUFNNUIsVUFBVTlCLE9BQU8wRSxnQkFBZ0I7WUFDdkM1QyxRQUFRNkMsT0FBTyxDQUFDLENBQUNqQztnQkFDZixJQUFJQSxPQUFPa0MsSUFBSSxLQUFLLFNBQVM7b0JBQzNCLE1BQU1DLGNBQWNuQztvQkFFcEIsTUFBTW9DLFNBQVM1TixvRUFBWUEsQ0FBQ3dNO29CQUU1Qm1CLFlBQVlFLE9BQU8sR0FBR0QsU0FBUzt3QkFBQ0E7cUJBQU8sR0FBRyxFQUFFO29CQUM1Q0QsWUFBWUcsWUFBWTtvQkFDeEJoRixPQUFPOEQsU0FBUztnQkFDbEI7WUFDRjtRQUNGO1FBQ0FtQixVQUFVLENBQUN2QjtZQUNUM04sMENBQU1BLENBQUMrQyxLQUFLLENBQUNvTSxPQUFPLENBQ2xCeEIsT0FDQSxDQUFDeUI7Z0JBQ0MsTUFBTXZDLFlBQVkzQjtnQkFFbEJrRSxNQUFNQyxZQUFZLENBQUN4QyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVcvQixLQUFLLEtBQUk7Z0JBQ3ZDc0UsTUFBTUUsYUFBYSxDQUFDekMsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXOUIsTUFBTSxLQUFJO2dCQUV6Q2lDLFlBQVlvQztZQUNkLEdBQ0E7Z0JBQ0VHLGFBQWE7WUFDZjtRQUVKO1FBQ0FDLFFBQVE7WUFDTnZGLE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQyxTQUFXMUMsT0FBT3dGLE1BQU0sQ0FBQzlDO1lBQzVEMUMsT0FBT2dFLG1CQUFtQjtZQUMxQmhFLE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0EyQixTQUFTLENBQUMvQixPQUFPcEM7WUFDZixNQUFNb0IsU0FBUyxJQUFJM00sMENBQU1BLENBQUMyUCxPQUFPLENBQUNoQyxPQUFPO2dCQUN2QyxHQUFHOU0sZ0VBQVk7Z0JBQ2ZpTixNQUFNNUQ7Z0JBQ04sR0FBR3FCLE9BQU87WUFDWjtZQUVBeUIsWUFBWUw7UUFDZDtRQUNBaUQsa0JBQWtCO1lBQ2hCLE1BQU1DLGlCQUFpQm5GLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ21GLGdCQUFnQjtnQkFDbkIsT0FBTztZQUNUO1lBRUEsTUFBTWxDLFFBQVFrQyxlQUFlQyxHQUFHLENBQUMsY0FBYztZQUUvQyxPQUFPbkM7UUFDVDtRQUNBb0MsZ0JBQWdCLENBQUNwQztZQUNmMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJdEwsa0VBQVVBLENBQUNzTCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQixhQUFhO29CQUNiLHNDQUFzQztvQkFDdENsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFb0MsVUFBVXJDO29CQUFNO2dCQUMvQjtZQUNGO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBa0MsbUJBQW1CO1lBQ2pCLE1BQU1KLGlCQUFpQm5GLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ21GLGdCQUFnQjtnQkFDbkIsT0FBTzdPLDZEQUFTQTtZQUNsQjtZQUVBLGFBQWE7WUFDYixzQ0FBc0M7WUFDdEMsTUFBTTJNLFFBQVFrQyxlQUFlQyxHQUFHLENBQUMsZUFBZTlPLDZEQUFTQTtZQUV6RCxPQUFPMk07UUFDVDtRQUNBdUMsaUJBQWlCLENBQUN2QztZQUNoQjFELE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQztnQkFDakMsSUFBSXRMLGtFQUFVQSxDQUFDc0wsT0FBT2tDLElBQUksR0FBRztvQkFDM0IsYUFBYTtvQkFDYix1Q0FBdUM7b0JBQ3ZDbEMsT0FBT2lCLEdBQUcsQ0FBQzt3QkFBRXVDLFdBQVd4QztvQkFBTTtnQkFDaEM7WUFDRjtZQUNBMUQsT0FBTzhELFNBQVM7UUFDbEI7UUFDQXFDLG9CQUFvQjtZQUNsQixNQUFNUCxpQkFBaUJuRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNtRixnQkFBZ0I7Z0JBQ25CLE9BQU87WUFDVDtZQUVBLGFBQWE7WUFDYix1Q0FBdUM7WUFDdkMsTUFBTWxDLFFBQVFrQyxlQUFlQyxHQUFHLENBQUMsZ0JBQWdCO1lBRWpELE9BQU9uQztRQUNUO1FBQ0EwQyxxQkFBcUIsQ0FBQzFDO1lBQ3BCMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJdEwsa0VBQVVBLENBQUNzTCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQixhQUFhO29CQUNiLHVDQUF1QztvQkFDdkNsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFMEMsV0FBVzNDO29CQUFNO2dCQUNoQztZQUNGO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBd0Msd0JBQXdCO1lBQ3RCLE1BQU1WLGlCQUFpQm5GLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ21GLGdCQUFnQjtnQkFDbkIsT0FBTztZQUNUO1lBRUEsYUFBYTtZQUNiLHVDQUF1QztZQUN2QyxNQUFNbEMsUUFBUWtDLGVBQWVDLEdBQUcsQ0FBQyxnQkFBZ0I7WUFFakQsT0FBT25DO1FBQ1Q7UUFDQTZDLHVCQUF1QixDQUFDN0M7WUFDdEIxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDLElBQUl0TCxrRUFBVUEsQ0FBQ3NMLE9BQU9rQyxJQUFJLEdBQUc7b0JBQzNCLGFBQWE7b0JBQ2IseUNBQXlDO29CQUN6Q2xDLE9BQU9pQixHQUFHLENBQUM7d0JBQUU2QyxhQUFhOUM7b0JBQU07Z0JBQ2xDO1lBQ0Y7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0EyQywwQkFBMEI7WUFDeEIsTUFBTWIsaUJBQWlCbkYsZUFBZSxDQUFDLEVBQUU7WUFFekMsSUFBSSxDQUFDbUYsZ0JBQWdCO2dCQUNuQixPQUFPO1lBQ1Q7WUFFQSxhQUFhO1lBQ2IseUNBQXlDO1lBQ3pDLE1BQU1sQyxRQUFRa0MsZUFBZUMsR0FBRyxDQUFDLGtCQUFrQjtZQUVuRCxPQUFPbkM7UUFDVDtRQUNBZ0QsaUJBQWlCLENBQUNoRDtZQUNoQjFELE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQztnQkFDakMsSUFBSXRMLGtFQUFVQSxDQUFDc0wsT0FBT2tDLElBQUksR0FBRztvQkFDM0IsYUFBYTtvQkFDYix1Q0FBdUM7b0JBQ3ZDbEMsT0FBT2lCLEdBQUcsQ0FBQzt3QkFBRWdELFdBQVdqRDtvQkFBTTtnQkFDaEM7WUFDRjtZQUNBMUQsT0FBTzhELFNBQVM7UUFDbEI7UUFDQThDLG9CQUFvQjtZQUNsQixNQUFNaEIsaUJBQWlCbkYsZUFBZSxDQUFDLEVBQUU7WUFFekMsSUFBSSxDQUFDbUYsZ0JBQWdCO2dCQUNuQixPQUFPO1lBQ1Q7WUFFQSxhQUFhO1lBQ2IsdUNBQXVDO1lBQ3ZDLE1BQU1sQyxRQUFRa0MsZUFBZUMsR0FBRyxDQUFDLGdCQUFnQjtZQUVqRCxPQUFPbkM7UUFDVDtRQUNBbUQsa0JBQWtCLENBQUNuRDtZQUNqQjFELE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQztnQkFDakMsSUFBSXRMLGtFQUFVQSxDQUFDc0wsT0FBT2tDLElBQUksR0FBRztvQkFDM0IsYUFBYTtvQkFDYix3Q0FBd0M7b0JBQ3hDbEMsT0FBT2lCLEdBQUcsQ0FBQzt3QkFBRW1ELFlBQVlwRDtvQkFBTTtnQkFDakM7WUFDRjtZQUNBMUQsT0FBTzhELFNBQVM7UUFDbEI7UUFDQWlELGVBQWUsQ0FBQ3JEO1lBQ2QxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDQSxPQUFPaUIsR0FBRyxDQUFDO29CQUFFcUQsU0FBU3REO2dCQUFNO1lBQzlCO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBbUQsY0FBYztZQUNaakgsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQzFDLE9BQU9pSCxZQUFZLENBQUN2RTtZQUN0QjtZQUVBMUMsT0FBTzhELFNBQVM7WUFFaEIsTUFBTWxCLFlBQVkzQjtZQUNsQjJCLHNCQUFBQSxnQ0FBQUEsVUFBV3NFLFVBQVU7UUFDdkI7UUFDQUMsZUFBZTtZQUNibkgsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQzFDLE9BQU9tSCxhQUFhLENBQUN6RTtZQUN2QjtZQUVBMUMsT0FBTzhELFNBQVM7WUFDaEIsTUFBTWxCLFlBQVkzQjtZQUNsQjJCLHNCQUFBQSxnQ0FBQUEsVUFBV3NFLFVBQVU7UUFDdkI7UUFDQUUsa0JBQWtCLENBQUMxRDtZQUNqQnZELGNBQWN1RDtZQUNkMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJdEwsa0VBQVVBLENBQUNzTCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQixhQUFhO29CQUNiLHdDQUF3QztvQkFDeENsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFekQsWUFBWXdEO29CQUFNO2dCQUNqQztZQUNGO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBdUQsaUJBQWlCLENBQUMzRDtZQUNoQnRELGFBQWFzRDtZQUNiMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQ0EsT0FBT2lCLEdBQUcsQ0FBQztvQkFBRUUsTUFBTUg7Z0JBQU07WUFDM0I7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0F3RCxtQkFBbUIsQ0FBQzVEO1lBQ2xCcEQsZUFBZW9EO1lBQ2YxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDLCtCQUErQjtnQkFDL0IsSUFBSXRMLGtFQUFVQSxDQUFDc0wsT0FBT2tDLElBQUksR0FBRztvQkFDM0JsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFRSxNQUFNSDtvQkFBTTtvQkFDekI7Z0JBQ0Y7Z0JBRUFoQixPQUFPaUIsR0FBRyxDQUFDO29CQUFFNEQsUUFBUTdEO2dCQUFNO1lBQzdCO1lBQ0ExRCxPQUFPa0UsZ0JBQWdCLENBQUNDLEtBQUssR0FBR1Q7WUFDaEMxRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBMEQsbUJBQW1CLENBQUM5RDtZQUNsQmxELGVBQWVrRDtZQUNmMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQ0EsT0FBT2lCLEdBQUcsQ0FBQztvQkFBRXBELGFBQWFtRDtnQkFBTTtZQUNsQztZQUNBMUQsT0FBT2tFLGdCQUFnQixDQUFDckQsS0FBSyxHQUFHNkM7WUFDaEMxRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBMkQsdUJBQXVCLENBQUMvRDtZQUN0Qi9DLG1CQUFtQitDO1lBQ25CMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQ0EsT0FBT2lCLEdBQUcsQ0FBQztvQkFBRWpELGlCQUFpQmdEO2dCQUFNO1lBQ3RDO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBNEQsV0FBVztZQUNULE1BQU1oRixTQUFTLElBQUkzTSwwQ0FBTUEsQ0FBQzRSLE1BQU0sQ0FBQztnQkFDL0IsR0FBR3BSLGtFQUFjO2dCQUNqQnNOLE1BQU01RDtnQkFDTnNILFFBQVFsSDtnQkFDUkUsYUFBYUE7Z0JBQ2JHLGlCQUFpQkE7WUFDbkI7WUFFQXFDLFlBQVlMO1FBQ2Q7UUFDQWtGLGtCQUFrQjtZQUNoQixNQUFNbEYsU0FBUyxJQUFJM00sMENBQU1BLENBQUM4UixJQUFJLENBQUM7Z0JBQzdCLEdBQUduUixxRUFBaUI7Z0JBQ3BCb1IsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSmxFLE1BQU01RDtnQkFDTnNILFFBQVFsSDtnQkFDUkUsYUFBYUE7Z0JBQ2JHLGlCQUFpQkE7WUFDbkI7WUFFQXFDLFlBQVlMO1FBQ2Q7UUFDQXNGLGNBQWM7WUFDWixNQUFNdEYsU0FBUyxJQUFJM00sMENBQU1BLENBQUM4UixJQUFJLENBQUM7Z0JBQzdCLEdBQUduUixxRUFBaUI7Z0JBQ3BCbU4sTUFBTTVEO2dCQUNOc0gsUUFBUWxIO2dCQUNSRSxhQUFhQTtnQkFDYkcsaUJBQWlCQTtZQUNuQjtZQUVBcUMsWUFBWUw7UUFDZDtRQUNBdUYsYUFBYTtZQUNYLE1BQU12RixTQUFTLElBQUkzTSwwQ0FBTUEsQ0FBQ21TLFFBQVEsQ0FBQztnQkFDakMsR0FBR3pSLG9FQUFnQjtnQkFDbkJvTixNQUFNNUQ7Z0JBQ05zSCxRQUFRbEg7Z0JBQ1JFLGFBQWFBO2dCQUNiRyxpQkFBaUJBO1lBQ25CO1lBRUFxQyxZQUFZTDtRQUNkO1FBQ0F5RixvQkFBb0I7WUFDbEIsTUFBTUMsU0FBUzNSLG9FQUFnQkEsQ0FBQ3FLLE1BQU07WUFDdEMsTUFBTXVILFFBQVE1UixvRUFBZ0JBLENBQUNvSyxLQUFLO1lBRXBDLE1BQU02QixTQUFTLElBQUkzTSwwQ0FBTUEsQ0FBQ3VTLE9BQU8sQ0FDL0I7Z0JBQ0U7b0JBQUVDLEdBQUc7b0JBQUdDLEdBQUc7Z0JBQUU7Z0JBQ2I7b0JBQUVELEdBQUdGO29CQUFPRyxHQUFHO2dCQUFFO2dCQUNqQjtvQkFBRUQsR0FBR0YsUUFBUTtvQkFBR0csR0FBR0o7Z0JBQU87YUFDM0IsRUFDRDtnQkFDRSxHQUFHM1Isb0VBQWdCO2dCQUNuQm9OLE1BQU01RDtnQkFDTnNILFFBQVFsSDtnQkFDUkUsYUFBYUE7Z0JBQ2JHLGlCQUFpQkE7WUFDbkI7WUFHRnFDLFlBQVlMO1FBQ2Q7UUFDQStGLFlBQVk7WUFDVixNQUFNTCxTQUFTNVIsbUVBQWVBLENBQUNzSyxNQUFNO1lBQ3JDLE1BQU11SCxRQUFRN1IsbUVBQWVBLENBQUNxSyxLQUFLO1lBRW5DLE1BQU02QixTQUFTLElBQUkzTSwwQ0FBTUEsQ0FBQ3VTLE9BQU8sQ0FDL0I7Z0JBQ0U7b0JBQUVDLEdBQUdGLFFBQVE7b0JBQUdHLEdBQUc7Z0JBQUU7Z0JBQ3JCO29CQUFFRCxHQUFHRjtvQkFBT0csR0FBR0osU0FBUztnQkFBRTtnQkFDMUI7b0JBQUVHLEdBQUdGLFFBQVE7b0JBQUdHLEdBQUdKO2dCQUFPO2dCQUMxQjtvQkFBRUcsR0FBRztvQkFBR0MsR0FBR0osU0FBUztnQkFBRTthQUN2QixFQUNEO2dCQUNFLEdBQUc1UixtRUFBZTtnQkFDbEJxTixNQUFNNUQ7Z0JBQ05zSCxRQUFRbEg7Z0JBQ1JFLGFBQWFBO2dCQUNiRyxpQkFBaUJBO1lBQ25CO1lBRUZxQyxZQUFZTDtRQUNkO1FBQ0ExQztRQUNBMEkscUJBQXFCO1lBQ25CLE1BQU05QyxpQkFBaUJuRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNtRixnQkFBZ0I7Z0JBQ25CLE9BQU85TywrREFBV0E7WUFDcEI7WUFFQSxhQUFhO1lBQ2Isd0NBQXdDO1lBQ3hDLE1BQU00TSxRQUFRa0MsZUFBZUMsR0FBRyxDQUFDLGlCQUFpQi9PLCtEQUFXQTtZQUU3RCxPQUFPNE07UUFDVDtRQUNBaUYscUJBQXFCO1lBQ25CLE1BQU0vQyxpQkFBaUJuRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNtRixnQkFBZ0I7Z0JBQ25CLE9BQU8xRjtZQUNUO1lBRUEsYUFBYTtZQUNiLHdDQUF3QztZQUN4QyxNQUFNd0QsUUFBUWtDLGVBQWVDLEdBQUcsQ0FBQyxpQkFBaUIzRjtZQUVsRCxPQUFPd0Q7UUFDVDtRQUNBa0Ysb0JBQW9CO1lBQ2xCLE1BQU1oRCxpQkFBaUJuRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNtRixnQkFBZ0I7Z0JBQ25CLE9BQU8zRjtZQUNUO1lBRUEsTUFBTXlELFFBQVFrQyxlQUFlQyxHQUFHLENBQUMsV0FBVzVGO1lBRTVDLG9EQUFvRDtZQUNwRCxPQUFPeUQ7UUFDVDtRQUNBbUYsc0JBQXNCO1lBQ3BCLE1BQU1qRCxpQkFBaUJuRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNtRixnQkFBZ0I7Z0JBQ25CLE9BQU92RjtZQUNUO1lBRUEsTUFBTXFELFFBQVFrQyxlQUFlQyxHQUFHLENBQUMsYUFBYXhGO1lBRTlDLE9BQU9xRDtRQUNUO1FBQ0FvRixzQkFBc0I7WUFDcEIsTUFBTWxELGlCQUFpQm5GLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ21GLGdCQUFnQjtnQkFDbkIsT0FBT3JGO1lBQ1Q7WUFFQSxNQUFNbUQsUUFBUWtDLGVBQWVDLEdBQUcsQ0FBQyxrQkFBa0J0RjtZQUVuRCxPQUFPbUQ7UUFDVDtRQUNBcUYsMEJBQTBCO1lBQ3hCLE1BQU1uRCxpQkFBaUJuRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNtRixnQkFBZ0I7Z0JBQ25CLE9BQU9sRjtZQUNUO1lBRUEsTUFBTWdELFFBQVFrQyxlQUFlQyxHQUFHLENBQUMsc0JBQXNCbkY7WUFFdkQsT0FBT2dEO1FBQ1Q7UUFDQXNGLFNBQVMsQ0FBQzNKO1lBQ1IsSUFBSSxDQUFDVyxRQUFRO1lBRWIsd0RBQXdEO1lBQ3hELE1BQU1pSixnQkFBZ0JuUixlQUFlLENBQUN1SCxTQUFTO1lBQy9DLElBQUksQ0FBQzRKLGVBQWU7Z0JBQ2xCQyxRQUFRQyxJQUFJLENBQUMsUUFBaUIsT0FBVDlKLFVBQVM7Z0JBQzlCO1lBQ0Y7WUFFQSxpREFBaUQ7WUFDakQsTUFBTStKLFdBQVc7WUFDakIsTUFBTUMsWUFBWWhKLGVBQWU7WUFFakMsaURBQWlEO1lBQ2pELE1BQU1pSixZQUFZLG9DQUlmLE9BSm1EdEgsbUJBQW1CLHlCQUNwQ29ILE9BQXJCQSxVQUFTLGNBQWlFQyxPQUFyREQsVUFBUyw4Q0FDeENoSyxPQURvRmlLLFdBQVUscUhBQ3BFLE9BQTFCakssZ0JBQWdCQyxXQUFVO1lBSWhDLDRDQUE0QztZQUM1Q3RKLDBDQUFNQSxDQUFDK0MsS0FBSyxDQUFDb00sT0FBTyxDQUFDb0UsV0FBVyxDQUFDQztnQkFDL0IsSUFBSUEsT0FBT3ZKLFFBQVE7b0JBQ2pCdUosSUFBSTVGLEdBQUcsQ0FBQzt3QkFDTjVDLE1BQU07d0JBQ05DLEtBQUs7d0JBQ0x3SSxRQUFRO3dCQUNSQyxRQUFRO3dCQUNSLG1DQUFtQzt3QkFDbkNDLGNBQWM7d0JBQ2RDLGNBQWM7d0JBQ2RDLGdCQUFnQjt3QkFDaEJDLGVBQWU7d0JBQ2ZDLGVBQWU7d0JBQ2ZDLGNBQWM7d0JBQ2QscUNBQXFDO3dCQUNyQ0MsWUFBWTt3QkFDWkMsU0FBUzt3QkFDVCxrQ0FBa0M7d0JBQ2xDckYsTUFBTTt3QkFDTnZGLFVBQVVBO3dCQUNWLHlDQUF5Qzt3QkFDekM2SyxlQUFlYjtvQkFDakI7b0JBQ0F0RyxZQUFZd0c7Z0JBQ2Q7WUFDRjtRQUNGO1FBQ0Esd0JBQXdCO1FBQ3hCWSxpQkFBaUIsQ0FBQ2hHO1lBQ2hCbkUsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJQSxPQUFPa0MsSUFBSSxLQUFLLFVBQVVsQyxPQUFPckQsUUFBUSxFQUFFO29CQUM3QyxvQ0FBb0M7b0JBQ3BDLE1BQU0rSixXQUFXZ0IsS0FBS0MsR0FBRyxDQUFDM0gsT0FBTzdCLEtBQUssSUFBSSxJQUFJNkIsT0FBTzVCLE1BQU0sSUFBSTtvQkFDL0QsTUFBTXdJLFlBQVksb0NBSWYsT0FKbUR0SCxtQkFBbUIsNkJBQ3BDb0gsT0FBckJBLFVBQVMsY0FBaUVqRixPQUFyRGlGLFVBQVMsOENBQ3hDaEssT0FEb0YrRSxPQUFNLHlIQUN6RCxPQUFqQy9FLGdCQUFnQnNELE9BQU9yRCxRQUFRLEdBQUU7b0JBSXZDLDBCQUEwQjtvQkFDMUJ0SiwwQ0FBTUEsQ0FBQytDLEtBQUssQ0FBQ29NLE9BQU8sQ0FBQ29FLFdBQVcsQ0FBQ2dCO3dCQUMvQixJQUFJQSxVQUFVdEssUUFBUTs0QkFDcEIsc0NBQXNDOzRCQUN0QyxNQUFNdUssbUJBQW1CO2dDQUN2QnhKLE1BQU0yQixPQUFPM0IsSUFBSTtnQ0FDakJDLEtBQUswQixPQUFPMUIsR0FBRztnQ0FDZndJLFFBQVE5RyxPQUFPOEcsTUFBTTtnQ0FDckJDLFFBQVEvRyxPQUFPK0csTUFBTTtnQ0FDckJlLE9BQU85SCxPQUFPOEgsS0FBSztnQ0FDbkJDLE9BQU8vSCxPQUFPK0gsS0FBSztnQ0FDbkJDLE9BQU9oSSxPQUFPZ0ksS0FBSzs0QkFDckI7NEJBRUEsb0NBQW9DOzRCQUNwQzFLLE9BQU93RixNQUFNLENBQUM5Qzs0QkFDZDRILE9BQU8zRyxHQUFHLENBQUM7Z0NBQ1QsR0FBRzRHLGdCQUFnQjtnQ0FDbkIzRixNQUFNO2dDQUNOdkYsVUFBVXFELE9BQU9yRCxRQUFRO2dDQUN6QjZLLGVBQWUvRjtnQ0FDZjZGLFlBQVk7Z0NBQ1pDLFNBQVM7NEJBQ1g7NEJBQ0FqSyxPQUFPZ0QsR0FBRyxDQUFDc0g7NEJBQ1h0SyxPQUFPaUQsZUFBZSxDQUFDcUg7NEJBQ3ZCdEssT0FBTzhELFNBQVM7NEJBQ2hCdEU7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO1FBQ0FtTCxnQkFBZ0IsQ0FBQzlKLE9BQWVDO1lBQzlCZCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDLElBQUlBLE9BQU9rQyxJQUFJLEtBQUssUUFBUTtvQkFDMUJsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUNUNkYsUUFBUTNJLFFBQVM2QixDQUFBQSxPQUFPN0IsS0FBSyxJQUFJLEVBQUM7d0JBQ2xDNEksUUFBUTNJLFNBQVU0QixDQUFBQSxPQUFPNUIsTUFBTSxJQUFJLEVBQUM7b0JBQ3RDO2dCQUNGO1lBQ0Y7WUFDQWQsT0FBTzhELFNBQVM7WUFDaEJ0RTtRQUNGO1FBQ0FvTCxvQkFBb0I7WUFDbEIsTUFBTWhGLGlCQUFpQm5GLGVBQWUsQ0FBQyxFQUFFO1lBQ3pDLElBQUltRixrQkFBa0JBLGVBQWVoQixJQUFJLEtBQUssUUFBUTtnQkFDcEQsT0FBT2dCLGVBQWVzRSxhQUFhLElBQUk3SjtZQUN6QztZQUNBLE9BQU9BO1FBQ1Q7UUFDQXdLLG1CQUFtQjtZQUNqQixNQUFNakYsaUJBQWlCbkYsZUFBZSxDQUFDLEVBQUU7WUFDekMsSUFBSW1GLGtCQUFrQkEsZUFBZWhCLElBQUksS0FBSyxRQUFRO2dCQUNwRCxNQUFNL0QsUUFBUSxDQUFDK0UsZUFBZS9FLEtBQUssSUFBSSxFQUFDLElBQU0rRSxDQUFBQSxlQUFlNEQsTUFBTSxJQUFJO2dCQUN2RSxNQUFNMUksU0FBUyxDQUFDOEUsZUFBZTlFLE1BQU0sSUFBSSxFQUFDLElBQU04RSxDQUFBQSxlQUFlNkQsTUFBTSxJQUFJO2dCQUN6RSxPQUFPO29CQUFFNUksT0FBT3VKLEtBQUtVLEtBQUssQ0FBQ2pLO29CQUFRQyxRQUFRc0osS0FBS1UsS0FBSyxDQUFDaEs7Z0JBQVE7WUFDaEU7WUFDQSxPQUFPO2dCQUFFRCxPQUFPO2dCQUFJQyxRQUFRO1lBQUc7UUFDakM7UUFDQUw7SUFDRjtBQUNGO0FBRU8sTUFBTXNLLFlBQVk7UUFBQyxFQUN4QkMsWUFBWSxFQUNaQyxhQUFhLEVBQ2JDLFlBQVksRUFDWkMsc0JBQXNCLEVBQ3RCQyxZQUFZLEVBQ1pDLG1CQUFtQixFQUNIO0lBQ2hCLE1BQU1DLGVBQWVuViw2Q0FBTUEsQ0FBQzZVO0lBQzVCLE1BQU1PLGVBQWVwViw2Q0FBTUEsQ0FBQytVO0lBQzVCLE1BQU1NLGdCQUFnQnJWLDZDQUFNQSxDQUFDOFU7SUFFN0IsTUFBTSxDQUFDakwsUUFBUXlMLFVBQVUsR0FBR3hWLCtDQUFRQSxDQUF1QjtJQUMzRCxNQUFNLENBQUN5VixXQUFXQyxhQUFhLEdBQUcxViwrQ0FBUUEsQ0FBd0I7SUFDbEUsTUFBTSxDQUFDd0ssaUJBQWlCbUwsbUJBQW1CLEdBQUczViwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUUxRSxNQUFNLENBQUNpSyxZQUFZQyxjQUFjLEdBQUdsSywrQ0FBUUEsQ0FBQ1ksK0RBQVdBO0lBQ3hELE1BQU0sQ0FBQ29KLFdBQVdHLGFBQWEsR0FBR25LLCtDQUFRQSxDQUFDRyw4REFBVUE7SUFDckQsTUFBTSxDQUFDaUssYUFBYUMsZUFBZSxHQUFHckssK0NBQVFBLENBQUNLLGdFQUFZQTtJQUMzRCxNQUFNLENBQUNpSyxhQUFhQyxlQUFlLEdBQUd2SywrQ0FBUUEsQ0FBQ0ksZ0VBQVlBO0lBQzNELE1BQU0sQ0FBQ3FLLGlCQUFpQkMsbUJBQW1CLEdBQUcxSywrQ0FBUUEsQ0FBV1UscUVBQWlCQTtJQUVsRmdCLDBGQUFlQTtJQUVmLE1BQU0sRUFDSjZILElBQUksRUFDSkcsT0FBTyxFQUNQQyxPQUFPLEVBQ1BILElBQUksRUFDSkMsSUFBSSxFQUNKbU0sYUFBYSxFQUNiQyxlQUFlLEVBQ2hCLEdBQUc3VSw4RUFBVUEsQ0FBQztRQUNiK0k7UUFDQW9MO0lBQ0Y7SUFFQSxNQUFNLEVBQUV0TCxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHeEksa0ZBQVlBLENBQUM7UUFBRXlJO0lBQU87SUFFOUMsTUFBTSxFQUFFSCxRQUFRLEVBQUUsR0FBR3JJLHFGQUFhQSxDQUFDO1FBQ2pDd0k7UUFDQTBMO0lBQ0Y7SUFFQWpVLHlGQUFlQSxDQUFDO1FBQ2QrSDtRQUNBUTtRQUNBNEw7UUFDQVQ7UUFDQUU7SUFDRjtJQUVBM1QscUZBQWFBLENBQUM7UUFDWnNJO0lBQ0Y7SUFFQTFJLDhFQUFVQSxDQUFDO1FBQ1RtSTtRQUNBQztRQUNBSTtRQUNBQztRQUNBUDtRQUNBUTtJQUNGO0lBRUFwSSxvRkFBWUEsQ0FBQztRQUNYb0k7UUFDQUg7UUFDQXlMO1FBQ0FPO1FBQ0FDO0lBQ0Y7SUFFQSxNQUFNQyxTQUFTN1YsOENBQU9BLENBQUM7UUFDckIsSUFBSThKLFFBQVE7WUFDVixPQUFPVCxZQUFZO2dCQUNqQkM7Z0JBQ0FDO2dCQUNBQztnQkFDQUU7Z0JBQ0FEO2dCQUNBRTtnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FNO2dCQUNBRjtnQkFDQUQ7Z0JBQ0FFO2dCQUNBRTtnQkFDQUU7Z0JBQ0FEO2dCQUNBRTtnQkFDQVQ7Z0JBQ0FDO1lBQ0Y7UUFDRjtRQUVBLE9BQU82TDtJQUNULEdBQ0E7UUFDRXJNO1FBQ0FDO1FBQ0FIO1FBQ0FDO1FBQ0FGO1FBQ0FLO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FNO1FBQ0FGO1FBQ0FJO1FBQ0FDO1FBQ0FSO0tBQ0Q7SUFFRCxNQUFNK0wsT0FBT2pXLGtEQUFXQSxDQUN0QjtZQUFDLEVBQ0NrVyxhQUFhLEVBQ2JDLGdCQUFnQixFQUlqQjtRQUNDcFcsMENBQU1BLENBQUNxVyxNQUFNLENBQUNDLFNBQVMsQ0FBQzFJLEdBQUcsQ0FBQztZQUMxQjJJLGFBQWE7WUFDYkMsYUFBYTtZQUNiQyxhQUFhO1lBQ2JDLG1CQUFtQjtZQUNuQkMsb0JBQW9CO1lBQ3BCQyx5QkFBeUI7WUFDekJDLG1CQUFtQjtRQUNyQjtRQUVBLE1BQU1DLG1CQUFtQixJQUFJOVcsMENBQU1BLENBQUM4UixJQUFJLENBQUM7WUFDdkNoSCxPQUFPMEssYUFBYXVCLE9BQU87WUFDM0JoTSxRQUFRMEssY0FBY3NCLE9BQU87WUFDN0I1TCxNQUFNO1lBQ04yQyxNQUFNO1lBQ05tRyxZQUFZO1lBQ1orQyxhQUFhO1lBQ2JDLFFBQVEsSUFBSWpYLDBDQUFNQSxDQUFDa1gsTUFBTSxDQUFDO2dCQUN4QjlJLE9BQU87Z0JBQ1ArSSxNQUFNO1lBQ1I7UUFDRjtRQUVBaEIsY0FBY2lCLFFBQVEsQ0FBQ2hCLGlCQUFpQmlCLFdBQVc7UUFDbkRsQixjQUFjbUIsU0FBUyxDQUFDbEIsaUJBQWlCbUIsWUFBWTtRQUVyRHBCLGNBQWNsSixHQUFHLENBQUM2SjtRQUNsQlgsY0FBY3FCLFlBQVksQ0FBQ1Y7UUFDM0JYLGNBQWNzQixRQUFRLEdBQUdYO1FBRXpCcEIsVUFBVVM7UUFDVlAsYUFBYVE7UUFFYixNQUFNc0IsZUFBZXhMLEtBQUtDLFNBQVMsQ0FDakNnSyxjQUFjckssTUFBTSxDQUFDN0ssNkRBQVNBO1FBRWhDNlUsY0FBY2lCLE9BQU8sR0FBRztZQUFDVztTQUFhO1FBQ3RDM0IsZ0JBQWdCO0lBQ2xCLEdBQ0E7UUFDRUQ7UUFDQUM7S0FDRDtJQUdILE9BQU87UUFBRUc7UUFBTUY7SUFBTztBQUN4QixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWVkaXRvci50cz9jOWFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZhYnJpYyB9IGZyb20gXCJmYWJyaWNcIjtcclxuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVN0YXRlLCB1c2VNZW1vLCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmltcG9ydCB7IFxyXG4gIEVkaXRvciwgXHJcbiAgRklMTF9DT0xPUixcclxuICBTVFJPS0VfV0lEVEgsXHJcbiAgU1RST0tFX0NPTE9SLFxyXG4gIENJUkNMRV9PUFRJT05TLFxyXG4gIERJQU1PTkRfT1BUSU9OUyxcclxuICBUUklBTkdMRV9PUFRJT05TLFxyXG4gIEJ1aWxkRWRpdG9yUHJvcHMsIFxyXG4gIFJFQ1RBTkdMRV9PUFRJT05TLFxyXG4gIEVkaXRvckhvb2tQcm9wcyxcclxuICBTVFJPS0VfREFTSF9BUlJBWSxcclxuICBURVhUX09QVElPTlMsXHJcbiAgRk9OVF9GQU1JTFksXHJcbiAgRk9OVF9XRUlHSFQsXHJcbiAgRk9OVF9TSVpFLFxyXG4gIEpTT05fS0VZUyxcclxufSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvdHlwZXNcIjtcclxuaW1wb3J0IHsgdXNlSGlzdG9yeSB9IGZyb20gXCJAL2ZlYXR1cmVzL2VkaXRvci9ob29rcy91c2UtaGlzdG9yeVwiO1xyXG5pbXBvcnQgeyBcclxuICBjcmVhdGVGaWx0ZXIsIFxyXG4gIGRvd25sb2FkRmlsZSwgXHJcbiAgaXNUZXh0VHlwZSxcclxuICB0cmFuc2Zvcm1UZXh0XHJcbn0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL3V0aWxzXCI7XHJcbmltcG9ydCB7IHVzZUhvdGtleXMgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWhvdGtleXNcIjtcclxuaW1wb3J0IHsgdXNlQ2xpcGJvYXJkIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2hvb2tzLy91c2UtY2xpcGJvYXJkXCI7XHJcbmltcG9ydCB7IHVzZUF1dG9SZXNpemUgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWF1dG8tcmVzaXplXCI7XHJcbmltcG9ydCB7IHVzZUNhbnZhc0V2ZW50cyB9IGZyb20gXCJAL2ZlYXR1cmVzL2VkaXRvci9ob29rcy91c2UtY2FudmFzLWV2ZW50c1wiO1xyXG5pbXBvcnQgeyB1c2Vab29tRXZlbnRzIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2hvb2tzL3VzZS16b29tLWV2ZW50c1wiO1xyXG5pbXBvcnQgeyB1c2VXaW5kb3dFdmVudHMgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLXdpbmRvdy1ldmVudHNcIjtcclxuaW1wb3J0IHsgdXNlTG9hZFN0YXRlIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2hvb2tzL3VzZS1sb2FkLXN0YXRlXCI7XHJcbmltcG9ydCAqIGFzIEx1Y2lkZUljb25zIGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcclxuXHJcbi8vIENvbXByZWhlbnNpdmUgaWNvbiBtYXBwaW5nIHVzaW5nIEx1Y2lkZSBSZWFjdCBpY29uc1xyXG5jb25zdCBJQ09OX0NPTVBPTkVOVFM6IFJlY29yZDxzdHJpbmcsIFJlYWN0LkNvbXBvbmVudFR5cGU8YW55Pj4gPSB7XHJcbiAgXCJsdWNpZGU6aGVhcnRcIjogTHVjaWRlSWNvbnMuSGVhcnQsXHJcbiAgXCJsdWNpZGU6c3RhclwiOiBMdWNpZGVJY29ucy5TdGFyLFxyXG4gIFwibHVjaWRlOmFycm93LXJpZ2h0XCI6IEx1Y2lkZUljb25zLkFycm93UmlnaHQsXHJcbiAgXCJsdWNpZGU6YXJyb3ctbGVmdFwiOiBMdWNpZGVJY29ucy5BcnJvd0xlZnQsXHJcbiAgXCJsdWNpZGU6YXJyb3ctdXBcIjogTHVjaWRlSWNvbnMuQXJyb3dVcCxcclxuICBcImx1Y2lkZTphcnJvdy1kb3duXCI6IEx1Y2lkZUljb25zLkFycm93RG93bixcclxuICBcImx1Y2lkZTpob21lXCI6IEx1Y2lkZUljb25zLkhvbWUsXHJcbiAgXCJsdWNpZGU6dXNlclwiOiBMdWNpZGVJY29ucy5Vc2VyLFxyXG4gIFwibHVjaWRlOnNldHRpbmdzXCI6IEx1Y2lkZUljb25zLlNldHRpbmdzLFxyXG4gIFwibHVjaWRlOm1haWxcIjogTHVjaWRlSWNvbnMuTWFpbCxcclxuICBcImx1Y2lkZTpwaG9uZVwiOiBMdWNpZGVJY29ucy5QaG9uZSxcclxuICBcImx1Y2lkZTpjYXJcIjogTHVjaWRlSWNvbnMuQ2FyLFxyXG4gIFwibHVjaWRlOmNhbWVyYVwiOiBMdWNpZGVJY29ucy5DYW1lcmEsXHJcbiAgXCJsdWNpZGU6bXVzaWNcIjogTHVjaWRlSWNvbnMuTXVzaWMsXHJcbiAgXCJsdWNpZGU6dmlkZW9cIjogTHVjaWRlSWNvbnMuVmlkZW8sXHJcbiAgXCJsdWNpZGU6aW1hZ2VcIjogTHVjaWRlSWNvbnMuSW1hZ2UsXHJcbiAgXCJsdWNpZGU6ZmlsZVwiOiBMdWNpZGVJY29ucy5GaWxlLFxyXG4gIFwibHVjaWRlOmZvbGRlclwiOiBMdWNpZGVJY29ucy5Gb2xkZXIsXHJcbiAgXCJsdWNpZGU6c2VhcmNoXCI6IEx1Y2lkZUljb25zLlNlYXJjaCxcclxuICBcImx1Y2lkZTpwbHVzXCI6IEx1Y2lkZUljb25zLlBsdXMsXHJcbiAgXCJsdWNpZGU6bWludXNcIjogTHVjaWRlSWNvbnMuTWludXMsXHJcbiAgXCJsdWNpZGU6eFwiOiBMdWNpZGVJY29ucy5YLFxyXG4gIFwibHVjaWRlOmNoZWNrXCI6IEx1Y2lkZUljb25zLkNoZWNrLFxyXG4gIFwibHVjaWRlOmVkaXRcIjogTHVjaWRlSWNvbnMuRWRpdCxcclxuICBcImx1Y2lkZTp0cmFzaFwiOiBMdWNpZGVJY29ucy5UcmFzaDIsXHJcbiAgXCJsdWNpZGU6ZG93bmxvYWRcIjogTHVjaWRlSWNvbnMuRG93bmxvYWQsXHJcbiAgXCJsdWNpZGU6dXBsb2FkXCI6IEx1Y2lkZUljb25zLlVwbG9hZCxcclxuICBcImx1Y2lkZTpzaGFyZVwiOiBMdWNpZGVJY29ucy5TaGFyZSxcclxuICBcImx1Y2lkZTpjb3B5XCI6IEx1Y2lkZUljb25zLkNvcHksXHJcbiAgXCJsdWNpZGU6c2F2ZVwiOiBMdWNpZGVJY29ucy5TYXZlLFxyXG4gIFwibHVjaWRlOnByaW50XCI6IEx1Y2lkZUljb25zLlByaW50ZXIsXHJcbiAgXCJsdWNpZGU6Y2FsZW5kYXJcIjogTHVjaWRlSWNvbnMuQ2FsZW5kYXIsXHJcbiAgXCJsdWNpZGU6Y2xvY2tcIjogTHVjaWRlSWNvbnMuQ2xvY2ssXHJcbiAgXCJsdWNpZGU6bWFwXCI6IEx1Y2lkZUljb25zLk1hcCxcclxuICBcImx1Y2lkZTpnbG9iZVwiOiBMdWNpZGVJY29ucy5HbG9iZSxcclxuICBcImx1Y2lkZTp3aWZpXCI6IEx1Y2lkZUljb25zLldpZmksXHJcbiAgXCJsdWNpZGU6YmF0dGVyeVwiOiBMdWNpZGVJY29ucy5CYXR0ZXJ5LFxyXG4gIFwibHVjaWRlOnZvbHVtZVwiOiBMdWNpZGVJY29ucy5Wb2x1bWUyLFxyXG4gIFwibHVjaWRlOnBsYXlcIjogTHVjaWRlSWNvbnMuUGxheSxcclxuICBcImx1Y2lkZTpwYXVzZVwiOiBMdWNpZGVJY29ucy5QYXVzZSxcclxuICBcImx1Y2lkZTpzdG9wXCI6IEx1Y2lkZUljb25zLlNxdWFyZSxcclxuICBcImx1Y2lkZTpza2lwLWZvcndhcmRcIjogTHVjaWRlSWNvbnMuU2tpcEZvcndhcmQsXHJcbiAgXCJsdWNpZGU6c2tpcC1iYWNrXCI6IEx1Y2lkZUljb25zLlNraXBCYWNrLFxyXG4gIFwibHVjaWRlOnJlZnJlc2hcIjogTHVjaWRlSWNvbnMuUmVmcmVzaEN3LFxyXG4gIFwibHVjaWRlOm1heGltaXplXCI6IEx1Y2lkZUljb25zLk1heGltaXplLFxyXG4gIFwibHVjaWRlOm1pbmltaXplXCI6IEx1Y2lkZUljb25zLk1pbmltaXplLFxyXG4gIFwibHVjaWRlOmV5ZVwiOiBMdWNpZGVJY29ucy5FeWUsXHJcbiAgXCJsdWNpZGU6ZXllLW9mZlwiOiBMdWNpZGVJY29ucy5FeWVPZmYsXHJcbiAgXCJsdWNpZGU6bG9ja1wiOiBMdWNpZGVJY29ucy5Mb2NrLFxyXG4gIFwibHVjaWRlOnVubG9ja1wiOiBMdWNpZGVJY29ucy5VbmxvY2ssXHJcbiAgXCJsdWNpZGU6c2hpZWxkXCI6IEx1Y2lkZUljb25zLlNoaWVsZCxcclxuICBcImx1Y2lkZTphbGVydFwiOiBMdWNpZGVJY29ucy5BbGVydFRyaWFuZ2xlLFxyXG4gIFwibHVjaWRlOmluZm9cIjogTHVjaWRlSWNvbnMuSW5mbyxcclxuICBcImx1Y2lkZTpoZWxwXCI6IEx1Y2lkZUljb25zLkhlbHBDaXJjbGUsXHJcbiAgXCJsdWNpZGU6Ym9va21hcmtcIjogTHVjaWRlSWNvbnMuQm9va21hcmssXHJcbiAgXCJsdWNpZGU6dGFnXCI6IEx1Y2lkZUljb25zLlRhZyxcclxuICBcImx1Y2lkZTpmbGFnXCI6IEx1Y2lkZUljb25zLkZsYWcsXHJcbiAgXCJsdWNpZGU6dGh1bWJzLXVwXCI6IEx1Y2lkZUljb25zLlRodW1ic1VwLFxyXG4gIFwibHVjaWRlOnRodW1icy1kb3duXCI6IEx1Y2lkZUljb25zLlRodW1ic0Rvd24sXHJcbiAgXCJsdWNpZGU6bWVzc2FnZVwiOiBMdWNpZGVJY29ucy5NZXNzYWdlQ2lyY2xlLFxyXG4gIFwibHVjaWRlOnNlbmRcIjogTHVjaWRlSWNvbnMuU2VuZCxcclxuICBcImx1Y2lkZTpiZWxsXCI6IEx1Y2lkZUljb25zLkJlbGwsXHJcbiAgXCJsdWNpZGU6Z2lmdFwiOiBMdWNpZGVJY29ucy5HaWZ0LFxyXG4gIFwibHVjaWRlOnNob3BwaW5nLWNhcnRcIjogTHVjaWRlSWNvbnMuU2hvcHBpbmdDYXJ0LFxyXG4gIFwibHVjaWRlOmNyZWRpdC1jYXJkXCI6IEx1Y2lkZUljb25zLkNyZWRpdENhcmQsXHJcbiAgXCJsdWNpZGU6ZG9sbGFyLXNpZ25cIjogTHVjaWRlSWNvbnMuRG9sbGFyU2lnbixcclxuICBcImx1Y2lkZTp0cmVuZGluZy11cFwiOiBMdWNpZGVJY29ucy5UcmVuZGluZ1VwLFxyXG4gIFwibHVjaWRlOnRyZW5kaW5nLWRvd25cIjogTHVjaWRlSWNvbnMuVHJlbmRpbmdEb3duLFxyXG4gIFwibHVjaWRlOmJhci1jaGFydFwiOiBMdWNpZGVJY29ucy5CYXJDaGFydDMsXHJcbiAgXCJsdWNpZGU6cGllLWNoYXJ0XCI6IEx1Y2lkZUljb25zLlBpZUNoYXJ0LFxyXG4gIFwibHVjaWRlOmFjdGl2aXR5XCI6IEx1Y2lkZUljb25zLkFjdGl2aXR5LFxyXG4gIFwibHVjaWRlOnphcFwiOiBMdWNpZGVJY29ucy5aYXAsXHJcbiAgXCJsdWNpZGU6c3VuXCI6IEx1Y2lkZUljb25zLlN1bixcclxuICBcImx1Y2lkZTptb29uXCI6IEx1Y2lkZUljb25zLk1vb24sXHJcbiAgXCJsdWNpZGU6Y2xvdWRcIjogTHVjaWRlSWNvbnMuQ2xvdWQsXHJcbiAgXCJsdWNpZGU6dW1icmVsbGFcIjogTHVjaWRlSWNvbnMuVW1icmVsbGEsXHJcbiAgXCJsdWNpZGU6c25vd2ZsYWtlXCI6IEx1Y2lkZUljb25zLlNub3dmbGFrZSxcclxuICBcImx1Y2lkZTpkcm9wbGV0XCI6IEx1Y2lkZUljb25zLkRyb3BsZXRzLFxyXG4gIFwibHVjaWRlOmZsYW1lXCI6IEx1Y2lkZUljb25zLkZsYW1lLFxyXG4gIFwibHVjaWRlOmxlYWZcIjogTHVjaWRlSWNvbnMuTGVhZixcclxuICBcImx1Y2lkZTp0cmVlXCI6IEx1Y2lkZUljb25zLlRyZWVzLFxyXG4gIFwibHVjaWRlOmZsb3dlclwiOiBMdWNpZGVJY29ucy5GbG93ZXIsXHJcbiAgXCJsdWNpZGU6Y29mZmVlXCI6IEx1Y2lkZUljb25zLkNvZmZlZSxcclxuICBcImx1Y2lkZTpwaXp6YVwiOiBMdWNpZGVJY29ucy5QaXp6YSxcclxuICBcImx1Y2lkZTp1dGVuc2lsc1wiOiBMdWNpZGVJY29ucy5VdGVuc2lscyxcclxuICBcImx1Y2lkZTp3aW5lXCI6IEx1Y2lkZUljb25zLldpbmUsXHJcbiAgXCJsdWNpZGU6Z2FtZXBhZFwiOiBMdWNpZGVJY29ucy5HYW1lcGFkMixcclxuICBcImx1Y2lkZTpoZWFkcGhvbmVzXCI6IEx1Y2lkZUljb25zLkhlYWRwaG9uZXMsXHJcbiAgXCJsdWNpZGU6c21hcnRwaG9uZVwiOiBMdWNpZGVJY29ucy5TbWFydHBob25lLFxyXG4gIFwibHVjaWRlOmxhcHRvcFwiOiBMdWNpZGVJY29ucy5MYXB0b3AsXHJcbiAgXCJsdWNpZGU6bW9uaXRvclwiOiBMdWNpZGVJY29ucy5Nb25pdG9yLFxyXG4gIFwibHVjaWRlOmtleWJvYXJkXCI6IEx1Y2lkZUljb25zLktleWJvYXJkLFxyXG4gIFwibHVjaWRlOm1vdXNlXCI6IEx1Y2lkZUljb25zLk1vdXNlLFxyXG4gIFwibHVjaWRlOnByaW50ZXJcIjogTHVjaWRlSWNvbnMuUHJpbnRlcixcclxuICBcImx1Y2lkZTpoYXJkLWRyaXZlXCI6IEx1Y2lkZUljb25zLkhhcmREcml2ZSxcclxuICBcImx1Y2lkZTp1c2JcIjogTHVjaWRlSWNvbnMuVXNiLFxyXG4gIFwibHVjaWRlOmJsdWV0b290aFwiOiBMdWNpZGVJY29ucy5CbHVldG9vdGgsXHJcbiAgXCJsdWNpZGU6cmFkaW9cIjogTHVjaWRlSWNvbnMuUmFkaW8sXHJcbiAgXCJsdWNpZGU6dHZcIjogTHVjaWRlSWNvbnMuVHYsXHJcbiAgXCJsdWNpZGU6cGxhbmVcIjogTHVjaWRlSWNvbnMuUGxhbmUsXHJcbiAgXCJsdWNpZGU6dHJhaW5cIjogTHVjaWRlSWNvbnMuVHJhaW4sXHJcbiAgXCJsdWNpZGU6YnVzXCI6IEx1Y2lkZUljb25zLkJ1cyxcclxuICBcImx1Y2lkZTpiaWtlXCI6IEx1Y2lkZUljb25zLkJpa2UsXHJcbiAgXCJsdWNpZGU6c2hpcFwiOiBMdWNpZGVJY29ucy5TaGlwLFxyXG4gIFwibHVjaWRlOmFuY2hvclwiOiBMdWNpZGVJY29ucy5BbmNob3IsXHJcbiAgXCJsdWNpZGU6Y29tcGFzc1wiOiBMdWNpZGVJY29ucy5Db21wYXNzLFxyXG4gIFwibHVjaWRlOm1hcC1waW5cIjogTHVjaWRlSWNvbnMuTWFwUGluLFxyXG4gIFwibHVjaWRlOm5hdmlnYXRpb25cIjogTHVjaWRlSWNvbnMuTmF2aWdhdGlvbixcclxuICBcImx1Y2lkZTpidWlsZGluZ1wiOiBMdWNpZGVJY29ucy5CdWlsZGluZyxcclxuICBcImx1Y2lkZTpzY2hvb2xcIjogTHVjaWRlSWNvbnMuU2Nob29sLFxyXG4gIFwibHVjaWRlOmhvc3BpdGFsXCI6IEx1Y2lkZUljb25zLkhvc3BpdGFsLFxyXG4gIFwibHVjaWRlOnN0b3JlXCI6IEx1Y2lkZUljb25zLlN0b3JlLFxyXG4gIFwibHVjaWRlOmZhY3RvcnlcIjogTHVjaWRlSWNvbnMuRmFjdG9yeSxcclxuICBcImx1Y2lkZTp0ZW50XCI6IEx1Y2lkZUljb25zLlRlbnQsXHJcbiAgXCJsdWNpZGU6bW91bnRhaW5cIjogTHVjaWRlSWNvbnMuTW91bnRhaW4sXHJcbiAgXCJsdWNpZGU6d2F2ZXNcIjogTHVjaWRlSWNvbnMuV2F2ZXMsXHJcbiAgXCJsdWNpZGU6c3VucmlzZVwiOiBMdWNpZGVJY29ucy5TdW5yaXNlLFxyXG4gIFwibHVjaWRlOnN1bnNldFwiOiBMdWNpZGVJY29ucy5TdW5zZXQsXHJcbn07XHJcblxyXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IFNWRyBwYXRocyBmb3IgaWNvbnMgKHN0YXRpYyBhcHByb2FjaCBmb3IgcmVsaWFiaWxpdHkpXHJcbmNvbnN0IGdldEljb25TVkdQYXRocyA9IChpY29uTmFtZTogc3RyaW5nKTogc3RyaW5nID0+IHtcclxuICBjb25zdCBpY29uUGF0aHM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XHJcbiAgICBcImx1Y2lkZTpoZWFydFwiOiBgPHBhdGggZD1cIk0yMC44NCA0LjYxYTUuNSA1LjUgMCAwIDAtNy43OCAwTDEyIDUuNjdsLTEuMDYtMS4wNmE1LjUgNS41IDAgMCAwLTcuNzggNy43OGwxLjA2IDEuMDZMMTIgMjEuMjNsNy43OC03Ljc4IDEuMDYtMS4wNmE1LjUgNS41IDAgMCAwIDAtNy43OHpcIi8+YCxcclxuICAgIFwibHVjaWRlOnN0YXJcIjogYDxwb2x5Z29uIHBvaW50cz1cIjEyLDIgMTUuMDksOC4yNiAyMiw5LjI3IDE3LDE0LjE0IDE4LjE4LDIxLjAyIDEyLDE3Ljc3IDUuODIsMjEuMDIgNywxNC4xNCAyLDkuMjcgOC45MSw4LjI2XCIvPmAsXHJcbiAgICBcImx1Y2lkZTphcnJvdy1yaWdodFwiOiBgPGxpbmUgeDE9XCI1XCIgeTE9XCIxMlwiIHgyPVwiMTlcIiB5Mj1cIjEyXCIvPjxwb2x5bGluZSBwb2ludHM9XCIxMiw1IDE5LDEyIDEyLDE5XCIvPmAsXHJcbiAgICBcImx1Y2lkZTphcnJvdy1sZWZ0XCI6IGA8bGluZSB4MT1cIjE5XCIgeTE9XCIxMlwiIHgyPVwiNVwiIHkyPVwiMTJcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjEyLDE5IDUsMTIgMTIsNVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6YXJyb3ctdXBcIjogYDxsaW5lIHgxPVwiMTJcIiB5MT1cIjE5XCIgeDI9XCIxMlwiIHkyPVwiNVwiLz48cG9seWxpbmUgcG9pbnRzPVwiNSwxMiAxMiw1IDE5LDEyXCIvPmAsXHJcbiAgICBcImx1Y2lkZTphcnJvdy1kb3duXCI6IGA8bGluZSB4MT1cIjEyXCIgeTE9XCI1XCIgeDI9XCIxMlwiIHkyPVwiMTlcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjE5LDEyIDEyLDE5IDUsMTJcIi8+YCxcclxuICAgIFwibHVjaWRlOmhvbWVcIjogYDxwYXRoIGQ9XCJtMyA5IDktNyA5IDd2MTFhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJ6XCIvPjxwb2x5bGluZSBwb2ludHM9XCI5LDIyIDksMTIgMTUsMTIgMTUsMjJcIi8+YCxcclxuICAgIFwibHVjaWRlOnVzZXJcIjogYDxwYXRoIGQ9XCJNMjAgMjF2LTJhNCA0IDAgMCAwLTQtNEg4YTQgNCAwIDAgMC00IDR2MlwiLz48Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjdcIiByPVwiNFwiLz5gLFxyXG4gICAgXCJsdWNpZGU6c2V0dGluZ3NcIjogYDxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiM1wiLz48cGF0aCBkPVwiTTEyIDF2Nm0wIDZ2Nm0xMS03aC02bS02IDBIMVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6bWFpbFwiOiBgPHBhdGggZD1cIk00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yelwiLz48cG9seWxpbmUgcG9pbnRzPVwiMjIsNiAxMiwxMyAyLDZcIi8+YCxcclxuICAgIFwibHVjaWRlOnBob25lXCI6IGA8cGF0aCBkPVwiTTIyIDE2LjkydjNhMiAyIDAgMCAxLTIuMTggMiAxOS43OSAxOS43OSAwIDAgMS04LjYzLTMuMDcgMTkuNSAxOS41IDAgMCAxLTYtNiAxOS43OSAxOS43OSAwIDAgMS0zLjA3LTguNjdBMiAyIDAgMCAxIDQuMTEgMmgzYTIgMiAwIDAgMSAyIDEuNzIgMTIuODQgMTIuODQgMCAwIDAgLjcgMi44MSAyIDIgMCAwIDEtLjQ1IDIuMTFMOC4wOSA5LjkxYTE2IDE2IDAgMCAwIDYgNmwxLjI3LTEuMjdhMiAyIDAgMCAxIDIuMTEtLjQ1IDEyLjg0IDEyLjg0IDAgMCAwIDIuODEuN0EyIDIgMCAwIDEgMjIgMTYuOTJ6XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjYXJcIjogYDxwYXRoIGQ9XCJNMTkgMTdoMmMuNiAwIDEtLjQgMS0xdi0zYzAtLjktLjctMS43LTEuNS0xLjlMMTguNCAxMEg1LjZMMy41IDExLjFDMi43IDExLjMgMiAxMi4xIDIgMTN2M2MwIC42LjQgMSAxIDFoMlwiLz48Y2lyY2xlIGN4PVwiN1wiIGN5PVwiMTdcIiByPVwiMlwiLz48cGF0aCBkPVwiTTkgMTdoNlwiLz48Y2lyY2xlIGN4PVwiMTdcIiBjeT1cIjE3XCIgcj1cIjJcIi8+YCxcclxuICAgIFwibHVjaWRlOmNhbWVyYVwiOiBgPHBhdGggZD1cIk0xNC41IDRoLTVMNyA3SDRhMiAyIDAgMCAwLTIgMnY5YTIgMiAwIDAgMCAyIDJoMTZhMiAyIDAgMCAwIDItMlY5YTIgMiAwIDAgMC0yLTJoLTNsLTIuNS0zelwiLz48Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEzXCIgcj1cIjNcIi8+YCxcclxuICAgIFwibHVjaWRlOm11c2ljXCI6IGA8cGF0aCBkPVwiTTkgMThWNWwxMi0ydjEzXCIvPjxjaXJjbGUgY3g9XCI2XCIgY3k9XCIxOFwiIHI9XCIzXCIvPjxjaXJjbGUgY3g9XCIxOFwiIGN5PVwiMTZcIiByPVwiM1wiLz5gLFxyXG4gICAgXCJsdWNpZGU6dmlkZW9cIjogYDxwYXRoIGQ9XCJNMjMgN2wtNyA1IDcgNVY3elwiLz48cmVjdCB4PVwiMVwiIHk9XCI1XCIgd2lkdGg9XCIxNVwiIGhlaWdodD1cIjE0XCIgcng9XCIyXCIgcnk9XCIyXCIvPmAsXHJcbiAgICBcImx1Y2lkZTppbWFnZVwiOiBgPHJlY3QgeD1cIjNcIiB5PVwiM1wiIHdpZHRoPVwiMThcIiBoZWlnaHQ9XCIxOFwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz48Y2lyY2xlIGN4PVwiOVwiIGN5PVwiOVwiIHI9XCIyXCIvPjxwYXRoIGQ9XCJtMjEgMTUtMy4wODYtMy4wODZhMiAyIDAgMCAwLTIuODI4IDBMNiAyMVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6ZmlsZVwiOiBgPHBhdGggZD1cIk0xNC41IDJINmEyIDIgMCAwIDAtMiAydjE2YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlY3LjVMMTQuNSAyelwiLz48cG9seWxpbmUgcG9pbnRzPVwiMTQsMiAxNCw4IDIwLDhcIi8+YCxcclxuICAgIFwibHVjaWRlOmZvbGRlclwiOiBgPHBhdGggZD1cIk0yMCAyMGEyIDIgMCAwIDAgMi0yVjhhMiAyIDAgMCAwLTItMmgtNy45YTIgMiAwIDAgMS0xLjY5LS45TDkuNiAzLjlBMiAyIDAgMCAwIDcuOTMgM0g0YTIgMiAwIDAgMC0yIDJ2MTNhMiAyIDAgMCAwIDIgMlpcIi8+YCxcclxuICAgIFwibHVjaWRlOnNlYXJjaFwiOiBgPGNpcmNsZSBjeD1cIjExXCIgY3k9XCIxMVwiIHI9XCI4XCIvPjxwYXRoIGQ9XCJtMjEgMjEtNC4zNS00LjM1XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpwbHVzXCI6IGA8cGF0aCBkPVwiTTUgMTJoMTRcIi8+PHBhdGggZD1cIk0xMiA1djE0XCIvPmAsXHJcbiAgICBcImx1Y2lkZTptaW51c1wiOiBgPHBhdGggZD1cIk01IDEyaDE0XCIvPmAsXHJcbiAgICBcImx1Y2lkZTp4XCI6IGA8cGF0aCBkPVwiTTE4IDYgNiAxOFwiLz48cGF0aCBkPVwibTYgNiAxMiAxMlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6Y2hlY2tcIjogYDxwb2x5bGluZSBwb2ludHM9XCIyMCw2IDksMTcgNCwxMlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6ZWRpdFwiOiBgPHBhdGggZD1cIk0xMSA0SDRhMiAyIDAgMCAwLTIgMnYxNGEyIDIgMCAwIDAgMiAyaDE0YTIgMiAwIDAgMCAyLTJ2LTdcIi8+PHBhdGggZD1cIk0xOC41IDIuNWEyLjEyMSAyLjEyMSAwIDAgMSAzIDNMMTIgMTVsLTQgMSAxLTQgOS41LTkuNXpcIi8+YCxcclxuICAgIFwibHVjaWRlOnRyYXNoXCI6IGA8cGF0aCBkPVwiTTMgNmgxOFwiLz48cGF0aCBkPVwiTTE5IDZ2MTRjMCAxLTEgMi0yIDJIN2MtMSAwLTItMS0yLTJWNlwiLz48cGF0aCBkPVwiTTggNlY0YzAtMSAxLTIgMi0yaDRjMC0xIDEtMiAyLTJ2MlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6ZG93bmxvYWRcIjogYDxwYXRoIGQ9XCJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNFwiLz48cG9seWxpbmUgcG9pbnRzPVwiNywxMCAxMiwxNSAxNywxMFwiLz48bGluZSB4MT1cIjEyXCIgeTE9XCIxNVwiIHgyPVwiMTJcIiB5Mj1cIjNcIi8+YCxcclxuICAgIFwibHVjaWRlOnVwbG9hZFwiOiBgPHBhdGggZD1cIk0yMSAxNXY0YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi00XCIvPjxwb2x5bGluZSBwb2ludHM9XCIxNyw4IDEyLDMgNyw4XCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjNcIiB4Mj1cIjEyXCIgeTI9XCIxNVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6c2hhcmVcIjogYDxwYXRoIGQ9XCJNNCAxMnY4YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMnYtOFwiLz48cG9seWxpbmUgcG9pbnRzPVwiMTYsNiAxMiwyIDgsNlwiLz48bGluZSB4MT1cIjEyXCIgeTE9XCIyXCIgeDI9XCIxMlwiIHkyPVwiMTVcIi8+YCxcclxuICAgIFwibHVjaWRlOmNvcHlcIjogYDxyZWN0IHg9XCI5XCIgeT1cIjlcIiB3aWR0aD1cIjEzXCIgaGVpZ2h0PVwiMTNcIiByeD1cIjJcIiByeT1cIjJcIi8+PHBhdGggZD1cIk01IDE1SDRhMiAyIDAgMCAxLTItMlY0YTIgMiAwIDAgMSAyLTJoOWEyIDIgMCAwIDEgMiAydjFcIi8+YCxcclxuICAgIFwibHVjaWRlOnNhdmVcIjogYDxwYXRoIGQ9XCJNMTkgMjFINWEyIDIgMCAwIDEtMi0yVjVhMiAyIDAgMCAxIDItMmgxMWw1IDV2MTFhMiAyIDAgMCAxLTIgMnpcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjE3LDIxIDE3LDEzIDcsMTMgNywyMVwiLz48cG9seWxpbmUgcG9pbnRzPVwiNywzIDcsOCAxNSw4XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpwcmludFwiOiBgPHBvbHlsaW5lIHBvaW50cz1cIjYsOSA2LDIgMTgsMiAxOCw5XCIvPjxwYXRoIGQ9XCJNNiAxOEg0YTIgMiAwIDAgMS0yLTJ2LTVhMiAyIDAgMCAxIDItMmgxNmEyIDIgMCAwIDEgMiAydjVhMiAyIDAgMCAxLTIgMmgtMlwiLz48cmVjdCB4PVwiNlwiIHk9XCIxNFwiIHdpZHRoPVwiMTJcIiBoZWlnaHQ9XCI4XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjYWxlbmRhclwiOiBgPHJlY3QgeD1cIjNcIiB5PVwiNFwiIHdpZHRoPVwiMThcIiBoZWlnaHQ9XCIxOFwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz48bGluZSB4MT1cIjE2XCIgeTE9XCIyXCIgeDI9XCIxNlwiIHkyPVwiNlwiLz48bGluZSB4MT1cIjhcIiB5MT1cIjJcIiB4Mj1cIjhcIiB5Mj1cIjZcIi8+PGxpbmUgeDE9XCIzXCIgeTE9XCIxMFwiIHgyPVwiMjFcIiB5Mj1cIjEwXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjbG9ja1wiOiBgPGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiLz48cG9seWxpbmUgcG9pbnRzPVwiMTIsNiAxMiwxMiAxNiwxNFwiLz5gLFxyXG4gICAgXCJsdWNpZGU6bWFwXCI6IGA8cG9seWdvbiBwb2ludHM9XCIxLDYgMSwyMiA4LDE4IDE2LDIyIDIzLDE4IDIzLDIgMTYsNiA4LDJcIi8+YCxcclxuICAgIFwibHVjaWRlOmdsb2JlXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIvPjxsaW5lIHgxPVwiMlwiIHkxPVwiMTJcIiB4Mj1cIjIyXCIgeTI9XCIxMlwiLz48cGF0aCBkPVwiTTEyIDJhMTUuMyAxNS4zIDAgMCAxIDQgMTAgMTUuMyAxNS4zIDAgMCAxLTQgMTAgMTUuMyAxNS4zIDAgMCAxLTQtMTAgMTUuMyAxNS4zIDAgMCAxIDQtMTB6XCIvPmAsXHJcbiAgICBcImx1Y2lkZTp3aWZpXCI6IGA8cGF0aCBkPVwiTTUgMTIuNTVhMTEgMTEgMCAwIDEgMTQuMDggMFwiLz48cGF0aCBkPVwiTTEuNDIgOWExNiAxNiAwIDAgMSAyMS4xNiAwXCIvPjxwYXRoIGQ9XCJNOC41MyAxNi4xMWE2IDYgMCAwIDEgNi45NSAwXCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjIwXCIgeDI9XCIxMi4wMVwiIHkyPVwiMjBcIi8+YCxcclxuICAgIFwibHVjaWRlOmJhdHRlcnlcIjogYDxyZWN0IHg9XCIxXCIgeT1cIjZcIiB3aWR0aD1cIjE4XCIgaGVpZ2h0PVwiMTJcIiByeD1cIjJcIiByeT1cIjJcIi8+PGxpbmUgeDE9XCIyM1wiIHkxPVwiMTNcIiB4Mj1cIjIzXCIgeTI9XCIxMVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6dm9sdW1lXCI6IGA8cG9seWdvbiBwb2ludHM9XCIxMSw1IDYsOSAyLDkgMiwxNSA2LDE1IDExLDE5XCIvPjxwYXRoIGQ9XCJNMTkuMDcgNC45M2ExMCAxMCAwIDAgMSAwIDE0LjE0TTE1LjU0IDguNDZhNSA1IDAgMCAxIDAgNy4wN1wiLz5gLFxyXG4gICAgXCJsdWNpZGU6cGxheVwiOiBgPHBvbHlnb24gcG9pbnRzPVwiNSwzIDE5LDEyIDUsMjFcIi8+YCxcclxuICAgIFwibHVjaWRlOnBhdXNlXCI6IGA8cmVjdCB4PVwiNlwiIHk9XCI0XCIgd2lkdGg9XCI0XCIgaGVpZ2h0PVwiMTZcIi8+PHJlY3QgeD1cIjE0XCIgeT1cIjRcIiB3aWR0aD1cIjRcIiBoZWlnaHQ9XCIxNlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6c3RvcFwiOiBgPHJlY3QgeD1cIjNcIiB5PVwiM1wiIHdpZHRoPVwiMThcIiBoZWlnaHQ9XCIxOFwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6c2tpcC1mb3J3YXJkXCI6IGA8cG9seWdvbiBwb2ludHM9XCI1LDQgMTUsMTIgNSwyMFwiLz48bGluZSB4MT1cIjE5XCIgeTE9XCI1XCIgeDI9XCIxOVwiIHkyPVwiMTlcIi8+YCxcclxuICAgIFwibHVjaWRlOnNraXAtYmFja1wiOiBgPHBvbHlnb24gcG9pbnRzPVwiMTksMjAgOSwxMiAxOSw0XCIvPjxsaW5lIHgxPVwiNVwiIHkxPVwiMTlcIiB4Mj1cIjVcIiB5Mj1cIjVcIi8+YCxcclxuICAgIFwibHVjaWRlOnJlZnJlc2hcIjogYDxwb2x5bGluZSBwb2ludHM9XCIyMyw0IDIzLDEwIDE3LDEwXCIvPjxwb2x5bGluZSBwb2ludHM9XCIxLDIwIDEsMTQgNywxNFwiLz48cGF0aCBkPVwiTTIwLjQ5IDlBOSA5IDAgMCAwIDUuNjQgNS42NEwxIDEwbTIyIDQtNC42NCA0LjM2QTkgOSAwIDAgMSAzLjUxIDE1XCIvPmAsXHJcbiAgICAvLyBBZGRpdGlvbmFsIGljb25zXHJcbiAgICBcImx1Y2lkZTpjaXJjbGVcIjogYDxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiMTBcIi8+YCxcclxuICAgIFwibHVjaWRlOnRyaWFuZ2xlXCI6IGA8cGF0aCBkPVwiTTEyIDIgMjIgMjBIMnpcIi8+YCxcclxuICAgIFwibHVjaWRlOmRpYW1vbmRcIjogYDxwYXRoIGQ9XCJNMTIgMiAyMiAxMiAxMiAyMiAyIDEyelwiLz5gLFxyXG4gICAgXCJsdWNpZGU6aGV4YWdvblwiOiBgPHBhdGggZD1cIk0yMSAxNlY4YTIgMiAwIDAgMC0xLTEuNzNsLTctNGEyIDIgMCAwIDAtMiAwbC03IDRBMiAyIDAgMCAwIDMgOHY4YTIgMiAwIDAgMCAxIDEuNzNsNyA0YTIgMiAwIDAgMCAyIDBsNy00QTIgMiAwIDAgMCAyMSAxNnpcIi8+YCxcclxuICAgIFwibHVjaWRlOm9jdGFnb25cIjogYDxwb2x5Z29uIHBvaW50cz1cIjcuODYgMiAxNi4xNCAyIDIyIDcuODYgMjIgMTYuMTQgMTYuMTQgMjIgNy44NiAyMiAyIDE2LjE0IDIgNy44NlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6YXJyb3ctdXAtcmlnaHRcIjogYDxsaW5lIHgxPVwiN1wiIHkxPVwiMTdcIiB4Mj1cIjE3XCIgeTI9XCI3XCIvPjxwb2x5bGluZSBwb2ludHM9XCI3LDcgMTcsNyAxNywxN1wiLz5gLFxyXG4gICAgXCJsdWNpZGU6YXJyb3ctZG93bi1sZWZ0XCI6IGA8bGluZSB4MT1cIjE3XCIgeTE9XCI3XCIgeDI9XCI3XCIgeTI9XCIxN1wiLz48cG9seWxpbmUgcG9pbnRzPVwiMTcsMTcgNywxNyA3LDdcIi8+YCxcclxuICAgIFwibHVjaWRlOmNoZXZyb24tcmlnaHRcIjogYDxwb2x5bGluZSBwb2ludHM9XCI5LDE4IDE1LDEyIDksNlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6Y2hldnJvbi1sZWZ0XCI6IGA8cG9seWxpbmUgcG9pbnRzPVwiMTUsMTggOSwxMiAxNSw2XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjaGV2cm9uLXVwXCI6IGA8cG9seWxpbmUgcG9pbnRzPVwiMTgsMTUgMTIsOSA2LDE1XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjaGV2cm9uLWRvd25cIjogYDxwb2x5bGluZSBwb2ludHM9XCI2LDkgMTIsMTUgMTgsOVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6bW92ZVwiOiBgPHBvbHlsaW5lIHBvaW50cz1cIjUsOSAyLDEyIDUsMTVcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjksNSAxMiwyIDE1LDVcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjE1LDE5IDEyLDIyIDksMTlcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjE5LDkgMjIsMTIgMTksMTVcIi8+PGxpbmUgeDE9XCIyXCIgeTE9XCIxMlwiIHgyPVwiMjJcIiB5Mj1cIjEyXCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjJcIiB4Mj1cIjEyXCIgeTI9XCIyMlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6Y29ybmVyLWRvd24tcmlnaHRcIjogYDxwb2x5bGluZSBwb2ludHM9XCIxNSwxMCAyMCwxNSAxNSwyMFwiLz48cGF0aCBkPVwiTTQgNHY3YTQgNCAwIDAgMCA0IDRoMTJcIi8+YCxcclxuICAgIFwibHVjaWRlOm1lbnVcIjogYDxsaW5lIHgxPVwiNFwiIHkxPVwiMTJcIiB4Mj1cIjIwXCIgeTI9XCIxMlwiLz48bGluZSB4MT1cIjRcIiB5MT1cIjZcIiB4Mj1cIjIwXCIgeTI9XCI2XCIvPjxsaW5lIHgxPVwiNFwiIHkxPVwiMThcIiB4Mj1cIjIwXCIgeTI9XCIxOFwiLz5gLFxyXG4gICAgXCJsdWNpZGU6bW9yZS1ob3Jpem9udGFsXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjFcIi8+PGNpcmNsZSBjeD1cIjE5XCIgY3k9XCIxMlwiIHI9XCIxXCIvPjxjaXJjbGUgY3g9XCI1XCIgY3k9XCIxMlwiIHI9XCIxXCIvPmAsXHJcbiAgICBcImx1Y2lkZTptb3JlLXZlcnRpY2FsXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjFcIi8+PGNpcmNsZSBjeD1cIjEyXCIgY3k9XCI1XCIgcj1cIjFcIi8+PGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxOVwiIHI9XCIxXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpmaWx0ZXJcIjogYDxwb2x5Z29uIHBvaW50cz1cIjIyLDMgMiwzIDEwLDEyLjQ2IDEwLDE5IDE0LDIxIDE0LDEyLjQ2XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpncmlkXCI6IGA8cmVjdCB4PVwiM1wiIHk9XCIzXCIgd2lkdGg9XCI3XCIgaGVpZ2h0PVwiN1wiLz48cmVjdCB4PVwiMTRcIiB5PVwiM1wiIHdpZHRoPVwiN1wiIGhlaWdodD1cIjdcIi8+PHJlY3QgeD1cIjE0XCIgeT1cIjE0XCIgd2lkdGg9XCI3XCIgaGVpZ2h0PVwiN1wiLz48cmVjdCB4PVwiM1wiIHk9XCIxNFwiIHdpZHRoPVwiN1wiIGhlaWdodD1cIjdcIi8+YCxcclxuICAgIFwibHVjaWRlOmxpc3RcIjogYDxsaW5lIHgxPVwiOFwiIHkxPVwiNlwiIHgyPVwiMjFcIiB5Mj1cIjZcIi8+PGxpbmUgeDE9XCI4XCIgeTE9XCIxMlwiIHgyPVwiMjFcIiB5Mj1cIjEyXCIvPjxsaW5lIHgxPVwiOFwiIHkxPVwiMThcIiB4Mj1cIjIxXCIgeTI9XCIxOFwiLz48bGluZSB4MT1cIjNcIiB5MT1cIjZcIiB4Mj1cIjMuMDFcIiB5Mj1cIjZcIi8+PGxpbmUgeDE9XCIzXCIgeTE9XCIxMlwiIHgyPVwiMy4wMVwiIHkyPVwiMTJcIi8+PGxpbmUgeDE9XCIzXCIgeTE9XCIxOFwiIHgyPVwiMy4wMVwiIHkyPVwiMThcIi8+YCxcclxuICAgIFwibHVjaWRlOmxheW91dFwiOiBgPHJlY3QgeD1cIjNcIiB5PVwiM1wiIHdpZHRoPVwiMThcIiBoZWlnaHQ9XCIxOFwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz48bGluZSB4MT1cIjNcIiB5MT1cIjlcIiB4Mj1cIjIxXCIgeTI9XCI5XCIvPjxsaW5lIHgxPVwiOVwiIHkxPVwiMjFcIiB4Mj1cIjlcIiB5Mj1cIjlcIi8+YCxcclxuICAgIFwibHVjaWRlOmNoZWNrLWNpcmNsZVwiOiBgPHBhdGggZD1cIk0yMiAxMS4wOFYxMmExMCAxMCAwIDEgMS01LjkzLTkuMTRcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjIyLDQgMTIsMTQuMDEgOSwxMS4wMVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6eC1jaXJjbGVcIjogYDxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiMTBcIi8+PGxpbmUgeDE9XCIxNVwiIHkxPVwiOVwiIHgyPVwiOVwiIHkyPVwiMTVcIi8+PGxpbmUgeDE9XCI5XCIgeTE9XCI5XCIgeDI9XCIxNVwiIHkyPVwiMTVcIi8+YCxcclxuICAgIFwibHVjaWRlOmFsZXJ0LWNpcmNsZVwiOiBgPGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiLz48bGluZSB4MT1cIjEyXCIgeTE9XCI4XCIgeDI9XCIxMlwiIHkyPVwiMTJcIi8+PGxpbmUgeDE9XCIxMlwiIHkxPVwiMTZcIiB4Mj1cIjEyLjAxXCIgeTI9XCIxNlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6aW5mb1wiOiBgPGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiLz48bGluZSB4MT1cIjEyXCIgeTE9XCIxNlwiIHgyPVwiMTJcIiB5Mj1cIjEyXCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjhcIiB4Mj1cIjEyLjAxXCIgeTI9XCI4XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpoZWxwLWNpcmNsZVwiOiBgPGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiLz48cGF0aCBkPVwiTTkuMDkgOWEzIDMgMCAwIDEgNS44MyAxYzAgMi0zIDMtMyAzXCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjE3XCIgeDI9XCIxMi4wMVwiIHkyPVwiMTdcIi8+YCxcclxuICAgIFwibHVjaWRlOnJvdGF0ZS1jY3dcIjogYDxwb2x5bGluZSBwb2ludHM9XCIxLDQgMSwxMCA3LDEwXCIvPjxwYXRoIGQ9XCJNMy41MSAxNWE5IDkgMCAxIDAgMi4xMy05LjM2TDEgMTBcIi8+YCxcclxuICAgIFwibHVjaWRlOnVuZG9cIjogYDxwb2x5bGluZSBwb2ludHM9XCIxLDQgMSwxMCA3LDEwXCIvPjxwYXRoIGQ9XCJNMy41MSAxNWE5IDkgMCAxIDAgMi4xMy05LjM2TDEgMTBcIi8+YCxcclxuICAgIFwibHVjaWRlOnJlZG9cIjogYDxwb2x5bGluZSBwb2ludHM9XCIyMyw0IDIzLDEwIDE3LDEwXCIvPjxwYXRoIGQ9XCJNMjAuNDkgOUE5IDkgMCAwIDAgNS42NCA1LjY0TDEgMTBcIi8+YCxcclxuICAgIFwibHVjaWRlOmZpbGUtdGV4dFwiOiBgPHBhdGggZD1cIk0xNC41IDJINmEyIDIgMCAwIDAtMiAydjE2YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDItMlY3LjVMMTQuNSAyelwiLz48cG9seWxpbmUgcG9pbnRzPVwiMTQsMiAxNCw4IDIwLDhcIi8+PGxpbmUgeDE9XCIxNlwiIHkxPVwiMTNcIiB4Mj1cIjhcIiB5Mj1cIjEzXCIvPjxsaW5lIHgxPVwiMTZcIiB5MT1cIjE3XCIgeDI9XCI4XCIgeTI9XCIxN1wiLz48bGluZSB4MT1cIjEwXCIgeTE9XCI5XCIgeDI9XCI4XCIgeTI9XCI5XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpmb2xkZXItb3BlblwiOiBgPHBhdGggZD1cIk0yMiAxOWEyIDIgMCAwIDEtMiAySDRhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJoNWwyIDNoOWEyIDIgMCAwIDEgMiAyelwiLz48cGF0aCBkPVwiTTIgN2gyMFwiLz5gLFxyXG4gICAgXCJsdWNpZGU6Y2xpcGJvYXJkXCI6IGA8cmVjdCB4PVwiOFwiIHk9XCIyXCIgd2lkdGg9XCI4XCIgaGVpZ2h0PVwiNFwiIHJ4PVwiMVwiIHJ5PVwiMVwiLz48cGF0aCBkPVwiTTE2IDRoMmEyIDIgMCAwIDEgMiAydjE0YTIgMiAwIDAgMS0yIDJINmEyIDIgMCAwIDEtMi0yVjZhMiAyIDAgMCAxIDItMmgyXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpwYXBlcmNsaXBcIjogYDxwYXRoIGQ9XCJNMjEuNDQgMTEuMDVsLTkuMTkgOS4xOWE2IDYgMCAwIDEtOC40OS04LjQ5bDkuMTktOS4xOWE0IDQgMCAwIDEgNS42NiA1LjY2TDkuNjQgMTYuMmEyIDIgMCAwIDEtMi44My0yLjgzbDguNDktOC40OVwiLz5gLFxyXG4gICAgLy8gQ29tbXVuaWNhdGlvbiBpY29uc1xyXG4gICAgXCJsdWNpZGU6bWVzc2FnZS1jaXJjbGVcIjogYDxwYXRoIGQ9XCJNMjEgMTEuNWE4LjM4IDguMzggMCAwIDEtLjkgMy44IDguNSA4LjUgMCAwIDEtNy42IDQuNyA4LjM4IDguMzggMCAwIDEtMy44LS45TDMgMjFsMS45LTUuN2E4LjM4IDguMzggMCAwIDEtLjktMy44IDguNSA4LjUgMCAwIDEgNC43LTcuNiA4LjM4IDguMzggMCAwIDEgMy44LS45aC41YTguNDggOC40OCAwIDAgMSA4IDh2LjV6XCIvPmAsXHJcbiAgICBcImx1Y2lkZTptZXNzYWdlLXNxdWFyZVwiOiBgPHBhdGggZD1cIk0yMSAxNWEyIDIgMCAwIDEtMiAySDdsLTQgNFY1YTIgMiAwIDAgMSAyLTJoMTRhMiAyIDAgMCAxIDIgMnpcIi8+YCxcclxuICAgIFwibHVjaWRlOnBob25lLWNhbGxcIjogYDxwYXRoIGQ9XCJNMjIgMTYuOTJ2M2EyIDIgMCAwIDEtMi4xOCAyIDE5Ljc5IDE5Ljc5IDAgMCAxLTguNjMtMy4wNyAxOS41IDE5LjUgMCAwIDEtNi02IDE5Ljc5IDE5Ljc5IDAgMCAxLTMuMDctOC42N0EyIDIgMCAwIDEgNC4xMSAyaDNhMiAyIDAgMCAxIDIgMS43MiAxMi44NCAxMi44NCAwIDAgMCAuNyAyLjgxIDIgMiAwIDAgMS0uNDUgMi4xMUw4LjA5IDkuOTFhMTYgMTYgMCAwIDAgNiA2bDEuMjctMS4yN2EyIDIgMCAwIDEgMi4xMS0uNDUgMTIuODQgMTIuODQgMCAwIDAgMi44MS43QTIgMiAwIDAgMSAyMiAxNi45MnpcIi8+PHBhdGggZD1cIk0xNC4wNSAyYTkgOSAwIDAgMSA4IDcuOTRcIi8+PHBhdGggZD1cIk0xNC4wNSA2QTUgNSAwIDAgMSAxOCAxMFwiLz5gLFxyXG4gICAgXCJsdWNpZGU6c2VuZFwiOiBgPGxpbmUgeDE9XCIyMlwiIHkxPVwiMlwiIHgyPVwiMTFcIiB5Mj1cIjEzXCIvPjxwb2x5Z29uIHBvaW50cz1cIjIyLDIgMTUsMjIgMTEsMTMgMiw5XCIvPmAsXHJcbiAgICAvLyBNZWRpYSBpY29uc1xyXG4gICAgXCJsdWNpZGU6ZmFzdC1mb3J3YXJkXCI6IGA8cG9seWdvbiBwb2ludHM9XCIxMywxOSAyMiwxMiAxMyw1XCIvPjxwb2x5Z29uIHBvaW50cz1cIjIsMTkgMTEsMTIgMiw1XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpyZXdpbmRcIjogYDxwb2x5Z29uIHBvaW50cz1cIjExLDE5IDIsMTIgMTEsNVwiLz48cG9seWdvbiBwb2ludHM9XCIyMiwxOSAxMywxMiAyMiw1XCIvPmAsXHJcbiAgICBcImx1Y2lkZTp2b2x1bWUteFwiOiBgPHBvbHlnb24gcG9pbnRzPVwiMTEsNSA2LDkgMiw5IDIsMTUgNiwxNSAxMSwxOVwiLz48bGluZSB4MT1cIjIzXCIgeTE9XCI5XCIgeDI9XCIxN1wiIHkyPVwiMTVcIi8+PGxpbmUgeDE9XCIxN1wiIHkxPVwiOVwiIHgyPVwiMjNcIiB5Mj1cIjE1XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpmaWxtXCI6IGA8cmVjdCB4PVwiMlwiIHk9XCIzXCIgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjE4XCIgcng9XCIyXCIgcnk9XCIyXCIvPjxsaW5lIHgxPVwiN1wiIHkxPVwiM1wiIHgyPVwiN1wiIHkyPVwiMjFcIi8+PGxpbmUgeDE9XCIxN1wiIHkxPVwiM1wiIHgyPVwiMTdcIiB5Mj1cIjIxXCIvPjxsaW5lIHgxPVwiMlwiIHkxPVwiOVwiIHgyPVwiN1wiIHkyPVwiOVwiLz48bGluZSB4MT1cIjJcIiB5MT1cIjE1XCIgeDI9XCI3XCIgeTI9XCIxNVwiLz48bGluZSB4MT1cIjE3XCIgeTE9XCI5XCIgeDI9XCIyMlwiIHkyPVwiOVwiLz48bGluZSB4MT1cIjE3XCIgeTE9XCIxNVwiIHgyPVwiMjJcIiB5Mj1cIjE1XCIvPmAsXHJcbiAgICAvLyBQZW9wbGUgaWNvbnNcclxuICAgIFwibHVjaWRlOnVzZXJzXCI6IGA8cGF0aCBkPVwiTTE2IDIxdi0yYTQgNCAwIDAgMC00LTRINmE0IDQgMCAwIDAtNCA0djJcIi8+PGNpcmNsZSBjeD1cIjlcIiBjeT1cIjdcIiByPVwiNFwiLz48cGF0aCBkPVwiTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODdcIi8+PHBhdGggZD1cIk0xNiAzLjEzYTQgNCAwIDAgMSAwIDcuNzVcIi8+YCxcclxuICAgIFwibHVjaWRlOnVzZXItcGx1c1wiOiBgPHBhdGggZD1cIk0xNiAyMXYtMmE0IDQgMCAwIDAtNC00SDVhNCA0IDAgMCAwLTQgNHYyXCIvPjxjaXJjbGUgY3g9XCI4LjVcIiBjeT1cIjdcIiByPVwiNFwiLz48bGluZSB4MT1cIjIwXCIgeTE9XCI4XCIgeDI9XCIyMFwiIHkyPVwiMTRcIi8+PGxpbmUgeDE9XCIyM1wiIHkxPVwiMTFcIiB4Mj1cIjE3XCIgeTI9XCIxMVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6dXNlci1taW51c1wiOiBgPHBhdGggZD1cIk0xNiAyMXYtMmE0IDQgMCAwIDAtNC00SDVhNCA0IDAgMCAwLTQgNHYyXCIvPjxjaXJjbGUgY3g9XCI4LjVcIiBjeT1cIjdcIiByPVwiNFwiLz48bGluZSB4MT1cIjIzXCIgeTE9XCIxMVwiIHgyPVwiMTdcIiB5Mj1cIjExXCIvPmAsXHJcbiAgICBcImx1Y2lkZTp1c2VyLWNoZWNrXCI6IGA8cGF0aCBkPVwiTTE2IDIxdi0yYTQgNCAwIDAgMC00LTRINWE0IDQgMCAwIDAtNCA0djJcIi8+PGNpcmNsZSBjeD1cIjguNVwiIGN5PVwiN1wiIHI9XCI0XCIvPjxwb2x5bGluZSBwb2ludHM9XCIxNywxMSAxOSwxMyAyMyw5XCIvPmAsXHJcbiAgICBcImx1Y2lkZTp1c2VyLXhcIjogYDxwYXRoIGQ9XCJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg1YTQgNCAwIDAgMC00IDR2MlwiLz48Y2lyY2xlIGN4PVwiOC41XCIgY3k9XCI3XCIgcj1cIjRcIi8+PGxpbmUgeDE9XCIxOFwiIHkxPVwiOFwiIHgyPVwiMjNcIiB5Mj1cIjEzXCIvPjxsaW5lIHgxPVwiMjNcIiB5MT1cIjhcIiB4Mj1cIjE4XCIgeTI9XCIxM1wiLz5gLFxyXG4gICAgXCJsdWNpZGU6Y3Jvd25cIjogYDxwYXRoIGQ9XCJNMiA0bDMgMTJoMTRsMy0xMi02IDctNC03LTQgNy02LTd6bTAgMTZoMjBcIi8+YCxcclxuICAgIFwibHVjaWRlOnNtaWxlXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIvPjxwYXRoIGQ9XCJNOCAxNHMxLjUgMiA0IDIgNC0yIDQtMlwiLz48bGluZSB4MT1cIjlcIiB5MT1cIjlcIiB4Mj1cIjkuMDFcIiB5Mj1cIjlcIi8+PGxpbmUgeDE9XCIxNVwiIHkxPVwiOVwiIHgyPVwiMTUuMDFcIiB5Mj1cIjlcIi8+YCxcclxuICAgIC8vIFRpbWUgaWNvbnNcclxuICAgIFwibHVjaWRlOnRpbWVyXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIvPjxwb2x5bGluZSBwb2ludHM9XCIxMiw2IDEyLDEyIDE2LDE0XCIvPmAsXHJcbiAgICBcImx1Y2lkZTphbGFybS1jbG9ja1wiOiBgPGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxM1wiIHI9XCI4XCIvPjxwYXRoIGQ9XCJNNSAzIDIgNlwiLz48cGF0aCBkPVwiTTIyIDZsLTMtM1wiLz48cGF0aCBkPVwiTTYuMzggMTguNyA0IDIxXCIvPjxwYXRoIGQ9XCJNMTcuNjQgMTguNjcgMjAgMjFcIi8+PHBhdGggZD1cIk05IDEzaDZcIi8+YCxcclxuICAgIFwibHVjaWRlOndhdGNoXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjZcIi8+PHBvbHlsaW5lIHBvaW50cz1cIjEyLDEwIDEyLDEyIDEzLDEzXCIvPjxwYXRoIGQ9XCJNMTYuMTMgNy42NmwtLjgxLTQuMDVhMiAyIDAgMCAwLTItMS42MWgtMi42OGEyIDIgMCAwIDAtMiAxLjYxbC0uNzggNC4wNVwiLz48cGF0aCBkPVwiTTcuODggMTYuMzZsLjggNGEyIDIgMCAwIDAgMiAxLjYxaDIuNzJhMiAyIDAgMCAwIDItMS42MWwuODEtNC4wNVwiLz5gLFxyXG4gICAgLy8gTG9jYXRpb24gaWNvbnNcclxuICAgIFwibHVjaWRlOm1hcC1waW5cIjogYDxwYXRoIGQ9XCJNMjAgMTBjMCA2LTggMTItOCAxMnMtOC02LTgtMTJhOCA4IDAgMCAxIDE2IDBaXCIvPjxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTBcIiByPVwiM1wiLz5gLFxyXG4gICAgXCJsdWNpZGU6bmF2aWdhdGlvblwiOiBgPHBvbHlnb24gcG9pbnRzPVwiMywxMSAyMiwyIDEzLDIxIDExLDEzXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjb21wYXNzXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIvPjxwb2x5Z29uIHBvaW50cz1cIjE2LjI0LDcuNzYgMTQuMTIsMTQuMTIgNy43NiwxNi4yNCA5Ljg4LDkuODhcIi8+YCxcclxuICAgIC8vIFRyYW5zcG9ydCBpY29uc1xyXG4gICAgXCJsdWNpZGU6cGxhbmVcIjogYDxwYXRoIGQ9XCJNMTcuOCAxOS4yIDE2IDExbDMuNS0zLjVDMjEgNiAyMS41IDQgMjEgM2MtMS0uNS0zIDAtNC41IDEuNUwxMyA4IDQuOCA2LjJjLS41LS4xLS45LjEtMS4xLjVsLS4zLjVjLS4yLjUtLjEgMSAuMyAxLjNMOSAxMmwtMiAzSDRsLTEgMSAzIDIgMiAzIDEtMXYtM2wzLTIgMy41IDUuM2MuMy40LjguNSAxLjMuM2wuNS0uMmMuNC0uMy42LS43LjUtMS4yelwiLz5gLFxyXG4gICAgXCJsdWNpZGU6dHJhaW5cIjogYDxyZWN0IHg9XCI0XCIgeT1cIjZcIiB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTBcIiByeD1cIjJcIiByeT1cIjJcIi8+PGNpcmNsZSBjeD1cIjhcIiBjeT1cIjE0XCIgcj1cIjJcIi8+PGNpcmNsZSBjeD1cIjE2XCIgY3k9XCIxNFwiIHI9XCIyXCIvPjxwYXRoIGQ9XCJNNCA2VjRhMiAyIDAgMCAxIDItMmgxMmEyIDIgMCAwIDEgMiAydjJcIi8+PHBhdGggZD1cIk0yIDE4aDJcIi8+PHBhdGggZD1cIk0yMCAxOGgyXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpiaWtlXCI6IGA8Y2lyY2xlIGN4PVwiMTguNVwiIGN5PVwiMTcuNVwiIHI9XCIzLjVcIi8+PGNpcmNsZSBjeD1cIjUuNVwiIGN5PVwiMTcuNVwiIHI9XCIzLjVcIi8+PGNpcmNsZSBjeD1cIjE1XCIgY3k9XCI1XCIgcj1cIjFcIi8+PHBhdGggZD1cIk0xMiAxNy41VjE0bC0zLTMgNC0zIDIgM2gyXCIvPmAsXHJcbiAgICAvLyBUZWNobm9sb2d5IGljb25zXHJcbiAgICBcImx1Y2lkZTpibHVldG9vdGhcIjogYDxwYXRoIGQ9XCJNNi41IDYuNSAxMiAxMmw1LjUtNS41TDEyIDEgNi41IDYuNXpNMTcuNSAxNy41IDEyIDEybC01LjUgNS41TDEyIDIzbDUuNS01LjV6XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpzbWFydHBob25lXCI6IGA8cmVjdCB4PVwiNVwiIHk9XCIyXCIgd2lkdGg9XCIxNFwiIGhlaWdodD1cIjIwXCIgcng9XCIyXCIgcnk9XCIyXCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjE4XCIgeDI9XCIxMi4wMVwiIHkyPVwiMThcIi8+YCxcclxuICAgIFwibHVjaWRlOmxhcHRvcFwiOiBgPHBhdGggZD1cIk0yMCAxNlY3YTIgMiAwIDAgMC0yLTJINmEyIDIgMCAwIDAtMiAydjltMTYgMEg0bTE2IDAgMS4yOCAyLjU1YTEgMSAwIDAgMS0uOSAxLjQ1SDMuNjJhMSAxIDAgMCAxLS45LTEuNDVMNCAxNlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6bW9uaXRvclwiOiBgPHJlY3QgeD1cIjJcIiB5PVwiM1wiIHdpZHRoPVwiMjBcIiBoZWlnaHQ9XCIxNFwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz48bGluZSB4MT1cIjhcIiB5MT1cIjIxXCIgeDI9XCIxNlwiIHkyPVwiMjFcIi8+PGxpbmUgeDE9XCIxMlwiIHkxPVwiMTdcIiB4Mj1cIjEyXCIgeTI9XCIyMVwiLz5gLFxyXG4gICAgXCJsdWNpZGU6cHJpbnRlclwiOiBgPHBvbHlsaW5lIHBvaW50cz1cIjYsOSA2LDIgMTgsMiAxOCw5XCIvPjxwYXRoIGQ9XCJNNiAxOEg0YTIgMiAwIDAgMS0yLTJ2LTVhMiAyIDAgMCAxIDItMmgxNmEyIDIgMCAwIDEgMiAydjVhMiAyIDAgMCAxLTIgMmgtMlwiLz48cmVjdCB4PVwiNlwiIHk9XCIxNFwiIHdpZHRoPVwiMTJcIiBoZWlnaHQ9XCI4XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpoYXJkLWRyaXZlXCI6IGA8bGluZSB4MT1cIjIyXCIgeTE9XCIxMlwiIHgyPVwiMlwiIHkyPVwiMTJcIi8+PHBhdGggZD1cIk01LjQ1IDUuMTEgMiAxMnY2YTIgMiAwIDAgMCAyIDJoMTZhMiAyIDAgMCAwIDItMnYtNmwtMy40NS02Ljg5QTIgMiAwIDAgMCAxNi43NiA0SDcuMjRhMiAyIDAgMCAwLTEuNzkgMS4xMXpcIi8+PGxpbmUgeDE9XCI2XCIgeTE9XCIxNlwiIHgyPVwiNi4wMVwiIHkyPVwiMTZcIi8+PGxpbmUgeDE9XCIxMFwiIHkxPVwiMTZcIiB4Mj1cIjEwLjAxXCIgeTI9XCIxNlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6c2VydmVyXCI6IGA8cmVjdCB4PVwiMlwiIHk9XCIyXCIgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjhcIiByeD1cIjJcIiByeT1cIjJcIi8+PHJlY3QgeD1cIjJcIiB5PVwiMTRcIiB3aWR0aD1cIjIwXCIgaGVpZ2h0PVwiOFwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz48bGluZSB4MT1cIjZcIiB5MT1cIjZcIiB4Mj1cIjYuMDFcIiB5Mj1cIjZcIi8+PGxpbmUgeDE9XCI2XCIgeTE9XCIxOFwiIHgyPVwiNi4wMVwiIHkyPVwiMThcIi8+YCxcclxuICAgIFwibHVjaWRlOmRhdGFiYXNlXCI6IGA8ZWxsaXBzZSBjeD1cIjEyXCIgY3k9XCI1XCIgcng9XCI5XCIgcnk9XCIzXCIvPjxwYXRoIGQ9XCJNMyA1djE0YzAgMS42NiA0LjAzIDMgOSAzczktMS4zNCA5LTNWNVwiLz48cGF0aCBkPVwiTTMgMTJjMCAxLjY2IDQuMDMgMyA5IDNzOS0xLjM0IDktM1wiLz5gLFxyXG4gICAgLy8gQ29tbWVyY2UgaWNvbnNcclxuICAgIFwibHVjaWRlOnNob3BwaW5nLWNhcnRcIjogYDxjaXJjbGUgY3g9XCI4XCIgY3k9XCIyMVwiIHI9XCIxXCIvPjxjaXJjbGUgY3g9XCIxOVwiIGN5PVwiMjFcIiByPVwiMVwiLz48cGF0aCBkPVwiTTIuMDUgMi4wNWgybDIuNjYgMTIuNDJhMiAyIDAgMCAwIDIgMS41OGg5Ljc4YTIgMiAwIDAgMCAxLjk1LTEuNTdsMS42NS03LjQzSDUuMTJcIi8+YCxcclxuICAgIFwibHVjaWRlOnNob3BwaW5nLWJhZ1wiOiBgPHBhdGggZD1cIk02IDIgMyA2djE0YTIgMiAwIDAgMCAyIDJoMTRhMiAyIDAgMCAwIDItMlY2bC0zLTR6XCIvPjxsaW5lIHgxPVwiM1wiIHkxPVwiNlwiIHgyPVwiMjFcIiB5Mj1cIjZcIi8+PHBhdGggZD1cIk0xNiAxMGE0IDQgMCAwIDEtOCAwXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjcmVkaXQtY2FyZFwiOiBgPHJlY3QgeD1cIjFcIiB5PVwiNFwiIHdpZHRoPVwiMjJcIiBoZWlnaHQ9XCIxNlwiIHJ4PVwiMlwiIHJ5PVwiMlwiLz48bGluZSB4MT1cIjFcIiB5MT1cIjEwXCIgeDI9XCIyM1wiIHkyPVwiMTBcIi8+YCxcclxuICAgIFwibHVjaWRlOmRvbGxhci1zaWduXCI6IGA8bGluZSB4MT1cIjEyXCIgeTE9XCIxXCIgeDI9XCIxMlwiIHkyPVwiMjNcIi8+PHBhdGggZD1cIk0xNyA1SDkuNWEzLjUgMy41IDAgMCAwIDAgN2g1YTMuNSAzLjUgMCAwIDEgMCA3SDZcIi8+YCxcclxuICAgIFwibHVjaWRlOnRhZ1wiOiBgPHBhdGggZD1cIk0xMiAySDJ2MTBsOS4yOSA5LjI5Yy45NC45NCAyLjQ4Ljk0IDMuNDIgMGw2LjU4LTYuNThjLjk0LS45NC45NC0yLjQ4IDAtMy40MkwxMiAyWlwiLz48cGF0aCBkPVwiTTcgN2guMDFcIi8+YCxcclxuICAgIFwibHVjaWRlOmdpZnRcIjogYDxwb2x5bGluZSBwb2ludHM9XCIyMCwxMiAyMCwyMiA0LDIyIDQsMTJcIi8+PHJlY3QgeD1cIjJcIiB5PVwiN1wiIHdpZHRoPVwiMjBcIiBoZWlnaHQ9XCI1XCIvPjxsaW5lIHgxPVwiMTJcIiB5MT1cIjIyXCIgeDI9XCIxMlwiIHkyPVwiN1wiLz48cGF0aCBkPVwiTTEyIDdINy41YTIuNSAyLjUgMCAwIDEgMC01QzExIDIgMTIgNyAxMiA3elwiLz48cGF0aCBkPVwiTTEyIDdoNC41YTIuNSAyLjUgMCAwIDAgMC01QzEzIDIgMTIgNyAxMiA3elwiLz5gLFxyXG4gICAgLy8gV2VhdGhlciBpY29uc1xyXG4gICAgXCJsdWNpZGU6c3VuXCI6IGA8Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjVcIi8+PGxpbmUgeDE9XCIxMlwiIHkxPVwiMVwiIHgyPVwiMTJcIiB5Mj1cIjNcIi8+PGxpbmUgeDE9XCIxMlwiIHkxPVwiMjFcIiB4Mj1cIjEyXCIgeTI9XCIyM1wiLz48bGluZSB4MT1cIjQuMjJcIiB5MT1cIjQuMjJcIiB4Mj1cIjUuNjRcIiB5Mj1cIjUuNjRcIi8+PGxpbmUgeDE9XCIxOC4zNlwiIHkxPVwiMTguMzZcIiB4Mj1cIjE5Ljc4XCIgeTI9XCIxOS43OFwiLz48bGluZSB4MT1cIjFcIiB5MT1cIjEyXCIgeDI9XCIzXCIgeTI9XCIxMlwiLz48bGluZSB4MT1cIjIxXCIgeTE9XCIxMlwiIHgyPVwiMjNcIiB5Mj1cIjEyXCIvPjxsaW5lIHgxPVwiNC4yMlwiIHkxPVwiMTkuNzhcIiB4Mj1cIjUuNjRcIiB5Mj1cIjE4LjM2XCIvPjxsaW5lIHgxPVwiMTguMzZcIiB5MT1cIjUuNjRcIiB4Mj1cIjE5Ljc4XCIgeTI9XCI0LjIyXCIvPmAsXHJcbiAgICBcImx1Y2lkZTptb29uXCI6IGA8cGF0aCBkPVwiTTEyIDNhNi4zNjQgNi4zNjQgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjbG91ZFwiOiBgPHBhdGggZD1cIk0xNy41IDE5SDlhNyA3IDAgMSAxIDYuNzEtOWgxLjc5YTQuNSA0LjUgMCAxIDEgMCA5WlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6Y2xvdWQtcmFpblwiOiBgPHBhdGggZD1cIk00IDE0Ljg5OUE3IDcgMCAxIDEgMTUuNzEgOGgxLjc5YTQuNSA0LjUgMCAwIDEgMi41IDguMjQyXCIvPjxwYXRoIGQ9XCJNMTYgMTR2Nm0tNC02djZtLTQtNnY2XCIvPmAsXHJcbiAgICBcImx1Y2lkZTpjbG91ZC1zbm93XCI6IGA8cGF0aCBkPVwiTTQgMTQuODk5QTcgNyAwIDEgMSAxNS43MSA4aDEuNzlhNC41IDQuNSAwIDAgMSAyLjUgOC4yNDJcIi8+PHBhdGggZD1cIk04IDE1aC4wMU04IDE5aC4wMU0xMiAxN2guMDFNMTIgMjFoLjAxTTE2IDE1aC4wMU0xNiAxOWguMDFcIi8+YCxcclxuICAgIFwibHVjaWRlOnphcFwiOiBgPHBvbHlnb24gcG9pbnRzPVwiMTMsMiAzLDE0IDEyLDE0IDExLDIyIDIxLDEwIDEyLDEwXCIvPmAsXHJcbiAgICBcImx1Y2lkZTp0cmVlLXBpbmVcIjogYDxwYXRoIGQ9XCJNMTcgMTRoLjAxTTE3IDN2NC41bC0yLTJMMTMgN1YzXCIvPjxwYXRoIGQ9XCJNMiAxN2gyMGwtMi00VjkuNWwtMi41LTEuNUwxNiA5LjVWNmwtMi0yLTIgMnYzLjVMMTAuNSA4IDggOS41VjEzbC0yIDRaXCIvPmAsXHJcbiAgICBcImx1Y2lkZTpmbG93ZXJcIjogYDxwYXRoIGQ9XCJNMTIgNy41YTQuNSA0LjUgMCAxIDEgNC41IDQuNU0xMiA3LjVBNC41IDQuNSAwIDEgMCA3LjUgMTJNMTIgNy41VjlhMyAzIDAgMCAwIDMgM2gxLjVNMTIgNy41VjlhMyAzIDAgMCAxLTMgM0g3LjVcIi8+PGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIzXCIvPjxwYXRoIGQ9XCJNMTIgMTYuNWE0LjUgNC41IDAgMSAxLTQuNS00LjVNMTIgMTYuNWE0LjUgNC41IDAgMSAwIDQuNS00LjVNMTIgMTYuNVYxNWEzIDMgMCAwIDAtMy0zSDcuNU0xMiAxNi41VjE1YTMgMyAwIDAgMSAzLTNoMS41XCIvPmAsXHJcbiAgICAvLyBUb29scyBpY29uc1xyXG4gICAgXCJsdWNpZGU6c2Npc3NvcnNcIjogYDxjaXJjbGUgY3g9XCI2XCIgY3k9XCI2XCIgcj1cIjNcIi8+PGNpcmNsZSBjeD1cIjZcIiBjeT1cIjE4XCIgcj1cIjNcIi8+PGxpbmUgeDE9XCIyMFwiIHkxPVwiNFwiIHgyPVwiOC4xMlwiIHkyPVwiMTUuODhcIi8+PGxpbmUgeDE9XCIxNC40N1wiIHkxPVwiMTQuNDhcIiB4Mj1cIjIwXCIgeTI9XCIyMFwiLz48bGluZSB4MT1cIjguMTJcIiB5MT1cIjguMTJcIiB4Mj1cIjEyXCIgeTI9XCIxMlwiLz5gLFxyXG4gICAgXCJsdWNpZGU6d3JlbmNoXCI6IGA8cGF0aCBkPVwiTTE0LjcgNi4zYTEgMSAwIDAgMCAwIDEuNGwxLjYgMS42YTEgMSAwIDAgMCAxLjQgMGwzLjc3LTMuNzdhNiA2IDAgMCAxLTcuOTQgNy45NGwtNi45MSA2LjkxYTIuMTIgMi4xMiAwIDAgMS0zLTNsNi45MS02LjkxYTYgNiAwIDAgMSA3Ljk0LTcuOTRsLTMuNzYgMy43NnpcIi8+YCxcclxuICAgIFwibHVjaWRlOmhhbW1lclwiOiBgPHBhdGggZD1cIk0xNSAxMmwtOC41LTguNWMtLjgzLS44My0yLjE3LS44My0zIDAgMCAwIDAgMCAwIDBsLTEuNSAxLjVjLS44My44My0uODMgMi4xNyAwIDNsOC41IDguNVwiLz48cGF0aCBkPVwiTTE3LjY0IDE1TDIyIDEwLjY0XCIvPjxwYXRoIGQ9XCJNMjAuOTEgMTEuN2wtMS4yNS0xLjI1TDIxLjkxIDguMmwyLjUgMi41LTMuNSAxelwiLz5gLFxyXG4gICAgXCJsdWNpZGU6a2V5XCI6IGA8Y2lyY2xlIGN4PVwiNy41XCIgY3k9XCIxNS41XCIgcj1cIjUuNVwiLz48cGF0aCBkPVwiTTIxIDIgMTcuNiA1LjRhNy41IDcuNSAwIDEgMS0zLjQgMy40TDIgMjFcIi8+YCxcclxuICAgIFwibHVjaWRlOmxvY2tcIjogYDxyZWN0IHg9XCIzXCIgeT1cIjExXCIgd2lkdGg9XCIxOFwiIGhlaWdodD1cIjExXCIgcng9XCIyXCIgcnk9XCIyXCIvPjxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTZcIiByPVwiMVwiLz48cGF0aCBkPVwiTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0XCIvPmAsXHJcbiAgICBcImx1Y2lkZTp1bmxvY2tcIjogYDxyZWN0IHg9XCIzXCIgeT1cIjExXCIgd2lkdGg9XCIxOFwiIGhlaWdodD1cIjExXCIgcng9XCIyXCIgcnk9XCIyXCIvPjxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTZcIiByPVwiMVwiLz48cGF0aCBkPVwiTTcgMTFWN2E1IDUgMCAwIDEgOS45LTFcIi8+YCxcclxuICAgIFwibHVjaWRlOnNoaWVsZFwiOiBgPHBhdGggZD1cIk0xMiAyMnM4LTQgOC0xMFY1bC04LTMtOCAzdjdjMCA2IDggMTAgOCAxMHpcIi8+YCxcclxuICAgIFwibHVjaWRlOmV5ZVwiOiBgPHBhdGggZD1cIk0xIDEyczQtOCAxMS04IDExIDggMTEgOC00IDgtMTEgOC0xMS04LTExLTh6XCIvPjxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiM1wiLz5gLFxyXG4gICAgXCJsdWNpZGU6ZXllLW9mZlwiOiBgPHBhdGggZD1cIk0xNy45NCAxNy45NEExMC4wNyAxMC4wNyAwIDAgMSAxMiAyMGMtNyAwLTExLTgtMTEtOGExOC40NSAxOC40NSAwIDAgMSA1LjA2LTUuOTRNOS45IDQuMjRBOS4xMiA5LjEyIDAgMCAxIDEyIDRjNyAwIDExIDggMTEgOGExOC41IDE4LjUgMCAwIDEtMi4xNiAzLjE5bS02LjcyLTEuMDdhMyAzIDAgMSAxLTQuMjQtNC4yNFwiLz48bGluZSB4MT1cIjFcIiB5MT1cIjFcIiB4Mj1cIjIzXCIgeTI9XCIyM1wiLz5gLFxyXG4gIH07XHJcblxyXG4gIHJldHVybiBpY29uUGF0aHNbaWNvbk5hbWVdIHx8IGA8cmVjdCB4PVwiMlwiIHk9XCIyXCIgd2lkdGg9XCIyMFwiIGhlaWdodD1cIjIwXCIgcng9XCIyXCIgcnk9XCIyXCIgc3Ryb2tlLWRhc2hhcnJheT1cIjIsMlwiLz5gO1xyXG59O1xyXG5cclxuY29uc3QgYnVpbGRFZGl0b3IgPSAoe1xyXG4gIHNhdmUsXHJcbiAgdW5kbyxcclxuICByZWRvLFxyXG4gIGNhblJlZG8sXHJcbiAgY2FuVW5kbyxcclxuICBhdXRvWm9vbSxcclxuICBjb3B5LFxyXG4gIHBhc3RlLFxyXG4gIGNhbnZhcyxcclxuICBmaWxsQ29sb3IsXHJcbiAgZm9udEZhbWlseSxcclxuICBzZXRGb250RmFtaWx5LFxyXG4gIHNldEZpbGxDb2xvcixcclxuICBzdHJva2VDb2xvcixcclxuICBzZXRTdHJva2VDb2xvcixcclxuICBzdHJva2VXaWR0aCxcclxuICBzZXRTdHJva2VXaWR0aCxcclxuICBzZWxlY3RlZE9iamVjdHMsXHJcbiAgc3Ryb2tlRGFzaEFycmF5LFxyXG4gIHNldFN0cm9rZURhc2hBcnJheSxcclxufTogQnVpbGRFZGl0b3JQcm9wcyk6IEVkaXRvciA9PiB7XHJcbiAgY29uc3QgZ2VuZXJhdGVTYXZlT3B0aW9ucyA9ICgpID0+IHtcclxuICAgIGNvbnN0IHsgd2lkdGgsIGhlaWdodCwgbGVmdCwgdG9wIH0gPSBnZXRXb3Jrc3BhY2UoKSBhcyBmYWJyaWMuUmVjdDtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBuYW1lOiBcIkltYWdlXCIsXHJcbiAgICAgIGZvcm1hdDogXCJwbmdcIixcclxuICAgICAgcXVhbGl0eTogMSxcclxuICAgICAgd2lkdGgsXHJcbiAgICAgIGhlaWdodCxcclxuICAgICAgbGVmdCxcclxuICAgICAgdG9wLFxyXG4gICAgfTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzYXZlUG5nID0gKCkgPT4ge1xyXG4gICAgY29uc3Qgb3B0aW9ucyA9IGdlbmVyYXRlU2F2ZU9wdGlvbnMoKTtcclxuXHJcbiAgICBjYW52YXMuc2V0Vmlld3BvcnRUcmFuc2Zvcm0oWzEsIDAsIDAsIDEsIDAsIDBdKTtcclxuICAgIGNvbnN0IGRhdGFVcmwgPSBjYW52YXMudG9EYXRhVVJMKG9wdGlvbnMpO1xyXG5cclxuICAgIGRvd25sb2FkRmlsZShkYXRhVXJsLCBcInBuZ1wiKTtcclxuICAgIGF1dG9ab29tKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2F2ZVN2ZyA9ICgpID0+IHtcclxuICAgIGNvbnN0IG9wdGlvbnMgPSBnZW5lcmF0ZVNhdmVPcHRpb25zKCk7XHJcblxyXG4gICAgY2FudmFzLnNldFZpZXdwb3J0VHJhbnNmb3JtKFsxLCAwLCAwLCAxLCAwLCAwXSk7XHJcbiAgICBjb25zdCBkYXRhVXJsID0gY2FudmFzLnRvRGF0YVVSTChvcHRpb25zKTtcclxuXHJcbiAgICBkb3dubG9hZEZpbGUoZGF0YVVybCwgXCJzdmdcIik7XHJcbiAgICBhdXRvWm9vbSgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHNhdmVKcGcgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBvcHRpb25zID0gZ2VuZXJhdGVTYXZlT3B0aW9ucygpO1xyXG5cclxuICAgIGNhbnZhcy5zZXRWaWV3cG9ydFRyYW5zZm9ybShbMSwgMCwgMCwgMSwgMCwgMF0pO1xyXG4gICAgY29uc3QgZGF0YVVybCA9IGNhbnZhcy50b0RhdGFVUkwob3B0aW9ucyk7XHJcblxyXG4gICAgZG93bmxvYWRGaWxlKGRhdGFVcmwsIFwianBnXCIpO1xyXG4gICAgYXV0b1pvb20oKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzYXZlSnNvbiA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IGRhdGFVcmwgPSBjYW52YXMudG9KU09OKEpTT05fS0VZUyk7XHJcblxyXG4gICAgYXdhaXQgdHJhbnNmb3JtVGV4dChkYXRhVXJsLm9iamVjdHMpO1xyXG4gICAgY29uc3QgZmlsZVN0cmluZyA9IGBkYXRhOnRleHQvanNvbjtjaGFyc2V0PXV0Zi04LCR7ZW5jb2RlVVJJQ29tcG9uZW50KFxyXG4gICAgICBKU09OLnN0cmluZ2lmeShkYXRhVXJsLCBudWxsLCBcIlxcdFwiKSxcclxuICAgICl9YDtcclxuICAgIGRvd25sb2FkRmlsZShmaWxlU3RyaW5nLCBcImpzb25cIik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbG9hZEpzb24gPSAoanNvbjogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShqc29uKTtcclxuXHJcbiAgICBjYW52YXMubG9hZEZyb21KU09OKGRhdGEsICgpID0+IHtcclxuICAgICAgYXV0b1pvb20oKTtcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdldFdvcmtzcGFjZSA9ICgpID0+IHtcclxuICAgIHJldHVybiBjYW52YXNcclxuICAgIC5nZXRPYmplY3RzKClcclxuICAgIC5maW5kKChvYmplY3QpID0+IG9iamVjdC5uYW1lID09PSBcImNsaXBcIik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY2VudGVyID0gKG9iamVjdDogZmFicmljLk9iamVjdCkgPT4ge1xyXG4gICAgY29uc3Qgd29ya3NwYWNlID0gZ2V0V29ya3NwYWNlKCk7XHJcbiAgICBjb25zdCBjZW50ZXIgPSB3b3Jrc3BhY2U/LmdldENlbnRlclBvaW50KCk7XHJcblxyXG4gICAgaWYgKCFjZW50ZXIpIHJldHVybjtcclxuXHJcbiAgICAvLyBAdHMtaWdub3JlXHJcbiAgICBjYW52YXMuX2NlbnRlck9iamVjdChvYmplY3QsIGNlbnRlcik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgYWRkVG9DYW52YXMgPSAob2JqZWN0OiBmYWJyaWMuT2JqZWN0KSA9PiB7XHJcbiAgICBjZW50ZXIob2JqZWN0KTtcclxuICAgIGNhbnZhcy5hZGQob2JqZWN0KTtcclxuICAgIGNhbnZhcy5zZXRBY3RpdmVPYmplY3Qob2JqZWN0KTtcclxuICB9O1xyXG5cclxuICByZXR1cm4ge1xyXG4gICAgc2F2ZVBuZyxcclxuICAgIHNhdmVKcGcsXHJcbiAgICBzYXZlU3ZnLFxyXG4gICAgc2F2ZUpzb24sXHJcbiAgICBsb2FkSnNvbixcclxuICAgIGNhblVuZG8sXHJcbiAgICBjYW5SZWRvLFxyXG4gICAgYXV0b1pvb20sXHJcbiAgICBnZXRXb3Jrc3BhY2UsXHJcbiAgICB6b29tSW46ICgpID0+IHtcclxuICAgICAgbGV0IHpvb21SYXRpbyA9IGNhbnZhcy5nZXRab29tKCk7XHJcbiAgICAgIHpvb21SYXRpbyArPSAwLjA1O1xyXG4gICAgICBjb25zdCBjZW50ZXIgPSBjYW52YXMuZ2V0Q2VudGVyKCk7XHJcbiAgICAgIGNhbnZhcy56b29tVG9Qb2ludChcclxuICAgICAgICBuZXcgZmFicmljLlBvaW50KGNlbnRlci5sZWZ0LCBjZW50ZXIudG9wKSxcclxuICAgICAgICB6b29tUmF0aW8gPiAxID8gMSA6IHpvb21SYXRpb1xyXG4gICAgICApO1xyXG4gICAgfSxcclxuICAgIHpvb21PdXQ6ICgpID0+IHtcclxuICAgICAgbGV0IHpvb21SYXRpbyA9IGNhbnZhcy5nZXRab29tKCk7XHJcbiAgICAgIHpvb21SYXRpbyAtPSAwLjA1O1xyXG4gICAgICBjb25zdCBjZW50ZXIgPSBjYW52YXMuZ2V0Q2VudGVyKCk7XHJcbiAgICAgIGNhbnZhcy56b29tVG9Qb2ludChcclxuICAgICAgICBuZXcgZmFicmljLlBvaW50KGNlbnRlci5sZWZ0LCBjZW50ZXIudG9wKSxcclxuICAgICAgICB6b29tUmF0aW8gPCAwLjIgPyAwLjIgOiB6b29tUmF0aW8sXHJcbiAgICAgICk7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlU2l6ZTogKHZhbHVlOiB7IHdpZHRoOiBudW1iZXI7IGhlaWdodDogbnVtYmVyIH0pID0+IHtcclxuICAgICAgY29uc3Qgd29ya3NwYWNlID0gZ2V0V29ya3NwYWNlKCk7XHJcblxyXG4gICAgICB3b3Jrc3BhY2U/LnNldCh2YWx1ZSk7XHJcbiAgICAgIGF1dG9ab29tKCk7XHJcbiAgICAgIHNhdmUoKTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VCYWNrZ3JvdW5kOiAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBjb25zdCB3b3Jrc3BhY2UgPSBnZXRXb3Jrc3BhY2UoKTtcclxuICAgICAgd29ya3NwYWNlPy5zZXQoeyBmaWxsOiB2YWx1ZSB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgICBzYXZlKCk7XHJcbiAgICB9LFxyXG4gICAgZW5hYmxlRHJhd2luZ01vZGU6ICgpID0+IHtcclxuICAgICAgY2FudmFzLmRpc2NhcmRBY3RpdmVPYmplY3QoKTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgICBjYW52YXMuaXNEcmF3aW5nTW9kZSA9IHRydWU7XHJcbiAgICAgIGNhbnZhcy5mcmVlRHJhd2luZ0JydXNoLndpZHRoID0gc3Ryb2tlV2lkdGg7XHJcbiAgICAgIGNhbnZhcy5mcmVlRHJhd2luZ0JydXNoLmNvbG9yID0gc3Ryb2tlQ29sb3I7XHJcbiAgICB9LFxyXG4gICAgZGlzYWJsZURyYXdpbmdNb2RlOiAoKSA9PiB7XHJcbiAgICAgIGNhbnZhcy5pc0RyYXdpbmdNb2RlID0gZmFsc2U7XHJcbiAgICB9LFxyXG4gICAgb25VbmRvOiAoKSA9PiB1bmRvKCksXHJcbiAgICBvblJlZG86ICgpID0+IHJlZG8oKSxcclxuICAgIG9uQ29weTogKCkgPT4gY29weSgpLFxyXG4gICAgb25QYXN0ZTogKCkgPT4gcGFzdGUoKSxcclxuICAgIGNoYW5nZUltYWdlRmlsdGVyOiAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBjb25zdCBvYmplY3RzID0gY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKTtcclxuICAgICAgb2JqZWN0cy5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBpZiAob2JqZWN0LnR5cGUgPT09IFwiaW1hZ2VcIikge1xyXG4gICAgICAgICAgY29uc3QgaW1hZ2VPYmplY3QgPSBvYmplY3QgYXMgZmFicmljLkltYWdlO1xyXG5cclxuICAgICAgICAgIGNvbnN0IGVmZmVjdCA9IGNyZWF0ZUZpbHRlcih2YWx1ZSk7XHJcblxyXG4gICAgICAgICAgaW1hZ2VPYmplY3QuZmlsdGVycyA9IGVmZmVjdCA/IFtlZmZlY3RdIDogW107XHJcbiAgICAgICAgICBpbWFnZU9iamVjdC5hcHBseUZpbHRlcnMoKTtcclxuICAgICAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgfSxcclxuICAgIGFkZEltYWdlOiAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBmYWJyaWMuSW1hZ2UuZnJvbVVSTChcclxuICAgICAgICB2YWx1ZSxcclxuICAgICAgICAoaW1hZ2UpID0+IHtcclxuICAgICAgICAgIGNvbnN0IHdvcmtzcGFjZSA9IGdldFdvcmtzcGFjZSgpO1xyXG5cclxuICAgICAgICAgIGltYWdlLnNjYWxlVG9XaWR0aCh3b3Jrc3BhY2U/LndpZHRoIHx8IDApO1xyXG4gICAgICAgICAgaW1hZ2Uuc2NhbGVUb0hlaWdodCh3b3Jrc3BhY2U/LmhlaWdodCB8fCAwKTtcclxuXHJcbiAgICAgICAgICBhZGRUb0NhbnZhcyhpbWFnZSk7XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBjcm9zc09yaWdpbjogXCJhbm9ueW1vdXNcIixcclxuICAgICAgICB9LFxyXG4gICAgICApO1xyXG4gICAgfSxcclxuICAgIGRlbGV0ZTogKCkgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4gY2FudmFzLnJlbW92ZShvYmplY3QpKTtcclxuICAgICAgY2FudmFzLmRpc2NhcmRBY3RpdmVPYmplY3QoKTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGFkZFRleHQ6ICh2YWx1ZSwgb3B0aW9ucykgPT4ge1xyXG4gICAgICBjb25zdCBvYmplY3QgPSBuZXcgZmFicmljLlRleHRib3godmFsdWUsIHtcclxuICAgICAgICAuLi5URVhUX09QVElPTlMsXHJcbiAgICAgICAgZmlsbDogZmlsbENvbG9yLFxyXG4gICAgICAgIC4uLm9wdGlvbnMsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVPcGFjaXR5OiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiAxO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcIm9wYWNpdHlcIikgfHwgMTtcclxuXHJcbiAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VGb250U2l6ZTogKHZhbHVlOiBudW1iZXIpID0+IHtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBpZiAoaXNUZXh0VHlwZShvYmplY3QudHlwZSkpIHtcclxuICAgICAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBmb250U2l6ZSBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgZm9udFNpemU6IHZhbHVlIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVGb250U2l6ZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gRk9OVF9TSVpFO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBmb250U2l6ZSBleGlzdHMuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwiZm9udFNpemVcIikgfHwgRk9OVF9TSVpFO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZVRleHRBbGlnbjogKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBpZiAoaXNUZXh0VHlwZShvYmplY3QudHlwZSkpIHtcclxuICAgICAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCB0ZXh0QWxpZ24gZXhpc3RzLlxyXG4gICAgICAgICAgb2JqZWN0LnNldCh7IHRleHRBbGlnbjogdmFsdWUgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZVRleHRBbGlnbjogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gXCJsZWZ0XCI7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIHRleHRBbGlnbiBleGlzdHMuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwidGV4dEFsaWduXCIpIHx8IFwibGVmdFwiO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZvbnRVbmRlcmxpbmU6ICh2YWx1ZTogYm9vbGVhbikgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIHVuZGVybGluZSBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgdW5kZXJsaW5lOiB2YWx1ZSB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlRm9udFVuZGVybGluZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIHVuZGVybGluZSBleGlzdHMuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwidW5kZXJsaW5lXCIpIHx8IGZhbHNlO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZvbnRMaW5ldGhyb3VnaDogKHZhbHVlOiBib29sZWFuKSA9PiB7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgaWYgKGlzVGV4dFR5cGUob2JqZWN0LnR5cGUpKSB7XHJcbiAgICAgICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgbGluZXRocm91Z2ggZXhpc3RzLlxyXG4gICAgICAgICAgb2JqZWN0LnNldCh7IGxpbmV0aHJvdWdoOiB2YWx1ZSB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlRm9udExpbmV0aHJvdWdoOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgbGluZXRocm91Z2ggZXhpc3RzLlxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcImxpbmV0aHJvdWdoXCIpIHx8IGZhbHNlO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZvbnRTdHlsZTogKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBpZiAoaXNUZXh0VHlwZShvYmplY3QudHlwZSkpIHtcclxuICAgICAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBmb250U3R5bGUgZXhpc3RzLlxyXG4gICAgICAgICAgb2JqZWN0LnNldCh7IGZvbnRTdHlsZTogdmFsdWUgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZUZvbnRTdHlsZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gXCJub3JtYWxcIjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgZm9udFN0eWxlIGV4aXN0cy5cclxuICAgICAgY29uc3QgdmFsdWUgPSBzZWxlY3RlZE9iamVjdC5nZXQoXCJmb250U3R5bGVcIikgfHwgXCJub3JtYWxcIjtcclxuXHJcbiAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VGb250V2VpZ2h0OiAodmFsdWU6IG51bWJlcikgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIGZvbnRXZWlnaHQgZXhpc3RzLlxyXG4gICAgICAgICAgb2JqZWN0LnNldCh7IGZvbnRXZWlnaHQ6IHZhbHVlIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VPcGFjaXR5OiAodmFsdWU6IG51bWJlcikgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIG9iamVjdC5zZXQoeyBvcGFjaXR5OiB2YWx1ZSB9KTtcclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBicmluZ0ZvcndhcmQ6ICgpID0+IHtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBjYW52YXMuYnJpbmdGb3J3YXJkKG9iamVjdCk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgICBcclxuICAgICAgY29uc3Qgd29ya3NwYWNlID0gZ2V0V29ya3NwYWNlKCk7XHJcbiAgICAgIHdvcmtzcGFjZT8uc2VuZFRvQmFjaygpO1xyXG4gICAgfSxcclxuICAgIHNlbmRCYWNrd2FyZHM6ICgpID0+IHtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBjYW52YXMuc2VuZEJhY2t3YXJkcyhvYmplY3QpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgICAgY29uc3Qgd29ya3NwYWNlID0gZ2V0V29ya3NwYWNlKCk7XHJcbiAgICAgIHdvcmtzcGFjZT8uc2VuZFRvQmFjaygpO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZvbnRGYW1pbHk6ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIHNldEZvbnRGYW1pbHkodmFsdWUpO1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIGZvbnRGYW1pbHkgZXhpc3RzLlxyXG4gICAgICAgICAgb2JqZWN0LnNldCh7IGZvbnRGYW1pbHk6IHZhbHVlIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VGaWxsQ29sb3I6ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIHNldEZpbGxDb2xvcih2YWx1ZSk7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgb2JqZWN0LnNldCh7IGZpbGw6IHZhbHVlIH0pO1xyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZVN0cm9rZUNvbG9yOiAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBzZXRTdHJva2VDb2xvcih2YWx1ZSk7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgLy8gVGV4dCB0eXBlcyBkb24ndCBoYXZlIHN0cm9rZVxyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgb2JqZWN0LnNldCh7IGZpbGw6IHZhbHVlIH0pO1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgb2JqZWN0LnNldCh7IHN0cm9rZTogdmFsdWUgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMuZnJlZURyYXdpbmdCcnVzaC5jb2xvciA9IHZhbHVlO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlU3Ryb2tlV2lkdGg6ICh2YWx1ZTogbnVtYmVyKSA9PiB7XHJcbiAgICAgIHNldFN0cm9rZVdpZHRoKHZhbHVlKTtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBvYmplY3Quc2V0KHsgc3Ryb2tlV2lkdGg6IHZhbHVlIH0pO1xyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLmZyZWVEcmF3aW5nQnJ1c2gud2lkdGggPSB2YWx1ZTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZVN0cm9rZURhc2hBcnJheTogKHZhbHVlOiBudW1iZXJbXSkgPT4ge1xyXG4gICAgICBzZXRTdHJva2VEYXNoQXJyYXkodmFsdWUpO1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIG9iamVjdC5zZXQoeyBzdHJva2VEYXNoQXJyYXk6IHZhbHVlIH0pO1xyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGFkZENpcmNsZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBvYmplY3QgPSBuZXcgZmFicmljLkNpcmNsZSh7XHJcbiAgICAgICAgLi4uQ0lSQ0xFX09QVElPTlMsXHJcbiAgICAgICAgZmlsbDogZmlsbENvbG9yLFxyXG4gICAgICAgIHN0cm9rZTogc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlV2lkdGg6IHN0cm9rZVdpZHRoLFxyXG4gICAgICAgIHN0cm9rZURhc2hBcnJheTogc3Ryb2tlRGFzaEFycmF5LFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGFkZFRvQ2FudmFzKG9iamVjdCk7XHJcbiAgICB9LFxyXG4gICAgYWRkU29mdFJlY3RhbmdsZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBvYmplY3QgPSBuZXcgZmFicmljLlJlY3Qoe1xyXG4gICAgICAgIC4uLlJFQ1RBTkdMRV9PUFRJT05TLFxyXG4gICAgICAgIHJ4OiA1MCxcclxuICAgICAgICByeTogNTAsXHJcbiAgICAgICAgZmlsbDogZmlsbENvbG9yLFxyXG4gICAgICAgIHN0cm9rZTogc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlV2lkdGg6IHN0cm9rZVdpZHRoLFxyXG4gICAgICAgIHN0cm9rZURhc2hBcnJheTogc3Ryb2tlRGFzaEFycmF5LFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGFkZFRvQ2FudmFzKG9iamVjdCk7XHJcbiAgICB9LFxyXG4gICAgYWRkUmVjdGFuZ2xlOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9iamVjdCA9IG5ldyBmYWJyaWMuUmVjdCh7XHJcbiAgICAgICAgLi4uUkVDVEFOR0xFX09QVElPTlMsXHJcbiAgICAgICAgZmlsbDogZmlsbENvbG9yLFxyXG4gICAgICAgIHN0cm9rZTogc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlV2lkdGg6IHN0cm9rZVdpZHRoLFxyXG4gICAgICAgIHN0cm9rZURhc2hBcnJheTogc3Ryb2tlRGFzaEFycmF5LFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGFkZFRvQ2FudmFzKG9iamVjdCk7XHJcbiAgICB9LFxyXG4gICAgYWRkVHJpYW5nbGU6ICgpID0+IHtcclxuICAgICAgY29uc3Qgb2JqZWN0ID0gbmV3IGZhYnJpYy5UcmlhbmdsZSh7XHJcbiAgICAgICAgLi4uVFJJQU5HTEVfT1BUSU9OUyxcclxuICAgICAgICBmaWxsOiBmaWxsQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlOiBzdHJva2VDb2xvcixcclxuICAgICAgICBzdHJva2VXaWR0aDogc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgc3Ryb2tlRGFzaEFycmF5OiBzdHJva2VEYXNoQXJyYXksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBhZGRJbnZlcnNlVHJpYW5nbGU6ICgpID0+IHtcclxuICAgICAgY29uc3QgSEVJR0hUID0gVFJJQU5HTEVfT1BUSU9OUy5oZWlnaHQ7XHJcbiAgICAgIGNvbnN0IFdJRFRIID0gVFJJQU5HTEVfT1BUSU9OUy53aWR0aDtcclxuXHJcbiAgICAgIGNvbnN0IG9iamVjdCA9IG5ldyBmYWJyaWMuUG9seWdvbihcclxuICAgICAgICBbXHJcbiAgICAgICAgICB7IHg6IDAsIHk6IDAgfSxcclxuICAgICAgICAgIHsgeDogV0lEVEgsIHk6IDAgfSxcclxuICAgICAgICAgIHsgeDogV0lEVEggLyAyLCB5OiBIRUlHSFQgfSxcclxuICAgICAgICBdLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIC4uLlRSSUFOR0xFX09QVElPTlMsXHJcbiAgICAgICAgICBmaWxsOiBmaWxsQ29sb3IsXHJcbiAgICAgICAgICBzdHJva2U6IHN0cm9rZUNvbG9yLFxyXG4gICAgICAgICAgc3Ryb2tlV2lkdGg6IHN0cm9rZVdpZHRoLFxyXG4gICAgICAgICAgc3Ryb2tlRGFzaEFycmF5OiBzdHJva2VEYXNoQXJyYXksXHJcbiAgICAgICAgfVxyXG4gICAgICApO1xyXG5cclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBhZGREaWFtb25kOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IEhFSUdIVCA9IERJQU1PTkRfT1BUSU9OUy5oZWlnaHQ7XHJcbiAgICAgIGNvbnN0IFdJRFRIID0gRElBTU9ORF9PUFRJT05TLndpZHRoO1xyXG5cclxuICAgICAgY29uc3Qgb2JqZWN0ID0gbmV3IGZhYnJpYy5Qb2x5Z29uKFxyXG4gICAgICAgIFtcclxuICAgICAgICAgIHsgeDogV0lEVEggLyAyLCB5OiAwIH0sXHJcbiAgICAgICAgICB7IHg6IFdJRFRILCB5OiBIRUlHSFQgLyAyIH0sXHJcbiAgICAgICAgICB7IHg6IFdJRFRIIC8gMiwgeTogSEVJR0hUIH0sXHJcbiAgICAgICAgICB7IHg6IDAsIHk6IEhFSUdIVCAvIDIgfSxcclxuICAgICAgICBdLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIC4uLkRJQU1PTkRfT1BUSU9OUyxcclxuICAgICAgICAgIGZpbGw6IGZpbGxDb2xvcixcclxuICAgICAgICAgIHN0cm9rZTogc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgICBzdHJva2VXaWR0aDogc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgICBzdHJva2VEYXNoQXJyYXk6IHN0cm9rZURhc2hBcnJheSxcclxuICAgICAgICB9XHJcbiAgICAgICk7XHJcbiAgICAgIGFkZFRvQ2FudmFzKG9iamVjdCk7XHJcbiAgICB9LFxyXG4gICAgY2FudmFzLFxyXG4gICAgZ2V0QWN0aXZlRm9udFdlaWdodDogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gRk9OVF9XRUlHSFQ7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIGZvbnRXZWlnaHQgZXhpc3RzLlxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcImZvbnRXZWlnaHRcIikgfHwgRk9OVF9XRUlHSFQ7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlRm9udEZhbWlseTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gZm9udEZhbWlseTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgZm9udEZhbWlseSBleGlzdHMuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwiZm9udEZhbWlseVwiKSB8fCBmb250RmFtaWx5O1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZUZpbGxDb2xvcjogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gZmlsbENvbG9yO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcImZpbGxcIikgfHwgZmlsbENvbG9yO1xyXG5cclxuICAgICAgLy8gQ3VycmVudGx5LCBncmFkaWVudHMgJiBwYXR0ZXJucyBhcmUgbm90IHN1cHBvcnRlZFxyXG4gICAgICByZXR1cm4gdmFsdWUgYXMgc3RyaW5nO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZVN0cm9rZUNvbG9yOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBzdHJva2VDb2xvcjtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgdmFsdWUgPSBzZWxlY3RlZE9iamVjdC5nZXQoXCJzdHJva2VcIikgfHwgc3Ryb2tlQ29sb3I7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlU3Ryb2tlV2lkdGg6ICgpID0+IHtcclxuICAgICAgY29uc3Qgc2VsZWN0ZWRPYmplY3QgPSBzZWxlY3RlZE9iamVjdHNbMF07XHJcblxyXG4gICAgICBpZiAoIXNlbGVjdGVkT2JqZWN0KSB7XHJcbiAgICAgICAgcmV0dXJuIHN0cm9rZVdpZHRoO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcInN0cm9rZVdpZHRoXCIpIHx8IHN0cm9rZVdpZHRoO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZVN0cm9rZURhc2hBcnJheTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gc3Ryb2tlRGFzaEFycmF5O1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcInN0cm9rZURhc2hBcnJheVwiKSB8fCBzdHJva2VEYXNoQXJyYXk7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgYWRkSWNvbjogKGljb25OYW1lOiBzdHJpbmcpID0+IHtcclxuICAgICAgaWYgKCFjYW52YXMpIHJldHVybjtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIHRoZSBpY29uIGV4aXN0cyBpbiBvdXIgY29tcHJlaGVuc2l2ZSBsaWJyYXJ5XHJcbiAgICAgIGNvbnN0IEljb25Db21wb25lbnQgPSBJQ09OX0NPTVBPTkVOVFNbaWNvbk5hbWVdO1xyXG4gICAgICBpZiAoIUljb25Db21wb25lbnQpIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oYEljb24gJHtpY29uTmFtZX0gbm90IGZvdW5kIGluIGxpYnJhcnlgKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENyZWF0ZSBTVkcgc3RyaW5nIHVzaW5nIHRoZSBwcm9wZXIgTHVjaWRlIGljb25cclxuICAgICAgY29uc3QgaWNvblNpemUgPSA4MDtcclxuICAgICAgY29uc3QgaWNvbkNvbG9yID0gc3Ryb2tlQ29sb3IgfHwgJyMwMDAwMDAnO1xyXG5cclxuICAgICAgLy8gQ3JlYXRlIGEgbW9yZSByZWxpYWJsZSBTVkcgZ2VuZXJhdGlvbiBhcHByb2FjaFxyXG4gICAgICBjb25zdCBzdmdTdHJpbmcgPSBgZGF0YTppbWFnZS9zdmcreG1sO2NoYXJzZXQ9dXRmLTgsJHtlbmNvZGVVUklDb21wb25lbnQoYFxyXG4gICAgICAgIDxzdmcgd2lkdGg9XCIke2ljb25TaXplfVwiIGhlaWdodD1cIiR7aWNvblNpemV9XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiJHtpY29uQ29sb3J9XCIgc3Ryb2tlLXdpZHRoPVwiMlwiIHN0cm9rZS1saW5lY2FwPVwicm91bmRcIiBzdHJva2UtbGluZWpvaW49XCJyb3VuZFwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cclxuICAgICAgICAgICR7Z2V0SWNvblNWR1BhdGhzKGljb25OYW1lKX1cclxuICAgICAgICA8L3N2Zz5cclxuICAgICAgYCl9YDtcclxuXHJcbiAgICAgIC8vIENyZWF0ZSBmYWJyaWMgaW1hZ2UgZnJvbSB0aGUgU1ZHIGRhdGEgVVJMXHJcbiAgICAgIGZhYnJpYy5JbWFnZS5mcm9tVVJMKHN2Z1N0cmluZywgKGltZykgPT4ge1xyXG4gICAgICAgIGlmIChpbWcgJiYgY2FudmFzKSB7XHJcbiAgICAgICAgICBpbWcuc2V0KHtcclxuICAgICAgICAgICAgbGVmdDogMTAwLFxyXG4gICAgICAgICAgICB0b3A6IDEwMCxcclxuICAgICAgICAgICAgc2NhbGVYOiAxLFxyXG4gICAgICAgICAgICBzY2FsZVk6IDEsXHJcbiAgICAgICAgICAgIC8vIEFsbG93IGZ1bGwgY29udHJvbCBvdmVyIHRoZSBpY29uXHJcbiAgICAgICAgICAgIGxvY2tTY2FsaW5nWDogZmFsc2UsXHJcbiAgICAgICAgICAgIGxvY2tTY2FsaW5nWTogZmFsc2UsXHJcbiAgICAgICAgICAgIGxvY2tVbmlTY2FsaW5nOiBmYWxzZSxcclxuICAgICAgICAgICAgbG9ja01vdmVtZW50WDogZmFsc2UsXHJcbiAgICAgICAgICAgIGxvY2tNb3ZlbWVudFk6IGZhbHNlLFxyXG4gICAgICAgICAgICBsb2NrUm90YXRpb246IGZhbHNlLFxyXG4gICAgICAgICAgICAvLyBFbnN1cmUgaXQncyBzZWxlY3RhYmxlIGFuZCBtb3ZhYmxlXHJcbiAgICAgICAgICAgIHNlbGVjdGFibGU6IHRydWUsXHJcbiAgICAgICAgICAgIGV2ZW50ZWQ6IHRydWUsXHJcbiAgICAgICAgICAgIC8vIEFkZCBtZXRhZGF0YSBmb3IgaWRlbnRpZmljYXRpb25cclxuICAgICAgICAgICAgdHlwZTogJ2ljb24nLFxyXG4gICAgICAgICAgICBpY29uTmFtZTogaWNvbk5hbWUsXHJcbiAgICAgICAgICAgIC8vIFN0b3JlIG9yaWdpbmFsIGNvbG9yIGZvciBjb2xvciBjaGFuZ2VzXHJcbiAgICAgICAgICAgIG9yaWdpbmFsQ29sb3I6IGljb25Db2xvcixcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgYWRkVG9DYW52YXMoaW1nKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgfSxcclxuICAgIC8vIEljb24tc3BlY2lmaWMgbWV0aG9kc1xyXG4gICAgY2hhbmdlSWNvbkNvbG9yOiAoY29sb3I6IHN0cmluZykgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChvYmplY3QudHlwZSA9PT0gJ2ljb24nICYmIG9iamVjdC5pY29uTmFtZSkge1xyXG4gICAgICAgICAgLy8gUmVnZW5lcmF0ZSB0aGUgU1ZHIHdpdGggbmV3IGNvbG9yXHJcbiAgICAgICAgICBjb25zdCBpY29uU2l6ZSA9IE1hdGgubWF4KG9iamVjdC53aWR0aCB8fCA4MCwgb2JqZWN0LmhlaWdodCB8fCA4MCk7XHJcbiAgICAgICAgICBjb25zdCBzdmdTdHJpbmcgPSBgZGF0YTppbWFnZS9zdmcreG1sO2NoYXJzZXQ9dXRmLTgsJHtlbmNvZGVVUklDb21wb25lbnQoYFxyXG4gICAgICAgICAgICA8c3ZnIHdpZHRoPVwiJHtpY29uU2l6ZX1cIiBoZWlnaHQ9XCIke2ljb25TaXplfVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cIiR7Y29sb3J9XCIgc3Ryb2tlLXdpZHRoPVwiMlwiIHN0cm9rZS1saW5lY2FwPVwicm91bmRcIiBzdHJva2UtbGluZWpvaW49XCJyb3VuZFwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cclxuICAgICAgICAgICAgICAke2dldEljb25TVkdQYXRocyhvYmplY3QuaWNvbk5hbWUpfVxyXG4gICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgIGApfWA7XHJcblxyXG4gICAgICAgICAgLy8gVXBkYXRlIHRoZSBpbWFnZSBzb3VyY2VcclxuICAgICAgICAgIGZhYnJpYy5JbWFnZS5mcm9tVVJMKHN2Z1N0cmluZywgKG5ld0ltZykgPT4ge1xyXG4gICAgICAgICAgICBpZiAobmV3SW1nICYmIGNhbnZhcykge1xyXG4gICAgICAgICAgICAgIC8vIFByZXNlcnZlIHRoZSBjdXJyZW50IHRyYW5zZm9ybWF0aW9uXHJcbiAgICAgICAgICAgICAgY29uc3QgY3VycmVudFRyYW5zZm9ybSA9IHtcclxuICAgICAgICAgICAgICAgIGxlZnQ6IG9iamVjdC5sZWZ0LFxyXG4gICAgICAgICAgICAgICAgdG9wOiBvYmplY3QudG9wLFxyXG4gICAgICAgICAgICAgICAgc2NhbGVYOiBvYmplY3Quc2NhbGVYLFxyXG4gICAgICAgICAgICAgICAgc2NhbGVZOiBvYmplY3Quc2NhbGVZLFxyXG4gICAgICAgICAgICAgICAgYW5nbGU6IG9iamVjdC5hbmdsZSxcclxuICAgICAgICAgICAgICAgIGZsaXBYOiBvYmplY3QuZmxpcFgsXHJcbiAgICAgICAgICAgICAgICBmbGlwWTogb2JqZWN0LmZsaXBZLFxyXG4gICAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgICAgIC8vIFJlbW92ZSBvbGQgb2JqZWN0IGFuZCBhZGQgbmV3IG9uZVxyXG4gICAgICAgICAgICAgIGNhbnZhcy5yZW1vdmUob2JqZWN0KTtcclxuICAgICAgICAgICAgICBuZXdJbWcuc2V0KHtcclxuICAgICAgICAgICAgICAgIC4uLmN1cnJlbnRUcmFuc2Zvcm0sXHJcbiAgICAgICAgICAgICAgICB0eXBlOiAnaWNvbicsXHJcbiAgICAgICAgICAgICAgICBpY29uTmFtZTogb2JqZWN0Lmljb25OYW1lLFxyXG4gICAgICAgICAgICAgICAgb3JpZ2luYWxDb2xvcjogY29sb3IsXHJcbiAgICAgICAgICAgICAgICBzZWxlY3RhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgZXZlbnRlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICBjYW52YXMuYWRkKG5ld0ltZyk7XHJcbiAgICAgICAgICAgICAgY2FudmFzLnNldEFjdGl2ZU9iamVjdChuZXdJbWcpO1xyXG4gICAgICAgICAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgICAgICAgICAgICBzYXZlKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlSWNvblNpemU6ICh3aWR0aDogbnVtYmVyLCBoZWlnaHQ6IG51bWJlcikgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChvYmplY3QudHlwZSA9PT0gJ2ljb24nKSB7XHJcbiAgICAgICAgICBvYmplY3Quc2V0KHtcclxuICAgICAgICAgICAgc2NhbGVYOiB3aWR0aCAvIChvYmplY3Qud2lkdGggfHwgODApLFxyXG4gICAgICAgICAgICBzY2FsZVk6IGhlaWdodCAvIChvYmplY3QuaGVpZ2h0IHx8IDgwKSxcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgICAgc2F2ZSgpO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZUljb25Db2xvcjogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuICAgICAgaWYgKHNlbGVjdGVkT2JqZWN0ICYmIHNlbGVjdGVkT2JqZWN0LnR5cGUgPT09ICdpY29uJykge1xyXG4gICAgICAgIHJldHVybiBzZWxlY3RlZE9iamVjdC5vcmlnaW5hbENvbG9yIHx8IHN0cm9rZUNvbG9yO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiBzdHJva2VDb2xvcjtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVJY29uU2l6ZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuICAgICAgaWYgKHNlbGVjdGVkT2JqZWN0ICYmIHNlbGVjdGVkT2JqZWN0LnR5cGUgPT09ICdpY29uJykge1xyXG4gICAgICAgIGNvbnN0IHdpZHRoID0gKHNlbGVjdGVkT2JqZWN0LndpZHRoIHx8IDgwKSAqIChzZWxlY3RlZE9iamVjdC5zY2FsZVggfHwgMSk7XHJcbiAgICAgICAgY29uc3QgaGVpZ2h0ID0gKHNlbGVjdGVkT2JqZWN0LmhlaWdodCB8fCA4MCkgKiAoc2VsZWN0ZWRPYmplY3Quc2NhbGVZIHx8IDEpO1xyXG4gICAgICAgIHJldHVybiB7IHdpZHRoOiBNYXRoLnJvdW5kKHdpZHRoKSwgaGVpZ2h0OiBNYXRoLnJvdW5kKGhlaWdodCkgfTtcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4geyB3aWR0aDogODAsIGhlaWdodDogODAgfTtcclxuICAgIH0sXHJcbiAgICBzZWxlY3RlZE9iamVjdHMsXHJcbiAgfTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VFZGl0b3IgPSAoe1xyXG4gIGRlZmF1bHRTdGF0ZSxcclxuICBkZWZhdWx0SGVpZ2h0LFxyXG4gIGRlZmF1bHRXaWR0aCxcclxuICBjbGVhclNlbGVjdGlvbkNhbGxiYWNrLFxyXG4gIHNhdmVDYWxsYmFjayxcclxuICBzZXRDYW52YXNJc1NlbGVjdGVkLFxyXG59OiBFZGl0b3JIb29rUHJvcHMpID0+IHtcclxuICBjb25zdCBpbml0aWFsU3RhdGUgPSB1c2VSZWYoZGVmYXVsdFN0YXRlKTtcclxuICBjb25zdCBpbml0aWFsV2lkdGggPSB1c2VSZWYoZGVmYXVsdFdpZHRoKTtcclxuICBjb25zdCBpbml0aWFsSGVpZ2h0ID0gdXNlUmVmKGRlZmF1bHRIZWlnaHQpO1xyXG5cclxuICBjb25zdCBbY2FudmFzLCBzZXRDYW52YXNdID0gdXNlU3RhdGU8ZmFicmljLkNhbnZhcyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtjb250YWluZXIsIHNldENvbnRhaW5lcl0gPSB1c2VTdGF0ZTxIVE1MRGl2RWxlbWVudCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZE9iamVjdHMsIHNldFNlbGVjdGVkT2JqZWN0c10gPSB1c2VTdGF0ZTxmYWJyaWMuT2JqZWN0W10+KFtdKTtcclxuXHJcbiAgY29uc3QgW2ZvbnRGYW1pbHksIHNldEZvbnRGYW1pbHldID0gdXNlU3RhdGUoRk9OVF9GQU1JTFkpO1xyXG4gIGNvbnN0IFtmaWxsQ29sb3IsIHNldEZpbGxDb2xvcl0gPSB1c2VTdGF0ZShGSUxMX0NPTE9SKTtcclxuICBjb25zdCBbc3Ryb2tlQ29sb3IsIHNldFN0cm9rZUNvbG9yXSA9IHVzZVN0YXRlKFNUUk9LRV9DT0xPUik7XHJcbiAgY29uc3QgW3N0cm9rZVdpZHRoLCBzZXRTdHJva2VXaWR0aF0gPSB1c2VTdGF0ZShTVFJPS0VfV0lEVEgpO1xyXG4gIGNvbnN0IFtzdHJva2VEYXNoQXJyYXksIHNldFN0cm9rZURhc2hBcnJheV0gPSB1c2VTdGF0ZTxudW1iZXJbXT4oU1RST0tFX0RBU0hfQVJSQVkpO1xyXG5cclxuICB1c2VXaW5kb3dFdmVudHMoKTtcclxuXHJcbiAgY29uc3QgeyBcclxuICAgIHNhdmUsIFxyXG4gICAgY2FuUmVkbywgXHJcbiAgICBjYW5VbmRvLCBcclxuICAgIHVuZG8sIFxyXG4gICAgcmVkbyxcclxuICAgIGNhbnZhc0hpc3RvcnksXHJcbiAgICBzZXRIaXN0b3J5SW5kZXgsXHJcbiAgfSA9IHVzZUhpc3RvcnkoeyBcclxuICAgIGNhbnZhcyxcclxuICAgIHNhdmVDYWxsYmFja1xyXG4gIH0pO1xyXG5cclxuICBjb25zdCB7IGNvcHksIHBhc3RlIH0gPSB1c2VDbGlwYm9hcmQoeyBjYW52YXMgfSk7XHJcblxyXG4gIGNvbnN0IHsgYXV0b1pvb20gfSA9IHVzZUF1dG9SZXNpemUoe1xyXG4gICAgY2FudmFzLFxyXG4gICAgY29udGFpbmVyLFxyXG4gIH0pO1xyXG5cclxuICB1c2VDYW52YXNFdmVudHMoe1xyXG4gICAgc2F2ZSxcclxuICAgIGNhbnZhcyxcclxuICAgIHNldFNlbGVjdGVkT2JqZWN0cyxcclxuICAgIGNsZWFyU2VsZWN0aW9uQ2FsbGJhY2ssXHJcbiAgICBzZXRDYW52YXNJc1NlbGVjdGVkLFxyXG4gIH0pO1xyXG5cclxuICB1c2Vab29tRXZlbnRzKHtcclxuICAgIGNhbnZhcyxcclxuICB9KTtcclxuXHJcbiAgdXNlSG90a2V5cyh7XHJcbiAgICB1bmRvLFxyXG4gICAgcmVkbyxcclxuICAgIGNvcHksXHJcbiAgICBwYXN0ZSxcclxuICAgIHNhdmUsXHJcbiAgICBjYW52YXMsXHJcbiAgfSk7XHJcblxyXG4gIHVzZUxvYWRTdGF0ZSh7XHJcbiAgICBjYW52YXMsXHJcbiAgICBhdXRvWm9vbSxcclxuICAgIGluaXRpYWxTdGF0ZSxcclxuICAgIGNhbnZhc0hpc3RvcnksXHJcbiAgICBzZXRIaXN0b3J5SW5kZXgsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IGVkaXRvciA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgaWYgKGNhbnZhcykge1xyXG4gICAgICByZXR1cm4gYnVpbGRFZGl0b3Ioe1xyXG4gICAgICAgIHNhdmUsXHJcbiAgICAgICAgdW5kbyxcclxuICAgICAgICByZWRvLFxyXG4gICAgICAgIGNhblVuZG8sXHJcbiAgICAgICAgY2FuUmVkbyxcclxuICAgICAgICBhdXRvWm9vbSxcclxuICAgICAgICBjb3B5LFxyXG4gICAgICAgIHBhc3RlLFxyXG4gICAgICAgIGNhbnZhcyxcclxuICAgICAgICBmaWxsQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgc2V0RmlsbENvbG9yLFxyXG4gICAgICAgIHNldFN0cm9rZUNvbG9yLFxyXG4gICAgICAgIHNldFN0cm9rZVdpZHRoLFxyXG4gICAgICAgIHN0cm9rZURhc2hBcnJheSxcclxuICAgICAgICBzZWxlY3RlZE9iamVjdHMsXHJcbiAgICAgICAgc2V0U3Ryb2tlRGFzaEFycmF5LFxyXG4gICAgICAgIGZvbnRGYW1pbHksXHJcbiAgICAgICAgc2V0Rm9udEZhbWlseSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcclxuICB9LCBcclxuICBbXHJcbiAgICBjYW5SZWRvLFxyXG4gICAgY2FuVW5kbyxcclxuICAgIHVuZG8sXHJcbiAgICByZWRvLFxyXG4gICAgc2F2ZSxcclxuICAgIGF1dG9ab29tLFxyXG4gICAgY29weSxcclxuICAgIHBhc3RlLFxyXG4gICAgY2FudmFzLFxyXG4gICAgZmlsbENvbG9yLFxyXG4gICAgc3Ryb2tlV2lkdGgsXHJcbiAgICBzdHJva2VDb2xvcixcclxuICAgIHNlbGVjdGVkT2JqZWN0cyxcclxuICAgIHN0cm9rZURhc2hBcnJheSxcclxuICAgIGZvbnRGYW1pbHksXHJcbiAgXSk7XHJcblxyXG4gIGNvbnN0IGluaXQgPSB1c2VDYWxsYmFjayhcclxuICAgICh7XHJcbiAgICAgIGluaXRpYWxDYW52YXMsXHJcbiAgICAgIGluaXRpYWxDb250YWluZXIsXHJcbiAgICB9OiB7XHJcbiAgICAgIGluaXRpYWxDYW52YXM6IGZhYnJpYy5DYW52YXM7XHJcbiAgICAgIGluaXRpYWxDb250YWluZXI6IEhUTUxEaXZFbGVtZW50O1xyXG4gICAgfSkgPT4ge1xyXG4gICAgICBmYWJyaWMuT2JqZWN0LnByb3RvdHlwZS5zZXQoe1xyXG4gICAgICAgIGNvcm5lckNvbG9yOiBcIiNGRkZcIixcclxuICAgICAgICBjb3JuZXJTdHlsZTogXCJjaXJjbGVcIixcclxuICAgICAgICBib3JkZXJDb2xvcjogXCIjM2I4MmY2XCIsXHJcbiAgICAgICAgYm9yZGVyU2NhbGVGYWN0b3I6IDEuNSxcclxuICAgICAgICB0cmFuc3BhcmVudENvcm5lcnM6IGZhbHNlLFxyXG4gICAgICAgIGJvcmRlck9wYWNpdHlXaGVuTW92aW5nOiAxLFxyXG4gICAgICAgIGNvcm5lclN0cm9rZUNvbG9yOiBcIiMzYjgyZjZcIixcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCBpbml0aWFsV29ya3NwYWNlID0gbmV3IGZhYnJpYy5SZWN0KHtcclxuICAgICAgICB3aWR0aDogaW5pdGlhbFdpZHRoLmN1cnJlbnQsXHJcbiAgICAgICAgaGVpZ2h0OiBpbml0aWFsSGVpZ2h0LmN1cnJlbnQsXHJcbiAgICAgICAgbmFtZTogXCJjbGlwXCIsXHJcbiAgICAgICAgZmlsbDogXCJ3aGl0ZVwiLFxyXG4gICAgICAgIHNlbGVjdGFibGU6IGZhbHNlLFxyXG4gICAgICAgIGhhc0NvbnRyb2xzOiBmYWxzZSxcclxuICAgICAgICBzaGFkb3c6IG5ldyBmYWJyaWMuU2hhZG93KHtcclxuICAgICAgICAgIGNvbG9yOiBcInJnYmEoMCwwLDAsMC44KVwiLFxyXG4gICAgICAgICAgYmx1cjogNSxcclxuICAgICAgICB9KSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpbml0aWFsQ2FudmFzLnNldFdpZHRoKGluaXRpYWxDb250YWluZXIub2Zmc2V0V2lkdGgpO1xyXG4gICAgICBpbml0aWFsQ2FudmFzLnNldEhlaWdodChpbml0aWFsQ29udGFpbmVyLm9mZnNldEhlaWdodCk7XHJcblxyXG4gICAgICBpbml0aWFsQ2FudmFzLmFkZChpbml0aWFsV29ya3NwYWNlKTtcclxuICAgICAgaW5pdGlhbENhbnZhcy5jZW50ZXJPYmplY3QoaW5pdGlhbFdvcmtzcGFjZSk7XHJcbiAgICAgIGluaXRpYWxDYW52YXMuY2xpcFBhdGggPSBpbml0aWFsV29ya3NwYWNlO1xyXG5cclxuICAgICAgc2V0Q2FudmFzKGluaXRpYWxDYW52YXMpO1xyXG4gICAgICBzZXRDb250YWluZXIoaW5pdGlhbENvbnRhaW5lcik7XHJcblxyXG4gICAgICBjb25zdCBjdXJyZW50U3RhdGUgPSBKU09OLnN0cmluZ2lmeShcclxuICAgICAgICBpbml0aWFsQ2FudmFzLnRvSlNPTihKU09OX0tFWVMpXHJcbiAgICAgICk7XHJcbiAgICAgIGNhbnZhc0hpc3RvcnkuY3VycmVudCA9IFtjdXJyZW50U3RhdGVdO1xyXG4gICAgICBzZXRIaXN0b3J5SW5kZXgoMCk7XHJcbiAgICB9LFxyXG4gICAgW1xyXG4gICAgICBjYW52YXNIaXN0b3J5LCAvLyBObyBuZWVkLCB0aGlzIGlzIGZyb20gdXNlUmVmXHJcbiAgICAgIHNldEhpc3RvcnlJbmRleCwgLy8gTm8gbmVlZCwgdGhpcyBpcyBmcm9tIHVzZVN0YXRlXHJcbiAgICBdXHJcbiAgKTtcclxuXHJcbiAgcmV0dXJuIHsgaW5pdCwgZWRpdG9yIH07XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJmYWJyaWMiLCJ1c2VDYWxsYmFjayIsInVzZVN0YXRlIiwidXNlTWVtbyIsInVzZVJlZiIsIkZJTExfQ09MT1IiLCJTVFJPS0VfV0lEVEgiLCJTVFJPS0VfQ09MT1IiLCJDSVJDTEVfT1BUSU9OUyIsIkRJQU1PTkRfT1BUSU9OUyIsIlRSSUFOR0xFX09QVElPTlMiLCJSRUNUQU5HTEVfT1BUSU9OUyIsIlNUUk9LRV9EQVNIX0FSUkFZIiwiVEVYVF9PUFRJT05TIiwiRk9OVF9GQU1JTFkiLCJGT05UX1dFSUdIVCIsIkZPTlRfU0laRSIsIkpTT05fS0VZUyIsInVzZUhpc3RvcnkiLCJjcmVhdGVGaWx0ZXIiLCJkb3dubG9hZEZpbGUiLCJpc1RleHRUeXBlIiwidHJhbnNmb3JtVGV4dCIsInVzZUhvdGtleXMiLCJ1c2VDbGlwYm9hcmQiLCJ1c2VBdXRvUmVzaXplIiwidXNlQ2FudmFzRXZlbnRzIiwidXNlWm9vbUV2ZW50cyIsInVzZVdpbmRvd0V2ZW50cyIsInVzZUxvYWRTdGF0ZSIsIkx1Y2lkZUljb25zIiwiSUNPTl9DT01QT05FTlRTIiwiSGVhcnQiLCJTdGFyIiwiQXJyb3dSaWdodCIsIkFycm93TGVmdCIsIkFycm93VXAiLCJBcnJvd0Rvd24iLCJIb21lIiwiVXNlciIsIlNldHRpbmdzIiwiTWFpbCIsIlBob25lIiwiQ2FyIiwiQ2FtZXJhIiwiTXVzaWMiLCJWaWRlbyIsIkltYWdlIiwiRmlsZSIsIkZvbGRlciIsIlNlYXJjaCIsIlBsdXMiLCJNaW51cyIsIlgiLCJDaGVjayIsIkVkaXQiLCJUcmFzaDIiLCJEb3dubG9hZCIsIlVwbG9hZCIsIlNoYXJlIiwiQ29weSIsIlNhdmUiLCJQcmludGVyIiwiQ2FsZW5kYXIiLCJDbG9jayIsIk1hcCIsIkdsb2JlIiwiV2lmaSIsIkJhdHRlcnkiLCJWb2x1bWUyIiwiUGxheSIsIlBhdXNlIiwiU3F1YXJlIiwiU2tpcEZvcndhcmQiLCJTa2lwQmFjayIsIlJlZnJlc2hDdyIsIk1heGltaXplIiwiTWluaW1pemUiLCJFeWUiLCJFeWVPZmYiLCJMb2NrIiwiVW5sb2NrIiwiU2hpZWxkIiwiQWxlcnRUcmlhbmdsZSIsIkluZm8iLCJIZWxwQ2lyY2xlIiwiQm9va21hcmsiLCJUYWciLCJGbGFnIiwiVGh1bWJzVXAiLCJUaHVtYnNEb3duIiwiTWVzc2FnZUNpcmNsZSIsIlNlbmQiLCJCZWxsIiwiR2lmdCIsIlNob3BwaW5nQ2FydCIsIkNyZWRpdENhcmQiLCJEb2xsYXJTaWduIiwiVHJlbmRpbmdVcCIsIlRyZW5kaW5nRG93biIsIkJhckNoYXJ0MyIsIlBpZUNoYXJ0IiwiQWN0aXZpdHkiLCJaYXAiLCJTdW4iLCJNb29uIiwiQ2xvdWQiLCJVbWJyZWxsYSIsIlNub3dmbGFrZSIsIkRyb3BsZXRzIiwiRmxhbWUiLCJMZWFmIiwiVHJlZXMiLCJGbG93ZXIiLCJDb2ZmZWUiLCJQaXp6YSIsIlV0ZW5zaWxzIiwiV2luZSIsIkdhbWVwYWQyIiwiSGVhZHBob25lcyIsIlNtYXJ0cGhvbmUiLCJMYXB0b3AiLCJNb25pdG9yIiwiS2V5Ym9hcmQiLCJNb3VzZSIsIkhhcmREcml2ZSIsIlVzYiIsIkJsdWV0b290aCIsIlJhZGlvIiwiVHYiLCJQbGFuZSIsIlRyYWluIiwiQnVzIiwiQmlrZSIsIlNoaXAiLCJBbmNob3IiLCJDb21wYXNzIiwiTWFwUGluIiwiTmF2aWdhdGlvbiIsIkJ1aWxkaW5nIiwiU2Nob29sIiwiSG9zcGl0YWwiLCJTdG9yZSIsIkZhY3RvcnkiLCJUZW50IiwiTW91bnRhaW4iLCJXYXZlcyIsIlN1bnJpc2UiLCJTdW5zZXQiLCJnZXRJY29uU1ZHUGF0aHMiLCJpY29uTmFtZSIsImljb25QYXRocyIsImJ1aWxkRWRpdG9yIiwic2F2ZSIsInVuZG8iLCJyZWRvIiwiY2FuUmVkbyIsImNhblVuZG8iLCJhdXRvWm9vbSIsImNvcHkiLCJwYXN0ZSIsImNhbnZhcyIsImZpbGxDb2xvciIsImZvbnRGYW1pbHkiLCJzZXRGb250RmFtaWx5Iiwic2V0RmlsbENvbG9yIiwic3Ryb2tlQ29sb3IiLCJzZXRTdHJva2VDb2xvciIsInN0cm9rZVdpZHRoIiwic2V0U3Ryb2tlV2lkdGgiLCJzZWxlY3RlZE9iamVjdHMiLCJzdHJva2VEYXNoQXJyYXkiLCJzZXRTdHJva2VEYXNoQXJyYXkiLCJnZW5lcmF0ZVNhdmVPcHRpb25zIiwid2lkdGgiLCJoZWlnaHQiLCJsZWZ0IiwidG9wIiwiZ2V0V29ya3NwYWNlIiwibmFtZSIsImZvcm1hdCIsInF1YWxpdHkiLCJzYXZlUG5nIiwib3B0aW9ucyIsInNldFZpZXdwb3J0VHJhbnNmb3JtIiwiZGF0YVVybCIsInRvRGF0YVVSTCIsInNhdmVTdmciLCJzYXZlSnBnIiwic2F2ZUpzb24iLCJ0b0pTT04iLCJvYmplY3RzIiwiZmlsZVN0cmluZyIsImVuY29kZVVSSUNvbXBvbmVudCIsIkpTT04iLCJzdHJpbmdpZnkiLCJsb2FkSnNvbiIsImpzb24iLCJkYXRhIiwicGFyc2UiLCJsb2FkRnJvbUpTT04iLCJnZXRPYmplY3RzIiwiZmluZCIsIm9iamVjdCIsImNlbnRlciIsIndvcmtzcGFjZSIsImdldENlbnRlclBvaW50IiwiX2NlbnRlck9iamVjdCIsImFkZFRvQ2FudmFzIiwiYWRkIiwic2V0QWN0aXZlT2JqZWN0Iiwiem9vbUluIiwiem9vbVJhdGlvIiwiZ2V0Wm9vbSIsImdldENlbnRlciIsInpvb21Ub1BvaW50IiwiUG9pbnQiLCJ6b29tT3V0IiwiY2hhbmdlU2l6ZSIsInZhbHVlIiwic2V0IiwiY2hhbmdlQmFja2dyb3VuZCIsImZpbGwiLCJyZW5kZXJBbGwiLCJlbmFibGVEcmF3aW5nTW9kZSIsImRpc2NhcmRBY3RpdmVPYmplY3QiLCJpc0RyYXdpbmdNb2RlIiwiZnJlZURyYXdpbmdCcnVzaCIsImNvbG9yIiwiZGlzYWJsZURyYXdpbmdNb2RlIiwib25VbmRvIiwib25SZWRvIiwib25Db3B5Iiwib25QYXN0ZSIsImNoYW5nZUltYWdlRmlsdGVyIiwiZ2V0QWN0aXZlT2JqZWN0cyIsImZvckVhY2giLCJ0eXBlIiwiaW1hZ2VPYmplY3QiLCJlZmZlY3QiLCJmaWx0ZXJzIiwiYXBwbHlGaWx0ZXJzIiwiYWRkSW1hZ2UiLCJmcm9tVVJMIiwiaW1hZ2UiLCJzY2FsZVRvV2lkdGgiLCJzY2FsZVRvSGVpZ2h0IiwiY3Jvc3NPcmlnaW4iLCJkZWxldGUiLCJyZW1vdmUiLCJhZGRUZXh0IiwiVGV4dGJveCIsImdldEFjdGl2ZU9wYWNpdHkiLCJzZWxlY3RlZE9iamVjdCIsImdldCIsImNoYW5nZUZvbnRTaXplIiwiZm9udFNpemUiLCJnZXRBY3RpdmVGb250U2l6ZSIsImNoYW5nZVRleHRBbGlnbiIsInRleHRBbGlnbiIsImdldEFjdGl2ZVRleHRBbGlnbiIsImNoYW5nZUZvbnRVbmRlcmxpbmUiLCJ1bmRlcmxpbmUiLCJnZXRBY3RpdmVGb250VW5kZXJsaW5lIiwiY2hhbmdlRm9udExpbmV0aHJvdWdoIiwibGluZXRocm91Z2giLCJnZXRBY3RpdmVGb250TGluZXRocm91Z2giLCJjaGFuZ2VGb250U3R5bGUiLCJmb250U3R5bGUiLCJnZXRBY3RpdmVGb250U3R5bGUiLCJjaGFuZ2VGb250V2VpZ2h0IiwiZm9udFdlaWdodCIsImNoYW5nZU9wYWNpdHkiLCJvcGFjaXR5IiwiYnJpbmdGb3J3YXJkIiwic2VuZFRvQmFjayIsInNlbmRCYWNrd2FyZHMiLCJjaGFuZ2VGb250RmFtaWx5IiwiY2hhbmdlRmlsbENvbG9yIiwiY2hhbmdlU3Ryb2tlQ29sb3IiLCJzdHJva2UiLCJjaGFuZ2VTdHJva2VXaWR0aCIsImNoYW5nZVN0cm9rZURhc2hBcnJheSIsImFkZENpcmNsZSIsIkNpcmNsZSIsImFkZFNvZnRSZWN0YW5nbGUiLCJSZWN0IiwicngiLCJyeSIsImFkZFJlY3RhbmdsZSIsImFkZFRyaWFuZ2xlIiwiVHJpYW5nbGUiLCJhZGRJbnZlcnNlVHJpYW5nbGUiLCJIRUlHSFQiLCJXSURUSCIsIlBvbHlnb24iLCJ4IiwieSIsImFkZERpYW1vbmQiLCJnZXRBY3RpdmVGb250V2VpZ2h0IiwiZ2V0QWN0aXZlRm9udEZhbWlseSIsImdldEFjdGl2ZUZpbGxDb2xvciIsImdldEFjdGl2ZVN0cm9rZUNvbG9yIiwiZ2V0QWN0aXZlU3Ryb2tlV2lkdGgiLCJnZXRBY3RpdmVTdHJva2VEYXNoQXJyYXkiLCJhZGRJY29uIiwiSWNvbkNvbXBvbmVudCIsImNvbnNvbGUiLCJ3YXJuIiwiaWNvblNpemUiLCJpY29uQ29sb3IiLCJzdmdTdHJpbmciLCJpbWciLCJzY2FsZVgiLCJzY2FsZVkiLCJsb2NrU2NhbGluZ1giLCJsb2NrU2NhbGluZ1kiLCJsb2NrVW5pU2NhbGluZyIsImxvY2tNb3ZlbWVudFgiLCJsb2NrTW92ZW1lbnRZIiwibG9ja1JvdGF0aW9uIiwic2VsZWN0YWJsZSIsImV2ZW50ZWQiLCJvcmlnaW5hbENvbG9yIiwiY2hhbmdlSWNvbkNvbG9yIiwiTWF0aCIsIm1heCIsIm5ld0ltZyIsImN1cnJlbnRUcmFuc2Zvcm0iLCJhbmdsZSIsImZsaXBYIiwiZmxpcFkiLCJjaGFuZ2VJY29uU2l6ZSIsImdldEFjdGl2ZUljb25Db2xvciIsImdldEFjdGl2ZUljb25TaXplIiwicm91bmQiLCJ1c2VFZGl0b3IiLCJkZWZhdWx0U3RhdGUiLCJkZWZhdWx0SGVpZ2h0IiwiZGVmYXVsdFdpZHRoIiwiY2xlYXJTZWxlY3Rpb25DYWxsYmFjayIsInNhdmVDYWxsYmFjayIsInNldENhbnZhc0lzU2VsZWN0ZWQiLCJpbml0aWFsU3RhdGUiLCJpbml0aWFsV2lkdGgiLCJpbml0aWFsSGVpZ2h0Iiwic2V0Q2FudmFzIiwiY29udGFpbmVyIiwic2V0Q29udGFpbmVyIiwic2V0U2VsZWN0ZWRPYmplY3RzIiwiY2FudmFzSGlzdG9yeSIsInNldEhpc3RvcnlJbmRleCIsImVkaXRvciIsInVuZGVmaW5lZCIsImluaXQiLCJpbml0aWFsQ2FudmFzIiwiaW5pdGlhbENvbnRhaW5lciIsIk9iamVjdCIsInByb3RvdHlwZSIsImNvcm5lckNvbG9yIiwiY29ybmVyU3R5bGUiLCJib3JkZXJDb2xvciIsImJvcmRlclNjYWxlRmFjdG9yIiwidHJhbnNwYXJlbnRDb3JuZXJzIiwiYm9yZGVyT3BhY2l0eVdoZW5Nb3ZpbmciLCJjb3JuZXJTdHJva2VDb2xvciIsImluaXRpYWxXb3Jrc3BhY2UiLCJjdXJyZW50IiwiaGFzQ29udHJvbHMiLCJzaGFkb3ciLCJTaGFkb3ciLCJibHVyIiwic2V0V2lkdGgiLCJvZmZzZXRXaWR0aCIsInNldEhlaWdodCIsIm9mZnNldEhlaWdodCIsImNlbnRlck9iamVjdCIsImNsaXBQYXRoIiwiY3VycmVudFN0YXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});