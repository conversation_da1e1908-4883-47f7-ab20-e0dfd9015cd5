"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx":
/*!**********************************************************!*\
  !*** ./src/features/editor/components/shape-sidebar.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShapeSidebar: function() { return /* binding */ ShapeSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IoTriangle!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaDiamond!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaCircle,FaSquare,FaSquareFull!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _iconify_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @iconify/react */ \"(app-pages-browser)/./node_modules/@iconify/react/dist/iconify.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/shape-tool */ \"(app-pages-browser)/./src/features/editor/components/shape-tool.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Define available icons from Iconify\nconst iconifyElements = [\n    // Popular/Featured icons (shown by default)\n    {\n        name: \"Heart\",\n        icon: \"lucide:heart\",\n        category: \"shapes\",\n        featured: true\n    },\n    {\n        name: \"Star\",\n        icon: \"lucide:star\",\n        category: \"shapes\",\n        featured: true\n    },\n    {\n        name: \"Arrow Right\",\n        icon: \"lucide:arrow-right\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Left\",\n        icon: \"lucide:arrow-left\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Up\",\n        icon: \"lucide:arrow-up\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Down\",\n        icon: \"lucide:arrow-down\",\n        category: \"arrows\",\n        featured: true\n    },\n    // Additional arrows\n    {\n        name: \"Arrow Up Right\",\n        icon: \"lucide:arrow-up-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Up Left\",\n        icon: \"lucide:arrow-up-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down Right\",\n        icon: \"lucide:arrow-down-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down Left\",\n        icon: \"lucide:arrow-down-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Right\",\n        icon: \"lucide:chevron-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Left\",\n        icon: \"lucide:chevron-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Up\",\n        icon: \"lucide:chevron-up\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Down\",\n        icon: \"lucide:chevron-down\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Move\",\n        icon: \"lucide:move\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Rotate CW\",\n        icon: \"lucide:rotate-cw\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Rotate CCW\",\n        icon: \"lucide:rotate-ccw\",\n        category: \"arrows\"\n    },\n    // Interface icons\n    {\n        name: \"Home\",\n        icon: \"lucide:home\",\n        category: \"interface\"\n    },\n    {\n        name: \"User\",\n        icon: \"lucide:user\",\n        category: \"interface\"\n    },\n    {\n        name: \"Users\",\n        icon: \"lucide:users\",\n        category: \"interface\"\n    },\n    {\n        name: \"Settings\",\n        icon: \"lucide:settings\",\n        category: \"interface\"\n    },\n    {\n        name: \"Menu\",\n        icon: \"lucide:menu\",\n        category: \"interface\"\n    },\n    {\n        name: \"More Horizontal\",\n        icon: \"lucide:more-horizontal\",\n        category: \"interface\"\n    },\n    {\n        name: \"More Vertical\",\n        icon: \"lucide:more-vertical\",\n        category: \"interface\"\n    },\n    {\n        name: \"Grid\",\n        icon: \"lucide:grid-3x3\",\n        category: \"interface\"\n    },\n    {\n        name: \"List\",\n        icon: \"lucide:list\",\n        category: \"interface\"\n    },\n    {\n        name: \"Layout\",\n        icon: \"lucide:layout\",\n        category: \"interface\"\n    },\n    {\n        name: \"Sidebar\",\n        icon: \"lucide:sidebar\",\n        category: \"interface\"\n    },\n    {\n        name: \"Panel Left\",\n        icon: \"lucide:panel-left\",\n        category: \"interface\"\n    },\n    {\n        name: \"Panel Right\",\n        icon: \"lucide:panel-right\",\n        category: \"interface\"\n    },\n    // Communication\n    {\n        name: \"Mail\",\n        icon: \"lucide:mail\",\n        category: \"communication\"\n    },\n    {\n        name: \"Message Circle\",\n        icon: \"lucide:message-circle\",\n        category: \"communication\"\n    },\n    {\n        name: \"Message Square\",\n        icon: \"lucide:message-square\",\n        category: \"communication\"\n    },\n    {\n        name: \"Phone\",\n        icon: \"lucide:phone\",\n        category: \"communication\"\n    },\n    {\n        name: \"Phone Call\",\n        icon: \"lucide:phone-call\",\n        category: \"communication\"\n    },\n    {\n        name: \"Video\",\n        icon: \"lucide:video\",\n        category: \"communication\"\n    },\n    {\n        name: \"Send\",\n        icon: \"lucide:send\",\n        category: \"communication\"\n    },\n    {\n        name: \"Share\",\n        icon: \"lucide:share\",\n        category: \"communication\"\n    },\n    {\n        name: \"Share 2\",\n        icon: \"lucide:share-2\",\n        category: \"communication\"\n    },\n    // Time & Calendar\n    {\n        name: \"Calendar\",\n        icon: \"lucide:calendar\",\n        category: \"time\"\n    },\n    {\n        name: \"Calendar Days\",\n        icon: \"lucide:calendar-days\",\n        category: \"time\"\n    },\n    {\n        name: \"Clock\",\n        icon: \"lucide:clock\",\n        category: \"time\"\n    },\n    {\n        name: \"Timer\",\n        icon: \"lucide:timer\",\n        category: \"time\"\n    },\n    {\n        name: \"Alarm Clock\",\n        icon: \"lucide:alarm-clock\",\n        category: \"time\"\n    },\n    {\n        name: \"Hourglass\",\n        icon: \"lucide:hourglass\",\n        category: \"time\"\n    },\n    // Media & Entertainment\n    {\n        name: \"Camera\",\n        icon: \"lucide:camera\",\n        category: \"media\"\n    },\n    {\n        name: \"Image\",\n        icon: \"lucide:image\",\n        category: \"media\"\n    },\n    {\n        name: \"Images\",\n        icon: \"lucide:images\",\n        category: \"media\"\n    },\n    {\n        name: \"Video Camera\",\n        icon: \"lucide:video\",\n        category: \"media\"\n    },\n    {\n        name: \"Music\",\n        icon: \"lucide:music\",\n        category: \"media\"\n    },\n    {\n        name: \"Play\",\n        icon: \"lucide:play\",\n        category: \"media\"\n    },\n    {\n        name: \"Pause\",\n        icon: \"lucide:pause\",\n        category: \"media\"\n    },\n    {\n        name: \"Stop\",\n        icon: \"lucide:square\",\n        category: \"media\"\n    },\n    {\n        name: \"Skip Forward\",\n        icon: \"lucide:skip-forward\",\n        category: \"media\"\n    },\n    {\n        name: \"Skip Back\",\n        icon: \"lucide:skip-back\",\n        category: \"media\"\n    },\n    {\n        name: \"Fast Forward\",\n        icon: \"lucide:fast-forward\",\n        category: \"media\"\n    },\n    {\n        name: \"Rewind\",\n        icon: \"lucide:rewind\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume\",\n        icon: \"lucide:volume-2\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume Off\",\n        icon: \"lucide:volume-x\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume Low\",\n        icon: \"lucide:volume-1\",\n        category: \"media\"\n    },\n    {\n        name: \"Headphones\",\n        icon: \"lucide:headphones\",\n        category: \"media\"\n    },\n    {\n        name: \"Mic\",\n        icon: \"lucide:mic\",\n        category: \"media\"\n    },\n    {\n        name: \"Mic Off\",\n        icon: \"lucide:mic-off\",\n        category: \"media\"\n    },\n    // Technology\n    {\n        name: \"Wifi\",\n        icon: \"lucide:wifi\",\n        category: \"tech\"\n    },\n    {\n        name: \"Wifi Off\",\n        icon: \"lucide:wifi-off\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery\",\n        icon: \"lucide:battery\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery Low\",\n        icon: \"lucide:battery-low\",\n        category: \"tech\"\n    },\n    {\n        name: \"Bluetooth\",\n        icon: \"lucide:bluetooth\",\n        category: \"tech\"\n    },\n    {\n        name: \"Smartphone\",\n        icon: \"lucide:smartphone\",\n        category: \"tech\"\n    },\n    {\n        name: \"Laptop\",\n        icon: \"lucide:laptop\",\n        category: \"tech\"\n    },\n    {\n        name: \"Monitor\",\n        icon: \"lucide:monitor\",\n        category: \"tech\"\n    },\n    {\n        name: \"Tablet\",\n        icon: \"lucide:tablet\",\n        category: \"tech\"\n    },\n    {\n        name: \"Hard Drive\",\n        icon: \"lucide:hard-drive\",\n        category: \"tech\"\n    },\n    {\n        name: \"Server\",\n        icon: \"lucide:server\",\n        category: \"tech\"\n    },\n    {\n        name: \"Database\",\n        icon: \"lucide:database\",\n        category: \"tech\"\n    },\n    {\n        name: \"Cloud\",\n        icon: \"lucide:cloud\",\n        category: \"tech\"\n    },\n    {\n        name: \"Globe\",\n        icon: \"lucide:globe\",\n        category: \"tech\"\n    },\n    // Actions & Controls\n    {\n        name: \"Download\",\n        icon: \"lucide:download\",\n        category: \"actions\"\n    },\n    {\n        name: \"Upload\",\n        icon: \"lucide:upload\",\n        category: \"actions\"\n    },\n    {\n        name: \"Search\",\n        icon: \"lucide:search\",\n        category: \"actions\"\n    },\n    {\n        name: \"Plus\",\n        icon: \"lucide:plus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Minus\",\n        icon: \"lucide:minus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Check\",\n        icon: \"lucide:check\",\n        category: \"actions\"\n    },\n    {\n        name: \"X\",\n        icon: \"lucide:x\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit\",\n        icon: \"lucide:edit\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit 2\",\n        icon: \"lucide:edit-2\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit 3\",\n        icon: \"lucide:edit-3\",\n        category: \"actions\"\n    },\n    {\n        name: \"Trash\",\n        icon: \"lucide:trash\",\n        category: \"actions\"\n    },\n    {\n        name: \"Trash 2\",\n        icon: \"lucide:trash-2\",\n        category: \"actions\"\n    },\n    {\n        name: \"Copy\",\n        icon: \"lucide:copy\",\n        category: \"actions\"\n    },\n    {\n        name: \"Cut\",\n        icon: \"lucide:scissors\",\n        category: \"actions\"\n    },\n    {\n        name: \"Paste\",\n        icon: \"lucide:clipboard\",\n        category: \"actions\"\n    },\n    {\n        name: \"Save\",\n        icon: \"lucide:save\",\n        category: \"actions\"\n    },\n    {\n        name: \"Undo\",\n        icon: \"lucide:undo\",\n        category: \"actions\"\n    },\n    {\n        name: \"Redo\",\n        icon: \"lucide:redo\",\n        category: \"actions\"\n    },\n    {\n        name: \"Refresh\",\n        icon: \"lucide:refresh-cw\",\n        category: \"actions\"\n    },\n    {\n        name: \"Power\",\n        icon: \"lucide:power\",\n        category: \"actions\"\n    },\n    {\n        name: \"Lock\",\n        icon: \"lucide:lock\",\n        category: \"actions\"\n    },\n    {\n        name: \"Unlock\",\n        icon: \"lucide:unlock\",\n        category: \"actions\"\n    },\n    {\n        name: \"Eye\",\n        icon: \"lucide:eye\",\n        category: \"actions\"\n    },\n    {\n        name: \"Eye Off\",\n        icon: \"lucide:eye-off\",\n        category: \"actions\"\n    },\n    // Shapes & Symbols\n    {\n        name: \"Circle\",\n        icon: \"lucide:circle\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Square\",\n        icon: \"lucide:square\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Triangle\",\n        icon: \"lucide:triangle\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Hexagon\",\n        icon: \"lucide:hexagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Octagon\",\n        icon: \"lucide:octagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Diamond\",\n        icon: \"lucide:diamond\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Pentagon\",\n        icon: \"lucide:pentagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Bookmark\",\n        icon: \"lucide:bookmark\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Tag\",\n        icon: \"lucide:tag\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Flag\",\n        icon: \"lucide:flag\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Shield\",\n        icon: \"lucide:shield\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Award\",\n        icon: \"lucide:award\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Medal\",\n        icon: \"lucide:medal\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Crown\",\n        icon: \"lucide:crown\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Gem\",\n        icon: \"lucide:gem\",\n        category: \"shapes\"\n    },\n    // Weather & Nature\n    {\n        name: \"Sun\",\n        icon: \"lucide:sun\",\n        category: \"weather\"\n    },\n    {\n        name: \"Moon\",\n        icon: \"lucide:moon\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud\",\n        icon: \"lucide:cloud\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud Rain\",\n        icon: \"lucide:cloud-rain\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud Snow\",\n        icon: \"lucide:cloud-snow\",\n        category: \"weather\"\n    },\n    {\n        name: \"Lightning\",\n        icon: \"lucide:zap\",\n        category: \"weather\"\n    },\n    {\n        name: \"Umbrella\",\n        icon: \"lucide:umbrella\",\n        category: \"weather\"\n    },\n    {\n        name: \"Thermometer\",\n        icon: \"lucide:thermometer\",\n        category: \"weather\"\n    },\n    {\n        name: \"Wind\",\n        icon: \"lucide:wind\",\n        category: \"weather\"\n    },\n    {\n        name: \"Tree\",\n        icon: \"lucide:tree-pine\",\n        category: \"nature\"\n    },\n    {\n        name: \"Leaf\",\n        icon: \"lucide:leaf\",\n        category: \"nature\"\n    },\n    {\n        name: \"Flower\",\n        icon: \"lucide:flower\",\n        category: \"nature\"\n    },\n    {\n        name: \"Sprout\",\n        icon: \"lucide:sprout\",\n        category: \"nature\"\n    },\n    // Transportation\n    {\n        name: \"Car\",\n        icon: \"lucide:car\",\n        category: \"transport\"\n    },\n    {\n        name: \"Truck\",\n        icon: \"lucide:truck\",\n        category: \"transport\"\n    },\n    {\n        name: \"Bus\",\n        icon: \"lucide:bus\",\n        category: \"transport\"\n    },\n    {\n        name: \"Bike\",\n        icon: \"lucide:bike\",\n        category: \"transport\"\n    },\n    {\n        name: \"Plane\",\n        icon: \"lucide:plane\",\n        category: \"transport\"\n    },\n    {\n        name: \"Train\",\n        icon: \"lucide:train\",\n        category: \"transport\"\n    },\n    {\n        name: \"Ship\",\n        icon: \"lucide:ship\",\n        category: \"transport\"\n    },\n    {\n        name: \"Fuel\",\n        icon: \"lucide:fuel\",\n        category: \"transport\"\n    },\n    {\n        name: \"Map Pin\",\n        icon: \"lucide:map-pin\",\n        category: \"transport\"\n    },\n    {\n        name: \"Navigation\",\n        icon: \"lucide:navigation\",\n        category: \"transport\"\n    },\n    {\n        name: \"Compass\",\n        icon: \"lucide:compass\",\n        category: \"transport\"\n    },\n    // Food & Drink\n    {\n        name: \"Coffee\",\n        icon: \"lucide:coffee\",\n        category: \"food\"\n    },\n    {\n        name: \"Cup\",\n        icon: \"lucide:cup-soda\",\n        category: \"food\"\n    },\n    {\n        name: \"Wine\",\n        icon: \"lucide:wine\",\n        category: \"food\"\n    },\n    {\n        name: \"Beer\",\n        icon: \"lucide:beer\",\n        category: \"food\"\n    },\n    {\n        name: \"Pizza\",\n        icon: \"lucide:pizza\",\n        category: \"food\"\n    },\n    {\n        name: \"Apple\",\n        icon: \"lucide:apple\",\n        category: \"food\"\n    },\n    {\n        name: \"Cherry\",\n        icon: \"lucide:cherry\",\n        category: \"food\"\n    },\n    {\n        name: \"Cake\",\n        icon: \"lucide:cake\",\n        category: \"food\"\n    },\n    {\n        name: \"Ice Cream\",\n        icon: \"lucide:ice-cream\",\n        category: \"food\"\n    },\n    // Business & Finance\n    {\n        name: \"Briefcase\",\n        icon: \"lucide:briefcase\",\n        category: \"business\"\n    },\n    {\n        name: \"Building\",\n        icon: \"lucide:building\",\n        category: \"business\"\n    },\n    {\n        name: \"Building 2\",\n        icon: \"lucide:building-2\",\n        category: \"business\"\n    },\n    {\n        name: \"Store\",\n        icon: \"lucide:store\",\n        category: \"business\"\n    },\n    {\n        name: \"Factory\",\n        icon: \"lucide:factory\",\n        category: \"business\"\n    },\n    {\n        name: \"Banknote\",\n        icon: \"lucide:banknote\",\n        category: \"finance\"\n    },\n    {\n        name: \"Credit Card\",\n        icon: \"lucide:credit-card\",\n        category: \"finance\"\n    },\n    {\n        name: \"Wallet\",\n        icon: \"lucide:wallet\",\n        category: \"finance\"\n    },\n    {\n        name: \"Coins\",\n        icon: \"lucide:coins\",\n        category: \"finance\"\n    },\n    {\n        name: \"Dollar Sign\",\n        icon: \"lucide:dollar-sign\",\n        category: \"finance\"\n    },\n    {\n        name: \"Trending Up\",\n        icon: \"lucide:trending-up\",\n        category: \"finance\"\n    },\n    {\n        name: \"Trending Down\",\n        icon: \"lucide:trending-down\",\n        category: \"finance\"\n    },\n    {\n        name: \"Bar Chart\",\n        icon: \"lucide:bar-chart\",\n        category: \"finance\"\n    },\n    {\n        name: \"Line Chart\",\n        icon: \"lucide:line-chart\",\n        category: \"finance\"\n    },\n    {\n        name: \"Pie Chart\",\n        icon: \"lucide:pie-chart\",\n        category: \"finance\"\n    },\n    // Health & Medical\n    {\n        name: \"Heart Pulse\",\n        icon: \"lucide:heart-pulse\",\n        category: \"health\"\n    },\n    {\n        name: \"Activity\",\n        icon: \"lucide:activity\",\n        category: \"health\"\n    },\n    {\n        name: \"Pill\",\n        icon: \"lucide:pill\",\n        category: \"health\"\n    },\n    {\n        name: \"Stethoscope\",\n        icon: \"lucide:stethoscope\",\n        category: \"health\"\n    },\n    {\n        name: \"Cross\",\n        icon: \"lucide:cross\",\n        category: \"health\"\n    },\n    {\n        name: \"Band Aid\",\n        icon: \"lucide:bandage\",\n        category: \"health\"\n    },\n    // Sports & Games\n    {\n        name: \"Trophy\",\n        icon: \"lucide:trophy\",\n        category: \"sports\"\n    },\n    {\n        name: \"Target\",\n        icon: \"lucide:target\",\n        category: \"sports\"\n    },\n    {\n        name: \"Dumbbell\",\n        icon: \"lucide:dumbbell\",\n        category: \"sports\"\n    },\n    {\n        name: \"Football\",\n        icon: \"lucide:football\",\n        category: \"sports\"\n    },\n    {\n        name: \"Gamepad\",\n        icon: \"lucide:gamepad-2\",\n        category: \"games\"\n    },\n    {\n        name: \"Dice\",\n        icon: \"lucide:dice-1\",\n        category: \"games\"\n    },\n    {\n        name: \"Puzzle\",\n        icon: \"lucide:puzzle\",\n        category: \"games\"\n    },\n    // Tools & Utilities\n    {\n        name: \"Wrench\",\n        icon: \"lucide:wrench\",\n        category: \"tools\"\n    },\n    {\n        name: \"Hammer\",\n        icon: \"lucide:hammer\",\n        category: \"tools\"\n    },\n    {\n        name: \"Screwdriver\",\n        icon: \"lucide:screwdriver\",\n        category: \"tools\"\n    },\n    {\n        name: \"Paintbrush\",\n        icon: \"lucide:paintbrush\",\n        category: \"tools\"\n    },\n    {\n        name: \"Palette\",\n        icon: \"lucide:palette\",\n        category: \"tools\"\n    },\n    {\n        name: \"Ruler\",\n        icon: \"lucide:ruler\",\n        category: \"tools\"\n    },\n    {\n        name: \"Calculator\",\n        icon: \"lucide:calculator\",\n        category: \"tools\"\n    },\n    {\n        name: \"Flashlight\",\n        icon: \"lucide:flashlight\",\n        category: \"tools\"\n    },\n    {\n        name: \"Key\",\n        icon: \"lucide:key\",\n        category: \"tools\"\n    },\n    {\n        name: \"Magnet\",\n        icon: \"lucide:magnet\",\n        category: \"tools\"\n    }\n];\nconst ShapeSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    // Filter icons based on search term\n    const filteredIcons = iconifyElements.filter((element)=>element.name.toLowerCase().includes(searchTerm.toLowerCase()) || element.category.toLowerCase().includes(searchTerm.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"shapes\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__.ToolSidebarHeader, {\n                title: \"Elements\",\n                description: \"Add elements to your canvas\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                    placeholder: \"Search elements...\",\n                    value: searchTerm,\n                    onChange: (e)=>setSearchTerm(e.target.value),\n                    className: \"w-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-3 text-gray-700\",\n                            children: \"Basic Shapes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addCircle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCircle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addSoftRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquare\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquareFull\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addInverseTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle,\n                                    iconClassName: \"rotate-180\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addDiamond(),\n                                    icon: _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaDiamond\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-3 text-gray-700\",\n                            children: \"Icons & Elements\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4\",\n                            children: filteredIcons.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Add the icon as an SVG element to the canvas\n                                        // This is a placeholder - you might need to implement addIcon method in your editor\n                                        console.log(\"Adding \".concat(element.name, \" icon\"));\n                                    },\n                                    className: \"aspect-square border rounded-md p-3 hover:bg-gray-50 transition-colors flex items-center justify-center group\",\n                                    title: element.name,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iconify_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        icon: element.icon,\n                                        className: \"h-6 w-6 text-gray-700 group-hover:text-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, element.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShapeSidebar, \"+YdqPTpSlp4r5CWiFEQiF/UjThM=\");\n_c = ShapeSidebar;\nvar _c;\n$RefreshReg$(_c, \"ShapeSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\n"));

/***/ })

});