"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx":
/*!**********************************************************!*\
  !*** ./src/features/editor/components/shape-sidebar.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShapeSidebar: function() { return /* binding */ ShapeSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IoTriangle!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaDiamond!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaCircle,FaSquare,FaSquareFull!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _iconify_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @iconify/react */ \"(app-pages-browser)/./node_modules/@iconify/react/dist/iconify.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/shape-tool */ \"(app-pages-browser)/./src/features/editor/components/shape-tool.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Define available icons from Iconify\nconst iconifyElements = [\n    // Popular/Featured icons (shown by default)\n    {\n        name: \"Heart\",\n        icon: \"lucide:heart\",\n        category: \"shapes\",\n        featured: true\n    },\n    {\n        name: \"Star\",\n        icon: \"lucide:star\",\n        category: \"shapes\",\n        featured: true\n    },\n    {\n        name: \"Arrow Right\",\n        icon: \"lucide:arrow-right\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Left\",\n        icon: \"lucide:arrow-left\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Up\",\n        icon: \"lucide:arrow-up\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Down\",\n        icon: \"lucide:arrow-down\",\n        category: \"arrows\",\n        featured: true\n    },\n    // Additional arrows\n    {\n        name: \"Arrow Up Right\",\n        icon: \"lucide:arrow-up-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Up Left\",\n        icon: \"lucide:arrow-up-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down Right\",\n        icon: \"lucide:arrow-down-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down Left\",\n        icon: \"lucide:arrow-down-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Right\",\n        icon: \"lucide:chevron-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Left\",\n        icon: \"lucide:chevron-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Up\",\n        icon: \"lucide:chevron-up\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Down\",\n        icon: \"lucide:chevron-down\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Move\",\n        icon: \"lucide:move\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Rotate CW\",\n        icon: \"lucide:rotate-cw\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Rotate CCW\",\n        icon: \"lucide:rotate-ccw\",\n        category: \"arrows\"\n    },\n    // Interface icons\n    {\n        name: \"Home\",\n        icon: \"lucide:home\",\n        category: \"interface\"\n    },\n    {\n        name: \"User\",\n        icon: \"lucide:user\",\n        category: \"interface\"\n    },\n    {\n        name: \"Users\",\n        icon: \"lucide:users\",\n        category: \"interface\"\n    },\n    {\n        name: \"Settings\",\n        icon: \"lucide:settings\",\n        category: \"interface\"\n    },\n    {\n        name: \"Menu\",\n        icon: \"lucide:menu\",\n        category: \"interface\"\n    },\n    {\n        name: \"More Horizontal\",\n        icon: \"lucide:more-horizontal\",\n        category: \"interface\"\n    },\n    {\n        name: \"More Vertical\",\n        icon: \"lucide:more-vertical\",\n        category: \"interface\"\n    },\n    {\n        name: \"Grid\",\n        icon: \"lucide:grid-3x3\",\n        category: \"interface\"\n    },\n    {\n        name: \"List\",\n        icon: \"lucide:list\",\n        category: \"interface\"\n    },\n    {\n        name: \"Layout\",\n        icon: \"lucide:layout\",\n        category: \"interface\"\n    },\n    {\n        name: \"Sidebar\",\n        icon: \"lucide:sidebar\",\n        category: \"interface\"\n    },\n    {\n        name: \"Panel Left\",\n        icon: \"lucide:panel-left\",\n        category: \"interface\"\n    },\n    {\n        name: \"Panel Right\",\n        icon: \"lucide:panel-right\",\n        category: \"interface\"\n    },\n    // Communication\n    {\n        name: \"Mail\",\n        icon: \"lucide:mail\",\n        category: \"communication\"\n    },\n    {\n        name: \"Message Circle\",\n        icon: \"lucide:message-circle\",\n        category: \"communication\"\n    },\n    {\n        name: \"Message Square\",\n        icon: \"lucide:message-square\",\n        category: \"communication\"\n    },\n    {\n        name: \"Phone\",\n        icon: \"lucide:phone\",\n        category: \"communication\"\n    },\n    {\n        name: \"Phone Call\",\n        icon: \"lucide:phone-call\",\n        category: \"communication\"\n    },\n    {\n        name: \"Video\",\n        icon: \"lucide:video\",\n        category: \"communication\"\n    },\n    {\n        name: \"Send\",\n        icon: \"lucide:send\",\n        category: \"communication\"\n    },\n    {\n        name: \"Share\",\n        icon: \"lucide:share\",\n        category: \"communication\"\n    },\n    {\n        name: \"Share 2\",\n        icon: \"lucide:share-2\",\n        category: \"communication\"\n    },\n    // Time & Calendar\n    {\n        name: \"Calendar\",\n        icon: \"lucide:calendar\",\n        category: \"time\"\n    },\n    {\n        name: \"Calendar Days\",\n        icon: \"lucide:calendar-days\",\n        category: \"time\"\n    },\n    {\n        name: \"Clock\",\n        icon: \"lucide:clock\",\n        category: \"time\"\n    },\n    {\n        name: \"Timer\",\n        icon: \"lucide:timer\",\n        category: \"time\"\n    },\n    {\n        name: \"Alarm Clock\",\n        icon: \"lucide:alarm-clock\",\n        category: \"time\"\n    },\n    {\n        name: \"Hourglass\",\n        icon: \"lucide:hourglass\",\n        category: \"time\"\n    },\n    // Media & Entertainment\n    {\n        name: \"Camera\",\n        icon: \"lucide:camera\",\n        category: \"media\"\n    },\n    {\n        name: \"Image\",\n        icon: \"lucide:image\",\n        category: \"media\"\n    },\n    {\n        name: \"Images\",\n        icon: \"lucide:images\",\n        category: \"media\"\n    },\n    {\n        name: \"Video Camera\",\n        icon: \"lucide:video\",\n        category: \"media\"\n    },\n    {\n        name: \"Music\",\n        icon: \"lucide:music\",\n        category: \"media\"\n    },\n    {\n        name: \"Play\",\n        icon: \"lucide:play\",\n        category: \"media\"\n    },\n    {\n        name: \"Pause\",\n        icon: \"lucide:pause\",\n        category: \"media\"\n    },\n    {\n        name: \"Stop\",\n        icon: \"lucide:square\",\n        category: \"media\"\n    },\n    {\n        name: \"Skip Forward\",\n        icon: \"lucide:skip-forward\",\n        category: \"media\"\n    },\n    {\n        name: \"Skip Back\",\n        icon: \"lucide:skip-back\",\n        category: \"media\"\n    },\n    {\n        name: \"Fast Forward\",\n        icon: \"lucide:fast-forward\",\n        category: \"media\"\n    },\n    {\n        name: \"Rewind\",\n        icon: \"lucide:rewind\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume\",\n        icon: \"lucide:volume-2\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume Off\",\n        icon: \"lucide:volume-x\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume Low\",\n        icon: \"lucide:volume-1\",\n        category: \"media\"\n    },\n    {\n        name: \"Headphones\",\n        icon: \"lucide:headphones\",\n        category: \"media\"\n    },\n    {\n        name: \"Mic\",\n        icon: \"lucide:mic\",\n        category: \"media\"\n    },\n    {\n        name: \"Mic Off\",\n        icon: \"lucide:mic-off\",\n        category: \"media\"\n    },\n    // Technology\n    {\n        name: \"Wifi\",\n        icon: \"lucide:wifi\",\n        category: \"tech\"\n    },\n    {\n        name: \"Wifi Off\",\n        icon: \"lucide:wifi-off\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery\",\n        icon: \"lucide:battery\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery Low\",\n        icon: \"lucide:battery-low\",\n        category: \"tech\"\n    },\n    {\n        name: \"Bluetooth\",\n        icon: \"lucide:bluetooth\",\n        category: \"tech\"\n    },\n    {\n        name: \"Smartphone\",\n        icon: \"lucide:smartphone\",\n        category: \"tech\"\n    },\n    {\n        name: \"Laptop\",\n        icon: \"lucide:laptop\",\n        category: \"tech\"\n    },\n    {\n        name: \"Monitor\",\n        icon: \"lucide:monitor\",\n        category: \"tech\"\n    },\n    {\n        name: \"Tablet\",\n        icon: \"lucide:tablet\",\n        category: \"tech\"\n    },\n    {\n        name: \"Hard Drive\",\n        icon: \"lucide:hard-drive\",\n        category: \"tech\"\n    },\n    {\n        name: \"Server\",\n        icon: \"lucide:server\",\n        category: \"tech\"\n    },\n    {\n        name: \"Database\",\n        icon: \"lucide:database\",\n        category: \"tech\"\n    },\n    {\n        name: \"Cloud\",\n        icon: \"lucide:cloud\",\n        category: \"tech\"\n    },\n    {\n        name: \"Globe\",\n        icon: \"lucide:globe\",\n        category: \"tech\"\n    },\n    // Actions & Controls\n    {\n        name: \"Download\",\n        icon: \"lucide:download\",\n        category: \"actions\"\n    },\n    {\n        name: \"Upload\",\n        icon: \"lucide:upload\",\n        category: \"actions\"\n    },\n    {\n        name: \"Search\",\n        icon: \"lucide:search\",\n        category: \"actions\"\n    },\n    {\n        name: \"Plus\",\n        icon: \"lucide:plus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Minus\",\n        icon: \"lucide:minus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Check\",\n        icon: \"lucide:check\",\n        category: \"actions\"\n    },\n    {\n        name: \"X\",\n        icon: \"lucide:x\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit\",\n        icon: \"lucide:edit\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit 2\",\n        icon: \"lucide:edit-2\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit 3\",\n        icon: \"lucide:edit-3\",\n        category: \"actions\"\n    },\n    {\n        name: \"Trash\",\n        icon: \"lucide:trash\",\n        category: \"actions\"\n    },\n    {\n        name: \"Trash 2\",\n        icon: \"lucide:trash-2\",\n        category: \"actions\"\n    },\n    {\n        name: \"Copy\",\n        icon: \"lucide:copy\",\n        category: \"actions\"\n    },\n    {\n        name: \"Cut\",\n        icon: \"lucide:scissors\",\n        category: \"actions\"\n    },\n    {\n        name: \"Paste\",\n        icon: \"lucide:clipboard\",\n        category: \"actions\"\n    },\n    {\n        name: \"Save\",\n        icon: \"lucide:save\",\n        category: \"actions\"\n    },\n    {\n        name: \"Undo\",\n        icon: \"lucide:undo\",\n        category: \"actions\"\n    },\n    {\n        name: \"Redo\",\n        icon: \"lucide:redo\",\n        category: \"actions\"\n    },\n    {\n        name: \"Refresh\",\n        icon: \"lucide:refresh-cw\",\n        category: \"actions\"\n    },\n    {\n        name: \"Power\",\n        icon: \"lucide:power\",\n        category: \"actions\"\n    },\n    {\n        name: \"Lock\",\n        icon: \"lucide:lock\",\n        category: \"actions\"\n    },\n    {\n        name: \"Unlock\",\n        icon: \"lucide:unlock\",\n        category: \"actions\"\n    },\n    {\n        name: \"Eye\",\n        icon: \"lucide:eye\",\n        category: \"actions\"\n    },\n    {\n        name: \"Eye Off\",\n        icon: \"lucide:eye-off\",\n        category: \"actions\"\n    },\n    // Shapes & Symbols\n    {\n        name: \"Circle\",\n        icon: \"lucide:circle\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Square\",\n        icon: \"lucide:square\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Triangle\",\n        icon: \"lucide:triangle\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Hexagon\",\n        icon: \"lucide:hexagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Octagon\",\n        icon: \"lucide:octagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Diamond\",\n        icon: \"lucide:diamond\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Pentagon\",\n        icon: \"lucide:pentagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Bookmark\",\n        icon: \"lucide:bookmark\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Tag\",\n        icon: \"lucide:tag\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Flag\",\n        icon: \"lucide:flag\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Shield\",\n        icon: \"lucide:shield\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Award\",\n        icon: \"lucide:award\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Medal\",\n        icon: \"lucide:medal\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Crown\",\n        icon: \"lucide:crown\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Gem\",\n        icon: \"lucide:gem\",\n        category: \"shapes\"\n    },\n    // Weather & Nature\n    {\n        name: \"Sun\",\n        icon: \"lucide:sun\",\n        category: \"weather\"\n    },\n    {\n        name: \"Moon\",\n        icon: \"lucide:moon\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud\",\n        icon: \"lucide:cloud\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud Rain\",\n        icon: \"lucide:cloud-rain\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud Snow\",\n        icon: \"lucide:cloud-snow\",\n        category: \"weather\"\n    },\n    {\n        name: \"Lightning\",\n        icon: \"lucide:zap\",\n        category: \"weather\"\n    },\n    {\n        name: \"Umbrella\",\n        icon: \"lucide:umbrella\",\n        category: \"weather\"\n    },\n    {\n        name: \"Thermometer\",\n        icon: \"lucide:thermometer\",\n        category: \"weather\"\n    },\n    {\n        name: \"Wind\",\n        icon: \"lucide:wind\",\n        category: \"weather\"\n    },\n    {\n        name: \"Tree\",\n        icon: \"lucide:tree-pine\",\n        category: \"nature\"\n    },\n    {\n        name: \"Leaf\",\n        icon: \"lucide:leaf\",\n        category: \"nature\"\n    },\n    {\n        name: \"Flower\",\n        icon: \"lucide:flower\",\n        category: \"nature\"\n    },\n    {\n        name: \"Sprout\",\n        icon: \"lucide:sprout\",\n        category: \"nature\"\n    },\n    // Transportation\n    {\n        name: \"Car\",\n        icon: \"lucide:car\",\n        category: \"transport\"\n    },\n    {\n        name: \"Truck\",\n        icon: \"lucide:truck\",\n        category: \"transport\"\n    },\n    {\n        name: \"Bus\",\n        icon: \"lucide:bus\",\n        category: \"transport\"\n    },\n    {\n        name: \"Bike\",\n        icon: \"lucide:bike\",\n        category: \"transport\"\n    },\n    {\n        name: \"Plane\",\n        icon: \"lucide:plane\",\n        category: \"transport\"\n    },\n    {\n        name: \"Train\",\n        icon: \"lucide:train\",\n        category: \"transport\"\n    },\n    {\n        name: \"Ship\",\n        icon: \"lucide:ship\",\n        category: \"transport\"\n    },\n    {\n        name: \"Fuel\",\n        icon: \"lucide:fuel\",\n        category: \"transport\"\n    },\n    {\n        name: \"Map Pin\",\n        icon: \"lucide:map-pin\",\n        category: \"transport\"\n    },\n    {\n        name: \"Navigation\",\n        icon: \"lucide:navigation\",\n        category: \"transport\"\n    },\n    {\n        name: \"Compass\",\n        icon: \"lucide:compass\",\n        category: \"transport\"\n    },\n    // Food & Drink\n    {\n        name: \"Coffee\",\n        icon: \"lucide:coffee\",\n        category: \"food\"\n    },\n    {\n        name: \"Cup\",\n        icon: \"lucide:cup-soda\",\n        category: \"food\"\n    },\n    {\n        name: \"Wine\",\n        icon: \"lucide:wine\",\n        category: \"food\"\n    },\n    {\n        name: \"Beer\",\n        icon: \"lucide:beer\",\n        category: \"food\"\n    },\n    {\n        name: \"Pizza\",\n        icon: \"lucide:pizza\",\n        category: \"food\"\n    },\n    {\n        name: \"Apple\",\n        icon: \"lucide:apple\",\n        category: \"food\"\n    },\n    {\n        name: \"Cherry\",\n        icon: \"lucide:cherry\",\n        category: \"food\"\n    },\n    {\n        name: \"Cake\",\n        icon: \"lucide:cake\",\n        category: \"food\"\n    },\n    {\n        name: \"Ice Cream\",\n        icon: \"lucide:ice-cream\",\n        category: \"food\"\n    },\n    // Business & Finance\n    {\n        name: \"Briefcase\",\n        icon: \"lucide:briefcase\",\n        category: \"business\"\n    },\n    {\n        name: \"Building\",\n        icon: \"lucide:building\",\n        category: \"business\"\n    },\n    {\n        name: \"Building 2\",\n        icon: \"lucide:building-2\",\n        category: \"business\"\n    },\n    {\n        name: \"Store\",\n        icon: \"lucide:store\",\n        category: \"business\"\n    },\n    {\n        name: \"Factory\",\n        icon: \"lucide:factory\",\n        category: \"business\"\n    },\n    {\n        name: \"Banknote\",\n        icon: \"lucide:banknote\",\n        category: \"finance\"\n    },\n    {\n        name: \"Credit Card\",\n        icon: \"lucide:credit-card\",\n        category: \"finance\"\n    },\n    {\n        name: \"Wallet\",\n        icon: \"lucide:wallet\",\n        category: \"finance\"\n    },\n    {\n        name: \"Coins\",\n        icon: \"lucide:coins\",\n        category: \"finance\"\n    },\n    {\n        name: \"Dollar Sign\",\n        icon: \"lucide:dollar-sign\",\n        category: \"finance\"\n    },\n    {\n        name: \"Trending Up\",\n        icon: \"lucide:trending-up\",\n        category: \"finance\"\n    },\n    {\n        name: \"Trending Down\",\n        icon: \"lucide:trending-down\",\n        category: \"finance\"\n    },\n    {\n        name: \"Bar Chart\",\n        icon: \"lucide:bar-chart\",\n        category: \"finance\"\n    },\n    {\n        name: \"Line Chart\",\n        icon: \"lucide:line-chart\",\n        category: \"finance\"\n    },\n    {\n        name: \"Pie Chart\",\n        icon: \"lucide:pie-chart\",\n        category: \"finance\"\n    },\n    // Health & Medical\n    {\n        name: \"Heart Pulse\",\n        icon: \"lucide:heart-pulse\",\n        category: \"health\"\n    },\n    {\n        name: \"Activity\",\n        icon: \"lucide:activity\",\n        category: \"health\"\n    },\n    {\n        name: \"Pill\",\n        icon: \"lucide:pill\",\n        category: \"health\"\n    },\n    {\n        name: \"Stethoscope\",\n        icon: \"lucide:stethoscope\",\n        category: \"health\"\n    },\n    {\n        name: \"Cross\",\n        icon: \"lucide:cross\",\n        category: \"health\"\n    },\n    {\n        name: \"Band Aid\",\n        icon: \"lucide:bandage\",\n        category: \"health\"\n    },\n    // Sports & Games\n    {\n        name: \"Trophy\",\n        icon: \"lucide:trophy\",\n        category: \"sports\"\n    },\n    {\n        name: \"Target\",\n        icon: \"lucide:target\",\n        category: \"sports\"\n    },\n    {\n        name: \"Dumbbell\",\n        icon: \"lucide:dumbbell\",\n        category: \"sports\"\n    },\n    {\n        name: \"Football\",\n        icon: \"lucide:football\",\n        category: \"sports\"\n    },\n    {\n        name: \"Gamepad\",\n        icon: \"lucide:gamepad-2\",\n        category: \"games\"\n    },\n    {\n        name: \"Dice\",\n        icon: \"lucide:dice-1\",\n        category: \"games\"\n    },\n    {\n        name: \"Puzzle\",\n        icon: \"lucide:puzzle\",\n        category: \"games\"\n    },\n    // Tools & Utilities\n    {\n        name: \"Wrench\",\n        icon: \"lucide:wrench\",\n        category: \"tools\"\n    },\n    {\n        name: \"Hammer\",\n        icon: \"lucide:hammer\",\n        category: \"tools\"\n    },\n    {\n        name: \"Screwdriver\",\n        icon: \"lucide:screwdriver\",\n        category: \"tools\"\n    },\n    {\n        name: \"Paintbrush\",\n        icon: \"lucide:paintbrush\",\n        category: \"tools\"\n    },\n    {\n        name: \"Palette\",\n        icon: \"lucide:palette\",\n        category: \"tools\"\n    },\n    {\n        name: \"Ruler\",\n        icon: \"lucide:ruler\",\n        category: \"tools\"\n    },\n    {\n        name: \"Calculator\",\n        icon: \"lucide:calculator\",\n        category: \"tools\"\n    },\n    {\n        name: \"Flashlight\",\n        icon: \"lucide:flashlight\",\n        category: \"tools\"\n    },\n    {\n        name: \"Key\",\n        icon: \"lucide:key\",\n        category: \"tools\"\n    },\n    {\n        name: \"Magnet\",\n        icon: \"lucide:magnet\",\n        category: \"tools\"\n    },\n    // Education & Learning\n    {\n        name: \"Book\",\n        icon: \"lucide:book\",\n        category: \"education\"\n    },\n    {\n        name: \"Book Open\",\n        icon: \"lucide:book-open\",\n        category: \"education\"\n    },\n    {\n        name: \"Bookmark\",\n        icon: \"lucide:bookmark\",\n        category: \"education\"\n    },\n    {\n        name: \"Graduation Cap\",\n        icon: \"lucide:graduation-cap\",\n        category: \"education\"\n    },\n    {\n        name: \"Pencil\",\n        icon: \"lucide:pencil\",\n        category: \"education\"\n    },\n    {\n        name: \"Pen\",\n        icon: \"lucide:pen-tool\",\n        category: \"education\"\n    },\n    {\n        name: \"Eraser\",\n        icon: \"lucide:eraser\",\n        category: \"education\"\n    },\n    {\n        name: \"Highlighter\",\n        icon: \"lucide:highlighter\",\n        category: \"education\"\n    },\n    {\n        name: \"Notebook\",\n        icon: \"lucide:notebook\",\n        category: \"education\"\n    },\n    {\n        name: \"Library\",\n        icon: \"lucide:library\",\n        category: \"education\"\n    },\n    {\n        name: \"School\",\n        icon: \"lucide:school\",\n        category: \"education\"\n    },\n    {\n        name: \"Backpack\",\n        icon: \"lucide:backpack\",\n        category: \"education\"\n    },\n    // Social & Communication\n    {\n        name: \"Users 2\",\n        icon: \"lucide:users-2\",\n        category: \"social\"\n    },\n    {\n        name: \"User Plus\",\n        icon: \"lucide:user-plus\",\n        category: \"social\"\n    },\n    {\n        name: \"User Minus\",\n        icon: \"lucide:user-minus\",\n        category: \"social\"\n    },\n    {\n        name: \"User Check\",\n        icon: \"lucide:user-check\",\n        category: \"social\"\n    },\n    {\n        name: \"User X\",\n        icon: \"lucide:user-x\",\n        category: \"social\"\n    },\n    {\n        name: \"Team\",\n        icon: \"lucide:users\",\n        category: \"social\"\n    },\n    {\n        name: \"Handshake\",\n        icon: \"lucide:handshake\",\n        category: \"social\"\n    },\n    {\n        name: \"Thumbs Up\",\n        icon: \"lucide:thumbs-up\",\n        category: \"social\"\n    },\n    {\n        name: \"Thumbs Down\",\n        icon: \"lucide:thumbs-down\",\n        category: \"social\"\n    },\n    {\n        name: \"Smile\",\n        icon: \"lucide:smile\",\n        category: \"social\"\n    },\n    {\n        name: \"Frown\",\n        icon: \"lucide:frown\",\n        category: \"social\"\n    },\n    {\n        name: \"Meh\",\n        icon: \"lucide:meh\",\n        category: \"social\"\n    },\n    {\n        name: \"Laugh\",\n        icon: \"lucide:laugh\",\n        category: \"social\"\n    },\n    // Security & Privacy\n    {\n        name: \"Shield Check\",\n        icon: \"lucide:shield-check\",\n        category: \"security\"\n    },\n    {\n        name: \"Shield Alert\",\n        icon: \"lucide:shield-alert\",\n        category: \"security\"\n    },\n    {\n        name: \"Shield X\",\n        icon: \"lucide:shield-x\",\n        category: \"security\"\n    },\n    {\n        name: \"Lock Open\",\n        icon: \"lucide:lock-open\",\n        category: \"security\"\n    },\n    {\n        name: \"Key Round\",\n        icon: \"lucide:key-round\",\n        category: \"security\"\n    },\n    {\n        name: \"Fingerprint\",\n        icon: \"lucide:fingerprint\",\n        category: \"security\"\n    },\n    {\n        name: \"Scan\",\n        icon: \"lucide:scan\",\n        category: \"security\"\n    },\n    {\n        name: \"Scan Line\",\n        icon: \"lucide:scan-line\",\n        category: \"security\"\n    },\n    // Files & Documents\n    {\n        name: \"File\",\n        icon: \"lucide:file\",\n        category: \"files\"\n    },\n    {\n        name: \"File Text\",\n        icon: \"lucide:file-text\",\n        category: \"files\"\n    },\n    {\n        name: \"File Image\",\n        icon: \"lucide:file-image\",\n        category: \"files\"\n    },\n    {\n        name: \"File Video\",\n        icon: \"lucide:file-video\",\n        category: \"files\"\n    },\n    {\n        name: \"File Audio\",\n        icon: \"lucide:file-audio\",\n        category: \"files\"\n    },\n    {\n        name: \"File Code\",\n        icon: \"lucide:file-code\",\n        category: \"files\"\n    },\n    {\n        name: \"File Spreadsheet\",\n        icon: \"lucide:file-spreadsheet\",\n        category: \"files\"\n    },\n    {\n        name: \"Folder\",\n        icon: \"lucide:folder\",\n        category: \"files\"\n    },\n    {\n        name: \"Folder Open\",\n        icon: \"lucide:folder-open\",\n        category: \"files\"\n    },\n    {\n        name: \"Folder Plus\",\n        icon: \"lucide:folder-plus\",\n        category: \"files\"\n    },\n    {\n        name: \"Archive\",\n        icon: \"lucide:archive\",\n        category: \"files\"\n    },\n    {\n        name: \"Package\",\n        icon: \"lucide:package\",\n        category: \"files\"\n    },\n    // Maps & Location\n    {\n        name: \"Map\",\n        icon: \"lucide:map\",\n        category: \"location\"\n    },\n    {\n        name: \"Map Pin\",\n        icon: \"lucide:map-pin\",\n        category: \"location\"\n    },\n    {\n        name: \"Navigation 2\",\n        icon: \"lucide:navigation-2\",\n        category: \"location\"\n    },\n    {\n        name: \"Route\",\n        icon: \"lucide:route\",\n        category: \"location\"\n    },\n    {\n        name: \"Signpost\",\n        icon: \"lucide:signpost\",\n        category: \"location\"\n    },\n    {\n        name: \"Milestone\",\n        icon: \"lucide:milestone\",\n        category: \"location\"\n    },\n    {\n        name: \"Locate\",\n        icon: \"lucide:locate\",\n        category: \"location\"\n    },\n    {\n        name: \"Locate Fixed\",\n        icon: \"lucide:locate-fixed\",\n        category: \"location\"\n    },\n    {\n        name: \"Locate Off\",\n        icon: \"lucide:locate-off\",\n        category: \"location\"\n    },\n    // Notifications & Alerts\n    {\n        name: \"Bell\",\n        icon: \"lucide:bell\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Bell Off\",\n        icon: \"lucide:bell-off\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Bell Ring\",\n        icon: \"lucide:bell-ring\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Alert Circle\",\n        icon: \"lucide:alert-circle\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Alert Triangle\",\n        icon: \"lucide:alert-triangle\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Alert Octagon\",\n        icon: \"lucide:alert-octagon\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Info\",\n        icon: \"lucide:info\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Help Circle\",\n        icon: \"lucide:help-circle\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Question Mark\",\n        icon: \"lucide:question-mark-circle\",\n        category: \"notifications\"\n    },\n    // E-commerce & Shopping\n    {\n        name: \"Shopping Cart\",\n        icon: \"lucide:shopping-cart\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Shopping Bag\",\n        icon: \"lucide:shopping-bag\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Gift\",\n        icon: \"lucide:gift\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Tag\",\n        icon: \"lucide:tag\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Tags\",\n        icon: \"lucide:tags\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Receipt\",\n        icon: \"lucide:receipt\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Percent\",\n        icon: \"lucide:percent\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Ticket\",\n        icon: \"lucide:ticket\",\n        category: \"shopping\"\n    },\n    // Design & Creative\n    {\n        name: \"Brush\",\n        icon: \"lucide:brush\",\n        category: \"design\"\n    },\n    {\n        name: \"Pen Tool\",\n        icon: \"lucide:pen-tool\",\n        category: \"design\"\n    },\n    {\n        name: \"Pipette\",\n        icon: \"lucide:pipette\",\n        category: \"design\"\n    },\n    {\n        name: \"Layers\",\n        icon: \"lucide:layers\",\n        category: \"design\"\n    },\n    {\n        name: \"Layout Grid\",\n        icon: \"lucide:layout-grid\",\n        category: \"design\"\n    },\n    {\n        name: \"Crop\",\n        icon: \"lucide:crop\",\n        category: \"design\"\n    },\n    {\n        name: \"Scissors\",\n        icon: \"lucide:scissors\",\n        category: \"design\"\n    },\n    {\n        name: \"Combine\",\n        icon: \"lucide:combine\",\n        category: \"design\"\n    },\n    {\n        name: \"Separate\",\n        icon: \"lucide:separate-horizontal\",\n        category: \"design\"\n    },\n    // Emoji & Expressions\n    {\n        name: \"Heart\",\n        icon: \"lucide:heart\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Heart Broken\",\n        icon: \"lucide:heart-crack\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Kiss\",\n        icon: \"lucide:kiss\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Angry\",\n        icon: \"lucide:angry\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Surprised\",\n        icon: \"lucide:surprised\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Wink\",\n        icon: \"lucide:eye-off\",\n        category: \"emoji\"\n    },\n    // Miscellaneous\n    {\n        name: \"Lightbulb\",\n        icon: \"lucide:lightbulb\",\n        category: \"misc\"\n    },\n    {\n        name: \"Zap\",\n        icon: \"lucide:zap\",\n        category: \"misc\"\n    },\n    {\n        name: \"Fire\",\n        icon: \"lucide:flame\",\n        category: \"misc\"\n    },\n    {\n        name: \"Snowflake\",\n        icon: \"lucide:snowflake\",\n        category: \"misc\"\n    },\n    {\n        name: \"Droplet\",\n        icon: \"lucide:droplet\",\n        category: \"misc\"\n    },\n    {\n        name: \"Feather\",\n        icon: \"lucide:feather\",\n        category: \"misc\"\n    },\n    {\n        name: \"Anchor\",\n        icon: \"lucide:anchor\",\n        category: \"misc\"\n    },\n    {\n        name: \"Infinity\",\n        icon: \"lucide:infinity\",\n        category: \"misc\"\n    },\n    {\n        name: \"Atom\",\n        icon: \"lucide:atom\",\n        category: \"misc\"\n    },\n    {\n        name: \"Dna\",\n        icon: \"lucide:dna\",\n        category: \"misc\"\n    },\n    {\n        name: \"Microscope\",\n        icon: \"lucide:microscope\",\n        category: \"misc\"\n    },\n    {\n        name: \"Telescope\",\n        icon: \"lucide:telescope\",\n        category: \"misc\"\n    },\n    {\n        name: \"Rocket\",\n        icon: \"lucide:rocket\",\n        category: \"misc\"\n    },\n    {\n        name: \"Satellite\",\n        icon: \"lucide:satellite\",\n        category: \"misc\"\n    },\n    {\n        name: \"Orbit\",\n        icon: \"lucide:orbit\",\n        category: \"misc\"\n    },\n    // Additional Popular Icons\n    {\n        name: \"Bookmark Plus\",\n        icon: \"lucide:bookmark-plus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Bookmark Minus\",\n        icon: \"lucide:bookmark-minus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Bookmark Check\",\n        icon: \"lucide:bookmark-check\",\n        category: \"actions\"\n    },\n    {\n        name: \"Bookmark X\",\n        icon: \"lucide:bookmark-x\",\n        category: \"actions\"\n    },\n    {\n        name: \"Filter\",\n        icon: \"lucide:filter\",\n        category: \"actions\"\n    },\n    {\n        name: \"Sort Asc\",\n        icon: \"lucide:sort-asc\",\n        category: \"actions\"\n    },\n    {\n        name: \"Sort Desc\",\n        icon: \"lucide:sort-desc\",\n        category: \"actions\"\n    },\n    {\n        name: \"Maximize\",\n        icon: \"lucide:maximize\",\n        category: \"actions\"\n    },\n    {\n        name: \"Minimize\",\n        icon: \"lucide:minimize\",\n        category: \"actions\"\n    },\n    {\n        name: \"Expand\",\n        icon: \"lucide:expand\",\n        category: \"actions\"\n    },\n    {\n        name: \"Shrink\",\n        icon: \"lucide:shrink\",\n        category: \"actions\"\n    },\n    {\n        name: \"Fullscreen\",\n        icon: \"lucide:fullscreen\",\n        category: \"actions\"\n    },\n    {\n        name: \"Exit Fullscreen\",\n        icon: \"lucide:minimize-2\",\n        category: \"actions\"\n    }\n];\nconst ShapeSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    // Filter icons based on search term\n    const filteredIcons = searchTerm ? iconifyElements.filter((element)=>element.name.toLowerCase().includes(searchTerm.toLowerCase()) || element.category.toLowerCase().includes(searchTerm.toLowerCase())) : iconifyElements.filter((element)=>element.featured);\n    // Get display icons (6 featured by default, all filtered when searching)\n    const displayIcons = searchTerm ? filteredIcons : filteredIcons.slice(0, 6);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"shapes\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__.ToolSidebarHeader, {\n                title: \"Elements\",\n                description: \"Add elements to your canvas\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                    placeholder: \"Search elements...\",\n                    value: searchTerm,\n                    onChange: (e)=>setSearchTerm(e.target.value),\n                    className: \"w-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-3 text-gray-700\",\n                            children: \"Basic Shapes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addCircle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCircle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addSoftRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquare\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquareFull\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addInverseTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle,\n                                    iconClassName: \"rotate-180\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addDiamond(),\n                                    icon: _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaDiamond\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Icons & Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, undefined),\n                                searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        filteredIcons.length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, undefined),\n                                !searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        iconifyElements.filter((el)=>!el.featured).length,\n                                        \" more available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4\",\n                            children: displayIcons.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        editor === null || editor === void 0 ? void 0 : editor.addIcon(element.icon);\n                                    },\n                                    className: \"aspect-square border rounded-md p-3 hover:bg-gray-50 transition-colors flex items-center justify-center group\",\n                                    title: element.name,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iconify_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        icon: element.icon,\n                                        className: \"h-6 w-6 text-gray-700 group-hover:text-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, element.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, undefined),\n                        !searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-gray-50 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 text-center\",\n                                children: [\n                                    \"\\uD83D\\uDD0D Search to discover \",\n                                    iconifyElements.filter((el)=>!el.featured).length,\n                                    \"+ more icons across categories like weather, food, business, education, social, security, and more!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, undefined),\n                        searchTerm && filteredIcons.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-gray-50 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 text-center\",\n                                children: [\n                                    'No icons found for \"',\n                                    searchTerm,\n                                    '\". Try searching for categories like \"arrow\", \"weather\", \"food\", or \"business\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n        lineNumber: 403,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShapeSidebar, \"+YdqPTpSlp4r5CWiFEQiF/UjThM=\");\n_c = ShapeSidebar;\nvar _c;\n$RefreshReg$(_c, \"ShapeSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\n"));

/***/ })

});