"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_60__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_61__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-open.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_62__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_63__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_64__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_65__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_66__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_67__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_68__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_69__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_70__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_71__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_72__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_73__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_74__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_75__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_76__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_77__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_78__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_79__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_80__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_81__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_82__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_83__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_84__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_85__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_86__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_87__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/umbrella.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_88__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/snowflake.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_89__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_90__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_91__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_92__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trees.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_93__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flower.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_94__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_95__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pizza.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_96__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_97__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wine.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_98__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_99__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_100__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_101__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_102__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_103__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/keyboard.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_104__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_105__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_106__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/usb.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_107__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bluetooth.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_108__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_109__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tv.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_110__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_111__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tram-front.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_112__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_113__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_114__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ship.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_115__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/anchor.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_116__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_117__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_118__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/navigation.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_119__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_120__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_121__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hospital.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_122__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_123__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_124__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tent.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_125__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_126__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/waves.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_127__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sunrise.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_128__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sunset.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Comprehensive icon mapping using Lucide React icons\nconst ICON_COMPONENTS = {\n    \"lucide:heart\": lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"lucide:star\": lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"lucide:arrow-right\": lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"lucide:arrow-left\": lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"lucide:arrow-up\": lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"lucide:arrow-down\": lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"lucide:home\": lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"lucide:user\": lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"lucide:settings\": lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    \"lucide:mail\": lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    \"lucide:phone\": lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    \"lucide:car\": lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    \"lucide:camera\": lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    \"lucide:music\": lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    \"lucide:video\": lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n    \"lucide:image\": lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n    \"lucide:file\": lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n    \"lucide:folder\": lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n    \"lucide:search\": lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n    \"lucide:plus\": lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n    \"lucide:minus\": lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n    \"lucide:x\": lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n    \"lucide:check\": lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n    \"lucide:edit\": lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n    \"lucide:trash\": lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n    \"lucide:download\": lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n    \"lucide:upload\": lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n    \"lucide:share\": lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"],\n    \"lucide:copy\": lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n    \"lucide:save\": lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n    \"lucide:print\": lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    \"lucide:calendar\": lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n    \"lucide:clock\": lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"],\n    \"lucide:map\": lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"],\n    \"lucide:globe\": lucide_react__WEBPACK_IMPORTED_MODULE_46__[\"default\"],\n    \"lucide:wifi\": lucide_react__WEBPACK_IMPORTED_MODULE_47__[\"default\"],\n    \"lucide:battery\": lucide_react__WEBPACK_IMPORTED_MODULE_48__[\"default\"],\n    \"lucide:volume\": lucide_react__WEBPACK_IMPORTED_MODULE_49__[\"default\"],\n    \"lucide:play\": lucide_react__WEBPACK_IMPORTED_MODULE_50__[\"default\"],\n    \"lucide:pause\": lucide_react__WEBPACK_IMPORTED_MODULE_51__[\"default\"],\n    \"lucide:stop\": lucide_react__WEBPACK_IMPORTED_MODULE_52__[\"default\"],\n    \"lucide:skip-forward\": lucide_react__WEBPACK_IMPORTED_MODULE_53__[\"default\"],\n    \"lucide:skip-back\": lucide_react__WEBPACK_IMPORTED_MODULE_54__[\"default\"],\n    \"lucide:refresh\": lucide_react__WEBPACK_IMPORTED_MODULE_55__[\"default\"],\n    \"lucide:maximize\": lucide_react__WEBPACK_IMPORTED_MODULE_56__[\"default\"],\n    \"lucide:minimize\": lucide_react__WEBPACK_IMPORTED_MODULE_57__[\"default\"],\n    \"lucide:eye\": lucide_react__WEBPACK_IMPORTED_MODULE_58__[\"default\"],\n    \"lucide:eye-off\": lucide_react__WEBPACK_IMPORTED_MODULE_59__[\"default\"],\n    \"lucide:lock\": lucide_react__WEBPACK_IMPORTED_MODULE_60__[\"default\"],\n    \"lucide:unlock\": lucide_react__WEBPACK_IMPORTED_MODULE_61__[\"default\"],\n    \"lucide:shield\": lucide_react__WEBPACK_IMPORTED_MODULE_62__[\"default\"],\n    \"lucide:alert\": lucide_react__WEBPACK_IMPORTED_MODULE_63__[\"default\"],\n    \"lucide:info\": lucide_react__WEBPACK_IMPORTED_MODULE_64__[\"default\"],\n    \"lucide:help\": lucide_react__WEBPACK_IMPORTED_MODULE_65__[\"default\"],\n    \"lucide:bookmark\": lucide_react__WEBPACK_IMPORTED_MODULE_66__[\"default\"],\n    \"lucide:tag\": lucide_react__WEBPACK_IMPORTED_MODULE_67__[\"default\"],\n    \"lucide:flag\": lucide_react__WEBPACK_IMPORTED_MODULE_68__[\"default\"],\n    \"lucide:thumbs-up\": lucide_react__WEBPACK_IMPORTED_MODULE_69__[\"default\"],\n    \"lucide:thumbs-down\": lucide_react__WEBPACK_IMPORTED_MODULE_70__[\"default\"],\n    \"lucide:message\": lucide_react__WEBPACK_IMPORTED_MODULE_71__[\"default\"],\n    \"lucide:send\": lucide_react__WEBPACK_IMPORTED_MODULE_72__[\"default\"],\n    \"lucide:bell\": lucide_react__WEBPACK_IMPORTED_MODULE_73__[\"default\"],\n    \"lucide:gift\": lucide_react__WEBPACK_IMPORTED_MODULE_74__[\"default\"],\n    \"lucide:shopping-cart\": lucide_react__WEBPACK_IMPORTED_MODULE_75__[\"default\"],\n    \"lucide:credit-card\": lucide_react__WEBPACK_IMPORTED_MODULE_76__[\"default\"],\n    \"lucide:dollar-sign\": lucide_react__WEBPACK_IMPORTED_MODULE_77__[\"default\"],\n    \"lucide:trending-up\": lucide_react__WEBPACK_IMPORTED_MODULE_78__[\"default\"],\n    \"lucide:trending-down\": lucide_react__WEBPACK_IMPORTED_MODULE_79__[\"default\"],\n    \"lucide:bar-chart\": lucide_react__WEBPACK_IMPORTED_MODULE_80__[\"default\"],\n    \"lucide:pie-chart\": lucide_react__WEBPACK_IMPORTED_MODULE_81__[\"default\"],\n    \"lucide:activity\": lucide_react__WEBPACK_IMPORTED_MODULE_82__[\"default\"],\n    \"lucide:zap\": lucide_react__WEBPACK_IMPORTED_MODULE_83__[\"default\"],\n    \"lucide:sun\": lucide_react__WEBPACK_IMPORTED_MODULE_84__[\"default\"],\n    \"lucide:moon\": lucide_react__WEBPACK_IMPORTED_MODULE_85__[\"default\"],\n    \"lucide:cloud\": lucide_react__WEBPACK_IMPORTED_MODULE_86__[\"default\"],\n    \"lucide:umbrella\": lucide_react__WEBPACK_IMPORTED_MODULE_87__[\"default\"],\n    \"lucide:snowflake\": lucide_react__WEBPACK_IMPORTED_MODULE_88__[\"default\"],\n    \"lucide:droplet\": lucide_react__WEBPACK_IMPORTED_MODULE_89__[\"default\"],\n    \"lucide:flame\": lucide_react__WEBPACK_IMPORTED_MODULE_90__[\"default\"],\n    \"lucide:leaf\": lucide_react__WEBPACK_IMPORTED_MODULE_91__[\"default\"],\n    \"lucide:tree\": lucide_react__WEBPACK_IMPORTED_MODULE_92__[\"default\"],\n    \"lucide:flower\": lucide_react__WEBPACK_IMPORTED_MODULE_93__[\"default\"],\n    \"lucide:coffee\": lucide_react__WEBPACK_IMPORTED_MODULE_94__[\"default\"],\n    \"lucide:pizza\": lucide_react__WEBPACK_IMPORTED_MODULE_95__[\"default\"],\n    \"lucide:utensils\": lucide_react__WEBPACK_IMPORTED_MODULE_96__[\"default\"],\n    \"lucide:wine\": lucide_react__WEBPACK_IMPORTED_MODULE_97__[\"default\"],\n    \"lucide:gamepad\": lucide_react__WEBPACK_IMPORTED_MODULE_98__[\"default\"],\n    \"lucide:headphones\": lucide_react__WEBPACK_IMPORTED_MODULE_99__[\"default\"],\n    \"lucide:smartphone\": lucide_react__WEBPACK_IMPORTED_MODULE_100__[\"default\"],\n    \"lucide:laptop\": lucide_react__WEBPACK_IMPORTED_MODULE_101__[\"default\"],\n    \"lucide:monitor\": lucide_react__WEBPACK_IMPORTED_MODULE_102__[\"default\"],\n    \"lucide:keyboard\": lucide_react__WEBPACK_IMPORTED_MODULE_103__[\"default\"],\n    \"lucide:mouse\": lucide_react__WEBPACK_IMPORTED_MODULE_104__[\"default\"],\n    \"lucide:printer\": lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    \"lucide:hard-drive\": lucide_react__WEBPACK_IMPORTED_MODULE_105__[\"default\"],\n    \"lucide:usb\": lucide_react__WEBPACK_IMPORTED_MODULE_106__[\"default\"],\n    \"lucide:bluetooth\": lucide_react__WEBPACK_IMPORTED_MODULE_107__[\"default\"],\n    \"lucide:radio\": lucide_react__WEBPACK_IMPORTED_MODULE_108__[\"default\"],\n    \"lucide:tv\": lucide_react__WEBPACK_IMPORTED_MODULE_109__[\"default\"],\n    \"lucide:plane\": lucide_react__WEBPACK_IMPORTED_MODULE_110__[\"default\"],\n    \"lucide:train\": lucide_react__WEBPACK_IMPORTED_MODULE_111__[\"default\"],\n    \"lucide:bus\": lucide_react__WEBPACK_IMPORTED_MODULE_112__[\"default\"],\n    \"lucide:bike\": lucide_react__WEBPACK_IMPORTED_MODULE_113__[\"default\"],\n    \"lucide:ship\": lucide_react__WEBPACK_IMPORTED_MODULE_114__[\"default\"],\n    \"lucide:anchor\": lucide_react__WEBPACK_IMPORTED_MODULE_115__[\"default\"],\n    \"lucide:compass\": lucide_react__WEBPACK_IMPORTED_MODULE_116__[\"default\"],\n    \"lucide:map-pin\": lucide_react__WEBPACK_IMPORTED_MODULE_117__[\"default\"],\n    \"lucide:navigation\": lucide_react__WEBPACK_IMPORTED_MODULE_118__[\"default\"],\n    \"lucide:building\": lucide_react__WEBPACK_IMPORTED_MODULE_119__[\"default\"],\n    \"lucide:school\": lucide_react__WEBPACK_IMPORTED_MODULE_120__[\"default\"],\n    \"lucide:hospital\": lucide_react__WEBPACK_IMPORTED_MODULE_121__[\"default\"],\n    \"lucide:store\": lucide_react__WEBPACK_IMPORTED_MODULE_122__[\"default\"],\n    \"lucide:factory\": lucide_react__WEBPACK_IMPORTED_MODULE_123__[\"default\"],\n    \"lucide:tent\": lucide_react__WEBPACK_IMPORTED_MODULE_124__[\"default\"],\n    \"lucide:mountain\": lucide_react__WEBPACK_IMPORTED_MODULE_125__[\"default\"],\n    \"lucide:waves\": lucide_react__WEBPACK_IMPORTED_MODULE_126__[\"default\"],\n    \"lucide:sunrise\": lucide_react__WEBPACK_IMPORTED_MODULE_127__[\"default\"],\n    \"lucide:sunset\": lucide_react__WEBPACK_IMPORTED_MODULE_128__[\"default\"]\n};\n// Helper function to get SVG paths for icons (static approach for reliability)\nconst getIconSVGPaths = (iconName)=>{\n    const iconPaths = {\n        \"lucide:heart\": '<path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"/>',\n        \"lucide:star\": '<polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"/>',\n        \"lucide:arrow-right\": '<line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"/><polyline points=\"12,5 19,12 12,19\"/>',\n        \"lucide:arrow-left\": '<line x1=\"19\" y1=\"12\" x2=\"5\" y2=\"12\"/><polyline points=\"12,19 5,12 12,5\"/>',\n        \"lucide:arrow-up\": '<line x1=\"12\" y1=\"19\" x2=\"12\" y2=\"5\"/><polyline points=\"5,12 12,5 19,12\"/>',\n        \"lucide:arrow-down\": '<line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"/><polyline points=\"19,12 12,19 5,12\"/>',\n        \"lucide:home\": '<path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/><polyline points=\"9,22 9,12 15,12 15,22\"/>',\n        \"lucide:user\": '<path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/><circle cx=\"12\" cy=\"7\" r=\"4\"/>',\n        \"lucide:settings\": '<circle cx=\"12\" cy=\"12\" r=\"3\"/><path d=\"M12 1v6m0 6v6m11-7h-6m-6 0H1\"/>',\n        \"lucide:mail\": '<path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"/><polyline points=\"22,6 12,13 2,6\"/>',\n        \"lucide:phone\": '<path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"/>',\n        \"lucide:car\": '<path d=\"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18.4 10H5.6L3.5 11.1C2.7 11.3 2 12.1 2 13v3c0 .6.4 1 1 1h2\"/><circle cx=\"7\" cy=\"17\" r=\"2\"/><path d=\"M9 17h6\"/><circle cx=\"17\" cy=\"17\" r=\"2\"/>',\n        \"lucide:camera\": '<path d=\"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z\"/><circle cx=\"12\" cy=\"13\" r=\"3\"/>',\n        \"lucide:music\": '<path d=\"M9 18V5l12-2v13\"/><circle cx=\"6\" cy=\"18\" r=\"3\"/><circle cx=\"18\" cy=\"16\" r=\"3\"/>',\n        \"lucide:video\": '<path d=\"M23 7l-7 5 7 5V7z\"/><rect x=\"1\" y=\"5\" width=\"15\" height=\"14\" rx=\"2\" ry=\"2\"/>',\n        \"lucide:image\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><circle cx=\"9\" cy=\"9\" r=\"2\"/><path d=\"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\"/>',\n        \"lucide:file\": '<path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/>',\n        \"lucide:folder\": '<path d=\"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\"/>',\n        \"lucide:search\": '<circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/>',\n        \"lucide:plus\": '<path d=\"M5 12h14\"/><path d=\"M12 5v14\"/>',\n        \"lucide:minus\": '<path d=\"M5 12h14\"/>',\n        \"lucide:x\": '<path d=\"M18 6 6 18\"/><path d=\"m6 6 12 12\"/>',\n        \"lucide:check\": '<polyline points=\"20,6 9,17 4,12\"/>',\n        \"lucide:edit\": '<path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"/><path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"/>',\n        \"lucide:trash\": '<path d=\"M3 6h18\"/><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/><path d=\"M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2\"/>',\n        \"lucide:download\": '<path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/><polyline points=\"7,10 12,15 17,10\"/><line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"/>',\n        \"lucide:upload\": '<path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/><polyline points=\"17,8 12,3 7,8\"/><line x1=\"12\" y1=\"3\" x2=\"12\" y2=\"15\"/>',\n        \"lucide:share\": '<path d=\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\"/><polyline points=\"16,6 12,2 8,6\"/><line x1=\"12\" y1=\"2\" x2=\"12\" y2=\"15\"/>',\n        \"lucide:copy\": '<rect x=\"9\" y=\"9\" width=\"13\" height=\"13\" rx=\"2\" ry=\"2\"/><path d=\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"/>',\n        \"lucide:save\": '<path d=\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\"/><polyline points=\"17,21 17,13 7,13 7,21\"/><polyline points=\"7,3 7,8 15,8\"/>',\n        \"lucide:print\": '<polyline points=\"6,9 6,2 18,2 18,9\"/><path d=\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\"/><rect x=\"6\" y=\"14\" width=\"12\" height=\"8\"/>',\n        \"lucide:calendar\": '<rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"/><line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"/><line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"/>',\n        \"lucide:clock\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><polyline points=\"12,6 12,12 16,14\"/>',\n        \"lucide:map\": '<polygon points=\"1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2\"/>',\n        \"lucide:globe\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"/><path d=\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\"/>',\n        \"lucide:wifi\": '<path d=\"M5 12.55a11 11 0 0 1 14.08 0\"/><path d=\"M1.42 9a16 16 0 0 1 21.16 0\"/><path d=\"M8.53 16.11a6 6 0 0 1 6.95 0\"/><line x1=\"12\" y1=\"20\" x2=\"12.01\" y2=\"20\"/>',\n        \"lucide:battery\": '<rect x=\"1\" y=\"6\" width=\"18\" height=\"12\" rx=\"2\" ry=\"2\"/><line x1=\"23\" y1=\"13\" x2=\"23\" y2=\"11\"/>',\n        \"lucide:volume\": '<polygon points=\"11,5 6,9 2,9 2,15 6,15 11,19\"/><path d=\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\"/>',\n        \"lucide:play\": '<polygon points=\"5,3 19,12 5,21\"/>',\n        \"lucide:pause\": '<rect x=\"6\" y=\"4\" width=\"4\" height=\"16\"/><rect x=\"14\" y=\"4\" width=\"4\" height=\"16\"/>',\n        \"lucide:stop\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>',\n        \"lucide:skip-forward\": '<polygon points=\"5,4 15,12 5,20\"/><line x1=\"19\" y1=\"5\" x2=\"19\" y2=\"19\"/>',\n        \"lucide:skip-back\": '<polygon points=\"19,20 9,12 19,4\"/><line x1=\"5\" y1=\"19\" x2=\"5\" y2=\"5\"/>',\n        \"lucide:refresh\": '<polyline points=\"23,4 23,10 17,10\"/><polyline points=\"1,20 1,14 7,14\"/><path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4-4.64 4.36A9 9 0 0 1 3.51 15\"/>'\n    };\n    return iconPaths[iconName] || '<rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2\" ry=\"2\" stroke-dasharray=\"2,2\"/>';\n};\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        addIcon: (iconName)=>{\n            if (!canvas) return;\n            // Check if the icon exists in our comprehensive library\n            const IconComponent = ICON_COMPONENTS[iconName];\n            if (!IconComponent) {\n                console.warn(\"Icon \".concat(iconName, \" not found in library\"));\n                return;\n            }\n            // Create SVG string using the proper Lucide icon\n            const iconSize = 80;\n            const iconColor = strokeColor || \"#000000\";\n            // Create a more reliable SVG generation approach\n            const svgString = \"data:image/svg+xml;charset=utf-8,\".concat(encodeURIComponent('\\n        <svg width=\"'.concat(iconSize, '\" height=\"').concat(iconSize, '\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"').concat(iconColor, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" xmlns=\"http://www.w3.org/2000/svg\">\\n          ').concat(getIconSVGPaths(iconName), \"\\n        </svg>\\n      \")));\n            // Create fabric image from the SVG data URL\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(svgString, (img)=>{\n                if (img && canvas) {\n                    img.set({\n                        left: 100,\n                        top: 100,\n                        scaleX: 1,\n                        scaleY: 1,\n                        // Allow full control over the icon\n                        lockScalingX: false,\n                        lockScalingY: false,\n                        lockUniScaling: false,\n                        lockMovementX: false,\n                        lockMovementY: false,\n                        lockRotation: false,\n                        // Ensure it's selectable and movable\n                        selectable: true,\n                        evented: true,\n                        // Add metadata for identification\n                        type: \"icon\",\n                        iconName: iconName,\n                        // Store original color for color changes\n                        originalColor: iconColor\n                    });\n                    addToCanvas(img);\n                }\n            });\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback, setCanvasIsSelected } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback,\n        setCanvasIsSelected\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});