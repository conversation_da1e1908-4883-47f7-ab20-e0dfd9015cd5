"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/color-picker.tsx":
/*!*********************************************************!*\
  !*** ./src/features/editor/components/color-picker.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorPicker: function() { return /* binding */ ColorPicker; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_color__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-color */ \"(app-pages-browser)/./node_modules/react-color/es/index.js\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n\n\n\n\nconst ColorPicker = (param)=>{\n    let { value, onChange } = param;\n    const handleColorChange = (color)=>{\n        const formattedValue = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_3__.rgbaObjectToString)(color.rgb);\n        onChange(formattedValue);\n    };\n    const handleColorChangeComplete = (color)=>{\n        const formattedValue = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_3__.rgbaObjectToString)(color.rgb);\n        onChange(formattedValue);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-4\",\n        onClick: (e)=>e.stopPropagation(),\n        onMouseDown: (e)=>e.stopPropagation(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_color__WEBPACK_IMPORTED_MODULE_1__.ChromePicker, {\n                color: value,\n                onChange: handleColorChange,\n                onChangeComplete: handleColorChangeComplete,\n                className: \"border rounded-lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\color-picker.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_color__WEBPACK_IMPORTED_MODULE_1__.CirclePicker, {\n                color: value,\n                colors: _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.colors,\n                onChange: handleColorChange,\n                onChangeComplete: handleColorChangeComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\color-picker.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\color-picker.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ColorPicker;\nvar _c;\n$RefreshReg$(_c, \"ColorPicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/color-picker.tsx\n"));

/***/ })

});