"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/toolbar.tsx":
/*!****************************************************!*\
  !*** ./src/features/editor/components/toolbar.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toolbar: function() { return /* binding */ Toolbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FaBold,FaItalic,FaStrikethrough,FaUnderline!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbColorFilter_react_icons_tb__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=TbColorFilter!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsBorderWidth_react_icons_bs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BsBorderWidth!=!react-icons/bs */ \"(app-pages-browser)/./node_modules/react-icons/bs/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RxTransparencyGrid_react_icons_rx__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=RxTransparencyGrid!=!react-icons/rx */ \"(app-pages-browser)/./node_modules/react-icons/rx/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-center.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-split-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlignCenter,AlignLeft,AlignRight,ArrowDown,ArrowUp,ChevronDown,Copy,SquareSplitHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_components_font_size_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/font-size-input */ \"(app-pages-browser)/./src/features/editor/components/font-size-input.tsx\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_hint__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/hint */ \"(app-pages-browser)/./src/components/hint.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst Toolbar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    var _editor_selectedObjects_;\n    _s();\n    const initialFillColor = editor === null || editor === void 0 ? void 0 : editor.getActiveFillColor();\n    const initialStrokeColor = editor === null || editor === void 0 ? void 0 : editor.getActiveStrokeColor();\n    const initialFontFamily = editor === null || editor === void 0 ? void 0 : editor.getActiveFontFamily();\n    const initialFontWeight = (editor === null || editor === void 0 ? void 0 : editor.getActiveFontWeight()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_4__.FONT_WEIGHT;\n    const initialFontStyle = editor === null || editor === void 0 ? void 0 : editor.getActiveFontStyle();\n    const initialFontLinethrough = editor === null || editor === void 0 ? void 0 : editor.getActiveFontLinethrough();\n    const initialFontUnderline = editor === null || editor === void 0 ? void 0 : editor.getActiveFontUnderline();\n    const initialTextAlign = editor === null || editor === void 0 ? void 0 : editor.getActiveTextAlign();\n    const initialFontSize = (editor === null || editor === void 0 ? void 0 : editor.getActiveFontSize()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_4__.FONT_SIZE;\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fillColor: initialFillColor,\n        strokeColor: initialStrokeColor,\n        fontFamily: initialFontFamily,\n        fontWeight: initialFontWeight,\n        fontStyle: initialFontStyle,\n        fontLinethrough: initialFontLinethrough,\n        fontUnderline: initialFontUnderline,\n        textAlign: initialTextAlign,\n        fontSize: initialFontSize\n    });\n    const selectedObject = editor === null || editor === void 0 ? void 0 : editor.selectedObjects[0];\n    const selectedObjectType = editor === null || editor === void 0 ? void 0 : (_editor_selectedObjects_ = editor.selectedObjects[0]) === null || _editor_selectedObjects_ === void 0 ? void 0 : _editor_selectedObjects_.type;\n    const isText = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_2__.isTextType)(selectedObjectType);\n    const isImage = selectedObjectType === \"image\";\n    const isIcon = selectedObjectType === \"icon\";\n    const onChangeFontSize = (value)=>{\n        if (!selectedObject) {\n            return;\n        }\n        editor === null || editor === void 0 ? void 0 : editor.changeFontSize(value);\n        setProperties((current)=>({\n                ...current,\n                fontSize: value\n            }));\n    };\n    const onChangeTextAlign = (value)=>{\n        if (!selectedObject) {\n            return;\n        }\n        editor === null || editor === void 0 ? void 0 : editor.changeTextAlign(value);\n        setProperties((current)=>({\n                ...current,\n                textAlign: value\n            }));\n    };\n    const toggleBold = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const newValue = properties.fontWeight > 500 ? 500 : 700;\n        editor === null || editor === void 0 ? void 0 : editor.changeFontWeight(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontWeight: newValue\n            }));\n    };\n    const toggleItalic = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const isItalic = properties.fontStyle === \"italic\";\n        const newValue = isItalic ? \"normal\" : \"italic\";\n        editor === null || editor === void 0 ? void 0 : editor.changeFontStyle(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontStyle: newValue\n            }));\n    };\n    const toggleLinethrough = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const newValue = properties.fontLinethrough ? false : true;\n        editor === null || editor === void 0 ? void 0 : editor.changeFontLinethrough(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontLinethrough: newValue\n            }));\n    };\n    const toggleUnderline = ()=>{\n        if (!selectedObject) {\n            return;\n        }\n        const newValue = properties.fontUnderline ? false : true;\n        editor === null || editor === void 0 ? void 0 : editor.changeFontUnderline(newValue);\n        setProperties((current)=>({\n                ...current,\n                fontUnderline: newValue\n            }));\n    };\n    if ((editor === null || editor === void 0 ? void 0 : editor.selectedObjects.length) === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"shrink-0 h-[56px] border-b bg-white w-full flex items-center overflow-x-auto z-[49] p-2 gap-x-2\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n            lineNumber: 160,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"shrink-0 h-[56px] border-b bg-white w-full flex items-center overflow-x-auto z-[49] p-2 gap-x-2\",\n        children: [\n            !isImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Color\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"fill\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"fill\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-sm size-4 border\",\n                            style: {\n                                backgroundColor: properties.fillColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, undefined),\n            !isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Stroke color\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"stroke-color\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"stroke-color\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-sm size-4 border-2 bg-white\",\n                            style: {\n                                borderColor: properties.strokeColor\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, undefined),\n            !isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Stroke width\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"stroke-width\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"stroke-width\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsBorderWidth_react_icons_bs__WEBPACK_IMPORTED_MODULE_8__.BsBorderWidth, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Font\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"font\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-auto px-2 text-sm\", activeTool === \"font\" && \"bg-gray-100\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[100px] truncate\",\n                                children: properties.fontFamily\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"size-4 ml-2 shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Bold\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleBold,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontWeight > 500 && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBold, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Italic\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleItalic,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontStyle === \"italic\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaItalic, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Underline\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleUnderline,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontUnderline && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaUnderline, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Strike\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: toggleLinethrough,\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.fontLinethrough && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBold_FaItalic_FaStrikethrough_FaUnderline_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaStrikethrough, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Align left\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeTextAlign(\"left\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.textAlign === \"left\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Align center\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeTextAlign(\"center\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.textAlign === \"center\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Align right\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeTextAlign(\"right\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(properties.textAlign === \"right\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, undefined),\n            isText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_font_size_input__WEBPACK_IMPORTED_MODULE_3__.FontSizeInput, {\n                    value: properties.fontSize,\n                    onChange: onChangeFontSize\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 10\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, undefined),\n            isImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Filters\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"filter\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"filter\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbColorFilter_react_icons_tb__WEBPACK_IMPORTED_MODULE_14__.TbColorFilter, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 361,\n                columnNumber: 9\n            }, undefined),\n            isImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Remove background\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"remove-bg\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"remove-bg\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Bring forward\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.bringForward(),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Send backwards\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.sendBackwards(),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Opacity\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>onChangeActiveTool(\"opacity\"),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(activeTool === \"opacity\" && \"bg-gray-100\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RxTransparencyGrid_react_icons_rx__WEBPACK_IMPORTED_MODULE_18__.RxTransparencyGrid, {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Duplicate\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>{\n                            editor === null || editor === void 0 ? void 0 : editor.onCopy();\n                            editor === null || editor === void 0 ? void 0 : editor.onPaste();\n                        },\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-full justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hint__WEBPACK_IMPORTED_MODULE_6__.Hint, {\n                    label: \"Delete\",\n                    side: \"bottom\",\n                    sideOffset: 5,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.delete(),\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: \"text-red-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlignCenter_AlignLeft_AlignRight_ArrowDown_ArrowUp_ChevronDown_Copy_SquareSplitHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"size-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\toolbar.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Toolbar, \"DmUe6JEyxArHMufs3mALpXmlU1U=\");\n_c = Toolbar;\nvar _c;\n$RefreshReg$(_c, \"Toolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/toolbar.tsx\n"));

/***/ })

});