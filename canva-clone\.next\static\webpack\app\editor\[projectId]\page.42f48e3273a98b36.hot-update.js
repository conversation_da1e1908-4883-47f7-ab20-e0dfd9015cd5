"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to get actual SVG content for icons\nconst getIconSVGContent = (iconName)=>{\n    const iconMap = {\n        \"lucide:heart\": '<path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"/>',\n        \"lucide:star\": '<polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"/>',\n        \"lucide:arrow-right\": '<line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"/><polyline points=\"12,5 19,12 12,19\"/>',\n        \"lucide:arrow-left\": '<line x1=\"19\" y1=\"12\" x2=\"5\" y2=\"12\"/><polyline points=\"12,19 5,12 12,5\"/>',\n        \"lucide:arrow-up\": '<line x1=\"12\" y1=\"19\" x2=\"12\" y2=\"5\"/><polyline points=\"5,12 12,5 19,12\"/>',\n        \"lucide:arrow-down\": '<line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"/><polyline points=\"19,12 12,19 5,12\"/>',\n        \"lucide:home\": '<path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/><polyline points=\"9,22 9,12 15,12 15,22\"/>',\n        \"lucide:user\": '<path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/><circle cx=\"12\" cy=\"7\" r=\"4\"/>',\n        \"lucide:settings\": '<circle cx=\"12\" cy=\"12\" r=\"3\"/><path d=\"M12 1v6m0 6v6m11-7h-6m-6 0H1m10-9a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H10a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2z\"/>',\n        \"lucide:mail\": '<path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"/><polyline points=\"22,6 12,13 2,6\"/>',\n        \"lucide:phone\": '<path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"/>'\n    };\n    return iconMap[iconName] || '<rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2\" ry=\"2\" stroke-dasharray=\"2,2\"/>';\n};\n// Icon SVG paths mapping for actual icon rendering - properly formatted\nconst ICON_PATHS = {\n    \"lucide:heart\": \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\",\n    \"lucide:star\": \"m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\",\n    \"lucide:arrow-right\": \"M5 12h14M12 5l7 7-7 7\",\n    \"lucide:arrow-left\": \"M19 12H5M12 19l-7-7 7-7\",\n    \"lucide:arrow-up\": \"M12 19V5M5 12l7-7 7 7\",\n    \"lucide:arrow-down\": \"M12 5v14M19 12l-7 7-7-7\",\n    \"lucide:home\": \"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z M9 22V12h6v10\",\n    \"lucide:user\": \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2 M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z\",\n    \"lucide:settings\": \"M12.22 2h-.44a2 2 0 0 0-2 2.18l.2 1.81c-.26.14-.51.3-.74.48l-1.8-.37a2 2 0 0 0-2.26 1.3l-.22.44a2 2 0 0 0 .47 2.26l1.31 1.31c-.18.23-.34.48-.48.74l-1.81.2a2 2 0 0 0-1.82 2v.44a2 2 0 0 0 1.82 2l1.81.2c.14.26.3.51.48.74l-1.31 1.31a2 2 0 0 0-.47 2.26l.22.44a2 2 0 0 0 2.26 1.3l1.8-.37c.23.18.48.34.74.48l.2 1.81a2 2 0 0 0 2 1.82h.44a2 2 0 0 0 2-1.82l.2-1.81c.26-.14.51-.3.74-.48l1.8.37a2 2 0 0 0 2.26-1.3l.22-.44a2 2 0 0 0-.47-2.26l-1.31-1.31c.18-.23.34-.48.48-.74l1.81-.2a2 2 0 0 0 1.82-2v-.44a2 2 0 0 0-1.82-2l-1.81-.2c-.14-.26-.3-.51-.48-.74l1.31-1.31a2 2 0 0 0 .47-2.26l-.22-.44a2 2 0 0 0-2.26-1.3l-1.8.37c-.23-.18-.48-.34-.74-.48l-.2-1.81a2 2 0 0 0-2-1.82Z M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z\",\n    \"lucide:mail\": \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z m0 4 8 5 8-5\",\n    \"lucide:phone\": \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n    \"lucide:calendar\": \"M8 2v4m8-4v4M3 10h18M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z\",\n    \"lucide:clock\": \"M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z m5 10-5 3V7\",\n    \"lucide:camera\": \"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z M12 17a3 3 0 1 1 0-6 3 3 0 0 1 0 6z\",\n    \"lucide:music\": \"M9 18V5l12-2v13M9 13a3 3 0 1 0 0 6 3 3 0 0 0 0-6z m12-2a3 3 0 1 0 0 6 3 3 0 0 0 0-6z\",\n    \"lucide:play\": \"m9 18 6-6-6-6v12z\",\n    \"lucide:pause\": \"M6 4h4v16H6z m8 0h4v16h-4z\",\n    \"lucide:volume-2\": \"M11 5 6 9H2v6h4l5 4V5z m8.54 8.54a8 8 0 0 0 0-11.32M15.54 8.46a4 4 0 0 1 0 5.66\",\n    \"lucide:wifi\": \"m1 9 2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9z m4 4 2 2a7.07 7.07 0 0 1 10 0l2-2a10.94 10.94 0 0 0-14 0z m4 4 2 2 2-2a3.53 3.53 0 0 0-4 0z\",\n    \"lucide:battery\": \"M15 7h1a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h7z m7 4v2\",\n    \"lucide:bluetooth\": \"m7 7 10 5-5 5V2l5 5L7 17\",\n    \"lucide:download\": \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3\",\n    \"lucide:upload\": \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12\",\n    \"lucide:search\": \"m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z\",\n    \"lucide:plus\": \"M12 5v14m-7-7h14\",\n    \"lucide:minus\": \"M5 12h14\",\n    \"lucide:check\": \"m9 12 2 2 4-4\",\n    \"lucide:x\": \"m18 6-12 12M6 6l12 12\"\n};\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        addIcon: (iconName)=>{\n            // Create SVG string with proper icon content\n            const svgContent = getIconSVGContent(iconName);\n            const svgString = \"data:image/svg+xml;charset=utf-8,\".concat(encodeURIComponent('\\n        <svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"'.concat(strokeColor || \"#000000\", '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" xmlns=\"http://www.w3.org/2000/svg\">\\n          ').concat(svgContent, \"\\n        </svg>\\n      \")));\n            // Create fabric image from the SVG data URL\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(svgString, (img)=>{\n                if (img && canvas) {\n                    img.set({\n                        left: 100,\n                        top: 100,\n                        scaleX: 1,\n                        scaleY: 1,\n                        // Lock the icon so it can't be resized\n                        lockScalingX: true,\n                        lockScalingY: true,\n                        lockUniScaling: true,\n                        // Keep other transformations available\n                        lockMovementX: false,\n                        lockMovementY: false,\n                        lockRotation: false,\n                        // Ensure it's selectable and movable\n                        selectable: true,\n                        evented: true\n                    });\n                    addToCanvas(img);\n                }\n            });\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback, setCanvasIsSelected } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback,\n        setCanvasIsSelected\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});