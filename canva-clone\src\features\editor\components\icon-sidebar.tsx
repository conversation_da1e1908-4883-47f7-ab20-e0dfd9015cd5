import { useEffect, useState } from "react";

import { 
  ActiveTool, 
  Editor, 
} from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";

import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

interface IconSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

export const IconSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: IconSidebarProps) => {
  const selectedObject = editor?.selectedObjects[0];
  const initialSize = editor?.getActiveIconSize() || { width: 80, height: 80 };
  
  const [width, setWidth] = useState(initialSize.width.toString());
  const [height, setHeight] = useState(initialSize.height.toString());
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);

  useEffect(() => {
    if (selectedObject && selectedObject.type === 'icon') {
      const size = editor?.getActiveIconSize() || { width: 80, height: 80 };
      setWidth(size.width.toString());
      setHeight(size.height.toString());
    }
  }, [selectedObject, editor]);

  const onClose = () => {
    onChangeActiveTool("select");
  };

  const handleWidthChange = (value: string) => {
    setWidth(value);
    if (maintainAspectRatio && value) {
      const aspectRatio = initialSize.height / initialSize.width;
      const newHeight = Math.round(parseInt(value) * aspectRatio);
      setHeight(newHeight.toString());
    }
  };

  const handleHeightChange = (value: string) => {
    setHeight(value);
    if (maintainAspectRatio && value) {
      const aspectRatio = initialSize.width / initialSize.height;
      const newWidth = Math.round(parseInt(value) * aspectRatio);
      setWidth(newWidth.toString());
    }
  };

  const applySize = () => {
    const widthValue = parseInt(width) || 80;
    const heightValue = parseInt(height) || 80;
    editor?.changeIconSize(widthValue, heightValue);
  };

  const resetSize = () => {
    setWidth("80");
    setHeight("80");
    editor?.changeIconSize(80, 80);
  };

  // Preset sizes
  const presetSizes = [
    { label: "Small", width: 40, height: 40 },
    { label: "Medium", width: 80, height: 80 },
    { label: "Large", width: 120, height: 120 },
    { label: "Extra Large", width: 160, height: 160 },
  ];

  const applyPresetSize = (presetWidth: number, presetHeight: number) => {
    setWidth(presetWidth.toString());
    setHeight(presetHeight.toString());
    editor?.changeIconSize(presetWidth, presetHeight);
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "icon-settings" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="Icon Settings"
        description="Adjust icon size and properties"
      />
      <ScrollArea>
        <div className="p-4 space-y-6">
          {/* Size Controls */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Size</Label>
            
            {/* Preset Sizes */}
            <div className="grid grid-cols-2 gap-2">
              {presetSizes.map((preset) => (
                <Button
                  key={preset.label}
                  onClick={() => applyPresetSize(preset.width, preset.height)}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  {preset.label}
                  <span className="ml-1 text-gray-500">
                    {preset.width}×{preset.height}
                  </span>
                </Button>
              ))}
            </div>

            {/* Custom Size Inputs */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="flex-1">
                  <Label className="text-xs text-gray-600">Width</Label>
                  <Input
                    type="number"
                    value={width}
                    onChange={(e) => handleWidthChange(e.target.value)}
                    placeholder="Width"
                    min="10"
                    max="500"
                  />
                </div>
                <div className="flex-1">
                  <Label className="text-xs text-gray-600">Height</Label>
                  <Input
                    type="number"
                    value={height}
                    onChange={(e) => handleHeightChange(e.target.value)}
                    placeholder="Height"
                    min="10"
                    max="500"
                  />
                </div>
              </div>

              {/* Aspect Ratio Toggle */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="aspect-ratio"
                  checked={maintainAspectRatio}
                  onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="aspect-ratio" className="text-xs text-gray-600">
                  Maintain aspect ratio
                </Label>
              </div>

              {/* Apply and Reset Buttons */}
              <div className="flex space-x-2">
                <Button
                  onClick={applySize}
                  size="sm"
                  className="flex-1"
                >
                  Apply Size
                </Button>
                <Button
                  onClick={resetSize}
                  variant="outline"
                  size="sm"
                  className="flex-1"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-4 border-t pt-4">
            <Label className="text-sm font-medium">Quick Actions</Label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => {
                  const currentSize = editor?.getActiveIconSize() || { width: 80, height: 80 };
                  const newWidth = Math.round(currentSize.width * 1.5);
                  const newHeight = Math.round(currentSize.height * 1.5);
                  setWidth(newWidth.toString());
                  setHeight(newHeight.toString());
                  editor?.changeIconSize(newWidth, newHeight);
                }}
                variant="outline"
                size="sm"
              >
                Scale Up 1.5×
              </Button>
              <Button
                onClick={() => {
                  const currentSize = editor?.getActiveIconSize() || { width: 80, height: 80 };
                  const newWidth = Math.round(currentSize.width * 0.75);
                  const newHeight = Math.round(currentSize.height * 0.75);
                  setWidth(newWidth.toString());
                  setHeight(newHeight.toString());
                  editor?.changeIconSize(newWidth, newHeight);
                }}
                variant="outline"
                size="sm"
              >
                Scale Down 0.75×
              </Button>
            </div>
          </div>
        </div>
      </ScrollArea>
      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
