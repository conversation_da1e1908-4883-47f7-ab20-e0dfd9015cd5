"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx":
/*!**********************************************************!*\
  !*** ./src/features/editor/components/shape-sidebar.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShapeSidebar: function() { return /* binding */ ShapeSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IoTriangle!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaDiamond!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaCircle,FaSquare,FaSquareFull!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _iconify_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @iconify/react */ \"(app-pages-browser)/./node_modules/@iconify/react/dist/iconify.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/shape-tool */ \"(app-pages-browser)/./src/features/editor/components/shape-tool.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Define available icons from Iconify\nconst iconifyElements = [\n    // Popular/Featured icons (shown by default)\n    {\n        name: \"Heart\",\n        icon: \"lucide:heart\",\n        category: \"shapes\",\n        featured: true\n    },\n    {\n        name: \"Star\",\n        icon: \"lucide:star\",\n        category: \"shapes\",\n        featured: true\n    },\n    {\n        name: \"Arrow Right\",\n        icon: \"lucide:arrow-right\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Left\",\n        icon: \"lucide:arrow-left\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Up\",\n        icon: \"lucide:arrow-up\",\n        category: \"arrows\",\n        featured: true\n    },\n    {\n        name: \"Arrow Down\",\n        icon: \"lucide:arrow-down\",\n        category: \"arrows\",\n        featured: true\n    },\n    // Additional arrows\n    {\n        name: \"Arrow Up Right\",\n        icon: \"lucide:arrow-up-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Up Left\",\n        icon: \"lucide:arrow-up-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down Right\",\n        icon: \"lucide:arrow-down-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Arrow Down Left\",\n        icon: \"lucide:arrow-down-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Right\",\n        icon: \"lucide:chevron-right\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Left\",\n        icon: \"lucide:chevron-left\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Up\",\n        icon: \"lucide:chevron-up\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Chevron Down\",\n        icon: \"lucide:chevron-down\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Move\",\n        icon: \"lucide:move\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Rotate CW\",\n        icon: \"lucide:rotate-cw\",\n        category: \"arrows\"\n    },\n    {\n        name: \"Rotate CCW\",\n        icon: \"lucide:rotate-ccw\",\n        category: \"arrows\"\n    },\n    // Interface icons\n    {\n        name: \"Home\",\n        icon: \"lucide:home\",\n        category: \"interface\"\n    },\n    {\n        name: \"User\",\n        icon: \"lucide:user\",\n        category: \"interface\"\n    },\n    {\n        name: \"Users\",\n        icon: \"lucide:users\",\n        category: \"interface\"\n    },\n    {\n        name: \"Settings\",\n        icon: \"lucide:settings\",\n        category: \"interface\"\n    },\n    {\n        name: \"Menu\",\n        icon: \"lucide:menu\",\n        category: \"interface\"\n    },\n    {\n        name: \"More Horizontal\",\n        icon: \"lucide:more-horizontal\",\n        category: \"interface\"\n    },\n    {\n        name: \"More Vertical\",\n        icon: \"lucide:more-vertical\",\n        category: \"interface\"\n    },\n    {\n        name: \"Grid\",\n        icon: \"lucide:grid-3x3\",\n        category: \"interface\"\n    },\n    {\n        name: \"List\",\n        icon: \"lucide:list\",\n        category: \"interface\"\n    },\n    {\n        name: \"Layout\",\n        icon: \"lucide:layout\",\n        category: \"interface\"\n    },\n    {\n        name: \"Sidebar\",\n        icon: \"lucide:sidebar\",\n        category: \"interface\"\n    },\n    {\n        name: \"Panel Left\",\n        icon: \"lucide:panel-left\",\n        category: \"interface\"\n    },\n    {\n        name: \"Panel Right\",\n        icon: \"lucide:panel-right\",\n        category: \"interface\"\n    },\n    // Communication\n    {\n        name: \"Mail\",\n        icon: \"lucide:mail\",\n        category: \"communication\"\n    },\n    {\n        name: \"Message Circle\",\n        icon: \"lucide:message-circle\",\n        category: \"communication\"\n    },\n    {\n        name: \"Message Square\",\n        icon: \"lucide:message-square\",\n        category: \"communication\"\n    },\n    {\n        name: \"Phone\",\n        icon: \"lucide:phone\",\n        category: \"communication\"\n    },\n    {\n        name: \"Phone Call\",\n        icon: \"lucide:phone-call\",\n        category: \"communication\"\n    },\n    {\n        name: \"Video\",\n        icon: \"lucide:video\",\n        category: \"communication\"\n    },\n    {\n        name: \"Send\",\n        icon: \"lucide:send\",\n        category: \"communication\"\n    },\n    {\n        name: \"Share\",\n        icon: \"lucide:share\",\n        category: \"communication\"\n    },\n    {\n        name: \"Share 2\",\n        icon: \"lucide:share-2\",\n        category: \"communication\"\n    },\n    // Time & Calendar\n    {\n        name: \"Calendar\",\n        icon: \"lucide:calendar\",\n        category: \"time\"\n    },\n    {\n        name: \"Calendar Days\",\n        icon: \"lucide:calendar-days\",\n        category: \"time\"\n    },\n    {\n        name: \"Clock\",\n        icon: \"lucide:clock\",\n        category: \"time\"\n    },\n    {\n        name: \"Timer\",\n        icon: \"lucide:timer\",\n        category: \"time\"\n    },\n    {\n        name: \"Alarm Clock\",\n        icon: \"lucide:alarm-clock\",\n        category: \"time\"\n    },\n    {\n        name: \"Hourglass\",\n        icon: \"lucide:hourglass\",\n        category: \"time\"\n    },\n    // Media & Entertainment\n    {\n        name: \"Camera\",\n        icon: \"lucide:camera\",\n        category: \"media\"\n    },\n    {\n        name: \"Image\",\n        icon: \"lucide:image\",\n        category: \"media\"\n    },\n    {\n        name: \"Images\",\n        icon: \"lucide:images\",\n        category: \"media\"\n    },\n    {\n        name: \"Video Camera\",\n        icon: \"lucide:video\",\n        category: \"media\"\n    },\n    {\n        name: \"Music\",\n        icon: \"lucide:music\",\n        category: \"media\"\n    },\n    {\n        name: \"Play\",\n        icon: \"lucide:play\",\n        category: \"media\"\n    },\n    {\n        name: \"Pause\",\n        icon: \"lucide:pause\",\n        category: \"media\"\n    },\n    {\n        name: \"Stop\",\n        icon: \"lucide:square\",\n        category: \"media\"\n    },\n    {\n        name: \"Skip Forward\",\n        icon: \"lucide:skip-forward\",\n        category: \"media\"\n    },\n    {\n        name: \"Skip Back\",\n        icon: \"lucide:skip-back\",\n        category: \"media\"\n    },\n    {\n        name: \"Fast Forward\",\n        icon: \"lucide:fast-forward\",\n        category: \"media\"\n    },\n    {\n        name: \"Rewind\",\n        icon: \"lucide:rewind\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume\",\n        icon: \"lucide:volume-2\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume Off\",\n        icon: \"lucide:volume-x\",\n        category: \"media\"\n    },\n    {\n        name: \"Volume Low\",\n        icon: \"lucide:volume-1\",\n        category: \"media\"\n    },\n    {\n        name: \"Headphones\",\n        icon: \"lucide:headphones\",\n        category: \"media\"\n    },\n    {\n        name: \"Mic\",\n        icon: \"lucide:mic\",\n        category: \"media\"\n    },\n    {\n        name: \"Mic Off\",\n        icon: \"lucide:mic-off\",\n        category: \"media\"\n    },\n    // Technology\n    {\n        name: \"Wifi\",\n        icon: \"lucide:wifi\",\n        category: \"tech\"\n    },\n    {\n        name: \"Wifi Off\",\n        icon: \"lucide:wifi-off\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery\",\n        icon: \"lucide:battery\",\n        category: \"tech\"\n    },\n    {\n        name: \"Battery Low\",\n        icon: \"lucide:battery-low\",\n        category: \"tech\"\n    },\n    {\n        name: \"Bluetooth\",\n        icon: \"lucide:bluetooth\",\n        category: \"tech\"\n    },\n    {\n        name: \"Smartphone\",\n        icon: \"lucide:smartphone\",\n        category: \"tech\"\n    },\n    {\n        name: \"Laptop\",\n        icon: \"lucide:laptop\",\n        category: \"tech\"\n    },\n    {\n        name: \"Monitor\",\n        icon: \"lucide:monitor\",\n        category: \"tech\"\n    },\n    {\n        name: \"Tablet\",\n        icon: \"lucide:tablet\",\n        category: \"tech\"\n    },\n    {\n        name: \"Hard Drive\",\n        icon: \"lucide:hard-drive\",\n        category: \"tech\"\n    },\n    {\n        name: \"Server\",\n        icon: \"lucide:server\",\n        category: \"tech\"\n    },\n    {\n        name: \"Database\",\n        icon: \"lucide:database\",\n        category: \"tech\"\n    },\n    {\n        name: \"Cloud\",\n        icon: \"lucide:cloud\",\n        category: \"tech\"\n    },\n    {\n        name: \"Globe\",\n        icon: \"lucide:globe\",\n        category: \"tech\"\n    },\n    // Actions & Controls\n    {\n        name: \"Download\",\n        icon: \"lucide:download\",\n        category: \"actions\"\n    },\n    {\n        name: \"Upload\",\n        icon: \"lucide:upload\",\n        category: \"actions\"\n    },\n    {\n        name: \"Search\",\n        icon: \"lucide:search\",\n        category: \"actions\"\n    },\n    {\n        name: \"Plus\",\n        icon: \"lucide:plus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Minus\",\n        icon: \"lucide:minus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Check\",\n        icon: \"lucide:check\",\n        category: \"actions\"\n    },\n    {\n        name: \"X\",\n        icon: \"lucide:x\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit\",\n        icon: \"lucide:edit\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit 2\",\n        icon: \"lucide:edit-2\",\n        category: \"actions\"\n    },\n    {\n        name: \"Edit 3\",\n        icon: \"lucide:edit-3\",\n        category: \"actions\"\n    },\n    {\n        name: \"Trash\",\n        icon: \"lucide:trash\",\n        category: \"actions\"\n    },\n    {\n        name: \"Trash 2\",\n        icon: \"lucide:trash-2\",\n        category: \"actions\"\n    },\n    {\n        name: \"Copy\",\n        icon: \"lucide:copy\",\n        category: \"actions\"\n    },\n    {\n        name: \"Cut\",\n        icon: \"lucide:scissors\",\n        category: \"actions\"\n    },\n    {\n        name: \"Paste\",\n        icon: \"lucide:clipboard\",\n        category: \"actions\"\n    },\n    {\n        name: \"Save\",\n        icon: \"lucide:save\",\n        category: \"actions\"\n    },\n    {\n        name: \"Undo\",\n        icon: \"lucide:undo\",\n        category: \"actions\"\n    },\n    {\n        name: \"Redo\",\n        icon: \"lucide:redo\",\n        category: \"actions\"\n    },\n    {\n        name: \"Refresh\",\n        icon: \"lucide:refresh-cw\",\n        category: \"actions\"\n    },\n    {\n        name: \"Power\",\n        icon: \"lucide:power\",\n        category: \"actions\"\n    },\n    {\n        name: \"Lock\",\n        icon: \"lucide:lock\",\n        category: \"actions\"\n    },\n    {\n        name: \"Unlock\",\n        icon: \"lucide:unlock\",\n        category: \"actions\"\n    },\n    {\n        name: \"Eye\",\n        icon: \"lucide:eye\",\n        category: \"actions\"\n    },\n    {\n        name: \"Eye Off\",\n        icon: \"lucide:eye-off\",\n        category: \"actions\"\n    },\n    // Shapes & Symbols\n    {\n        name: \"Circle\",\n        icon: \"lucide:circle\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Square\",\n        icon: \"lucide:square\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Triangle\",\n        icon: \"lucide:triangle\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Hexagon\",\n        icon: \"lucide:hexagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Octagon\",\n        icon: \"lucide:octagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Diamond\",\n        icon: \"lucide:diamond\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Pentagon\",\n        icon: \"lucide:pentagon\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Bookmark\",\n        icon: \"lucide:bookmark\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Tag\",\n        icon: \"lucide:tag\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Flag\",\n        icon: \"lucide:flag\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Shield\",\n        icon: \"lucide:shield\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Award\",\n        icon: \"lucide:award\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Medal\",\n        icon: \"lucide:medal\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Crown\",\n        icon: \"lucide:crown\",\n        category: \"shapes\"\n    },\n    {\n        name: \"Gem\",\n        icon: \"lucide:gem\",\n        category: \"shapes\"\n    },\n    // Weather & Nature\n    {\n        name: \"Sun\",\n        icon: \"lucide:sun\",\n        category: \"weather\"\n    },\n    {\n        name: \"Moon\",\n        icon: \"lucide:moon\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud\",\n        icon: \"lucide:cloud\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud Rain\",\n        icon: \"lucide:cloud-rain\",\n        category: \"weather\"\n    },\n    {\n        name: \"Cloud Snow\",\n        icon: \"lucide:cloud-snow\",\n        category: \"weather\"\n    },\n    {\n        name: \"Lightning\",\n        icon: \"lucide:zap\",\n        category: \"weather\"\n    },\n    {\n        name: \"Umbrella\",\n        icon: \"lucide:umbrella\",\n        category: \"weather\"\n    },\n    {\n        name: \"Thermometer\",\n        icon: \"lucide:thermometer\",\n        category: \"weather\"\n    },\n    {\n        name: \"Wind\",\n        icon: \"lucide:wind\",\n        category: \"weather\"\n    },\n    {\n        name: \"Tree\",\n        icon: \"lucide:tree-pine\",\n        category: \"nature\"\n    },\n    {\n        name: \"Leaf\",\n        icon: \"lucide:leaf\",\n        category: \"nature\"\n    },\n    {\n        name: \"Flower\",\n        icon: \"lucide:flower\",\n        category: \"nature\"\n    },\n    {\n        name: \"Sprout\",\n        icon: \"lucide:sprout\",\n        category: \"nature\"\n    },\n    // Transportation\n    {\n        name: \"Car\",\n        icon: \"lucide:car\",\n        category: \"transport\"\n    },\n    {\n        name: \"Truck\",\n        icon: \"lucide:truck\",\n        category: \"transport\"\n    },\n    {\n        name: \"Bus\",\n        icon: \"lucide:bus\",\n        category: \"transport\"\n    },\n    {\n        name: \"Bike\",\n        icon: \"lucide:bike\",\n        category: \"transport\"\n    },\n    {\n        name: \"Plane\",\n        icon: \"lucide:plane\",\n        category: \"transport\"\n    },\n    {\n        name: \"Train\",\n        icon: \"lucide:train\",\n        category: \"transport\"\n    },\n    {\n        name: \"Ship\",\n        icon: \"lucide:ship\",\n        category: \"transport\"\n    },\n    {\n        name: \"Fuel\",\n        icon: \"lucide:fuel\",\n        category: \"transport\"\n    },\n    {\n        name: \"Map Pin\",\n        icon: \"lucide:map-pin\",\n        category: \"transport\"\n    },\n    {\n        name: \"Navigation\",\n        icon: \"lucide:navigation\",\n        category: \"transport\"\n    },\n    {\n        name: \"Compass\",\n        icon: \"lucide:compass\",\n        category: \"transport\"\n    },\n    // Food & Drink\n    {\n        name: \"Coffee\",\n        icon: \"lucide:coffee\",\n        category: \"food\"\n    },\n    {\n        name: \"Cup\",\n        icon: \"lucide:cup-soda\",\n        category: \"food\"\n    },\n    {\n        name: \"Wine\",\n        icon: \"lucide:wine\",\n        category: \"food\"\n    },\n    {\n        name: \"Beer\",\n        icon: \"lucide:beer\",\n        category: \"food\"\n    },\n    {\n        name: \"Pizza\",\n        icon: \"lucide:pizza\",\n        category: \"food\"\n    },\n    {\n        name: \"Apple\",\n        icon: \"lucide:apple\",\n        category: \"food\"\n    },\n    {\n        name: \"Cherry\",\n        icon: \"lucide:cherry\",\n        category: \"food\"\n    },\n    {\n        name: \"Cake\",\n        icon: \"lucide:cake\",\n        category: \"food\"\n    },\n    {\n        name: \"Ice Cream\",\n        icon: \"lucide:ice-cream\",\n        category: \"food\"\n    },\n    // Business & Finance\n    {\n        name: \"Briefcase\",\n        icon: \"lucide:briefcase\",\n        category: \"business\"\n    },\n    {\n        name: \"Building\",\n        icon: \"lucide:building\",\n        category: \"business\"\n    },\n    {\n        name: \"Building 2\",\n        icon: \"lucide:building-2\",\n        category: \"business\"\n    },\n    {\n        name: \"Store\",\n        icon: \"lucide:store\",\n        category: \"business\"\n    },\n    {\n        name: \"Factory\",\n        icon: \"lucide:factory\",\n        category: \"business\"\n    },\n    {\n        name: \"Banknote\",\n        icon: \"lucide:banknote\",\n        category: \"finance\"\n    },\n    {\n        name: \"Credit Card\",\n        icon: \"lucide:credit-card\",\n        category: \"finance\"\n    },\n    {\n        name: \"Wallet\",\n        icon: \"lucide:wallet\",\n        category: \"finance\"\n    },\n    {\n        name: \"Coins\",\n        icon: \"lucide:coins\",\n        category: \"finance\"\n    },\n    {\n        name: \"Dollar Sign\",\n        icon: \"lucide:dollar-sign\",\n        category: \"finance\"\n    },\n    {\n        name: \"Trending Up\",\n        icon: \"lucide:trending-up\",\n        category: \"finance\"\n    },\n    {\n        name: \"Trending Down\",\n        icon: \"lucide:trending-down\",\n        category: \"finance\"\n    },\n    {\n        name: \"Bar Chart\",\n        icon: \"lucide:bar-chart\",\n        category: \"finance\"\n    },\n    {\n        name: \"Line Chart\",\n        icon: \"lucide:line-chart\",\n        category: \"finance\"\n    },\n    {\n        name: \"Pie Chart\",\n        icon: \"lucide:pie-chart\",\n        category: \"finance\"\n    },\n    // Health & Medical\n    {\n        name: \"Heart Pulse\",\n        icon: \"lucide:heart-pulse\",\n        category: \"health\"\n    },\n    {\n        name: \"Activity\",\n        icon: \"lucide:activity\",\n        category: \"health\"\n    },\n    {\n        name: \"Pill\",\n        icon: \"lucide:pill\",\n        category: \"health\"\n    },\n    {\n        name: \"Stethoscope\",\n        icon: \"lucide:stethoscope\",\n        category: \"health\"\n    },\n    {\n        name: \"Cross\",\n        icon: \"lucide:cross\",\n        category: \"health\"\n    },\n    {\n        name: \"Band Aid\",\n        icon: \"lucide:bandage\",\n        category: \"health\"\n    },\n    // Sports & Games\n    {\n        name: \"Trophy\",\n        icon: \"lucide:trophy\",\n        category: \"sports\"\n    },\n    {\n        name: \"Target\",\n        icon: \"lucide:target\",\n        category: \"sports\"\n    },\n    {\n        name: \"Dumbbell\",\n        icon: \"lucide:dumbbell\",\n        category: \"sports\"\n    },\n    {\n        name: \"Football\",\n        icon: \"lucide:football\",\n        category: \"sports\"\n    },\n    {\n        name: \"Gamepad\",\n        icon: \"lucide:gamepad-2\",\n        category: \"games\"\n    },\n    {\n        name: \"Dice\",\n        icon: \"lucide:dice-1\",\n        category: \"games\"\n    },\n    {\n        name: \"Puzzle\",\n        icon: \"lucide:puzzle\",\n        category: \"games\"\n    },\n    // Tools & Utilities\n    {\n        name: \"Wrench\",\n        icon: \"lucide:wrench\",\n        category: \"tools\"\n    },\n    {\n        name: \"Hammer\",\n        icon: \"lucide:hammer\",\n        category: \"tools\"\n    },\n    {\n        name: \"Screwdriver\",\n        icon: \"lucide:screwdriver\",\n        category: \"tools\"\n    },\n    {\n        name: \"Paintbrush\",\n        icon: \"lucide:paintbrush\",\n        category: \"tools\"\n    },\n    {\n        name: \"Palette\",\n        icon: \"lucide:palette\",\n        category: \"tools\"\n    },\n    {\n        name: \"Ruler\",\n        icon: \"lucide:ruler\",\n        category: \"tools\"\n    },\n    {\n        name: \"Calculator\",\n        icon: \"lucide:calculator\",\n        category: \"tools\"\n    },\n    {\n        name: \"Flashlight\",\n        icon: \"lucide:flashlight\",\n        category: \"tools\"\n    },\n    {\n        name: \"Key\",\n        icon: \"lucide:key\",\n        category: \"tools\"\n    },\n    {\n        name: \"Magnet\",\n        icon: \"lucide:magnet\",\n        category: \"tools\"\n    },\n    // Education & Learning\n    {\n        name: \"Book\",\n        icon: \"lucide:book\",\n        category: \"education\"\n    },\n    {\n        name: \"Book Open\",\n        icon: \"lucide:book-open\",\n        category: \"education\"\n    },\n    {\n        name: \"Bookmark\",\n        icon: \"lucide:bookmark\",\n        category: \"education\"\n    },\n    {\n        name: \"Graduation Cap\",\n        icon: \"lucide:graduation-cap\",\n        category: \"education\"\n    },\n    {\n        name: \"Pencil\",\n        icon: \"lucide:pencil\",\n        category: \"education\"\n    },\n    {\n        name: \"Pen\",\n        icon: \"lucide:pen-tool\",\n        category: \"education\"\n    },\n    {\n        name: \"Eraser\",\n        icon: \"lucide:eraser\",\n        category: \"education\"\n    },\n    {\n        name: \"Highlighter\",\n        icon: \"lucide:highlighter\",\n        category: \"education\"\n    },\n    {\n        name: \"Notebook\",\n        icon: \"lucide:notebook\",\n        category: \"education\"\n    },\n    {\n        name: \"Library\",\n        icon: \"lucide:library\",\n        category: \"education\"\n    },\n    {\n        name: \"School\",\n        icon: \"lucide:school\",\n        category: \"education\"\n    },\n    {\n        name: \"Backpack\",\n        icon: \"lucide:backpack\",\n        category: \"education\"\n    },\n    // Social & Communication\n    {\n        name: \"Users 2\",\n        icon: \"lucide:users-2\",\n        category: \"social\"\n    },\n    {\n        name: \"User Plus\",\n        icon: \"lucide:user-plus\",\n        category: \"social\"\n    },\n    {\n        name: \"User Minus\",\n        icon: \"lucide:user-minus\",\n        category: \"social\"\n    },\n    {\n        name: \"User Check\",\n        icon: \"lucide:user-check\",\n        category: \"social\"\n    },\n    {\n        name: \"User X\",\n        icon: \"lucide:user-x\",\n        category: \"social\"\n    },\n    {\n        name: \"Team\",\n        icon: \"lucide:users\",\n        category: \"social\"\n    },\n    {\n        name: \"Handshake\",\n        icon: \"lucide:handshake\",\n        category: \"social\"\n    },\n    {\n        name: \"Thumbs Up\",\n        icon: \"lucide:thumbs-up\",\n        category: \"social\"\n    },\n    {\n        name: \"Thumbs Down\",\n        icon: \"lucide:thumbs-down\",\n        category: \"social\"\n    },\n    {\n        name: \"Smile\",\n        icon: \"lucide:smile\",\n        category: \"social\"\n    },\n    {\n        name: \"Frown\",\n        icon: \"lucide:frown\",\n        category: \"social\"\n    },\n    {\n        name: \"Meh\",\n        icon: \"lucide:meh\",\n        category: \"social\"\n    },\n    {\n        name: \"Laugh\",\n        icon: \"lucide:laugh\",\n        category: \"social\"\n    },\n    // Security & Privacy\n    {\n        name: \"Shield Check\",\n        icon: \"lucide:shield-check\",\n        category: \"security\"\n    },\n    {\n        name: \"Shield Alert\",\n        icon: \"lucide:shield-alert\",\n        category: \"security\"\n    },\n    {\n        name: \"Shield X\",\n        icon: \"lucide:shield-x\",\n        category: \"security\"\n    },\n    {\n        name: \"Lock Open\",\n        icon: \"lucide:lock-open\",\n        category: \"security\"\n    },\n    {\n        name: \"Key Round\",\n        icon: \"lucide:key-round\",\n        category: \"security\"\n    },\n    {\n        name: \"Fingerprint\",\n        icon: \"lucide:fingerprint\",\n        category: \"security\"\n    },\n    {\n        name: \"Scan\",\n        icon: \"lucide:scan\",\n        category: \"security\"\n    },\n    {\n        name: \"Scan Line\",\n        icon: \"lucide:scan-line\",\n        category: \"security\"\n    },\n    // Files & Documents\n    {\n        name: \"File\",\n        icon: \"lucide:file\",\n        category: \"files\"\n    },\n    {\n        name: \"File Text\",\n        icon: \"lucide:file-text\",\n        category: \"files\"\n    },\n    {\n        name: \"File Image\",\n        icon: \"lucide:file-image\",\n        category: \"files\"\n    },\n    {\n        name: \"File Video\",\n        icon: \"lucide:file-video\",\n        category: \"files\"\n    },\n    {\n        name: \"File Audio\",\n        icon: \"lucide:file-audio\",\n        category: \"files\"\n    },\n    {\n        name: \"File Code\",\n        icon: \"lucide:file-code\",\n        category: \"files\"\n    },\n    {\n        name: \"File Spreadsheet\",\n        icon: \"lucide:file-spreadsheet\",\n        category: \"files\"\n    },\n    {\n        name: \"Folder\",\n        icon: \"lucide:folder\",\n        category: \"files\"\n    },\n    {\n        name: \"Folder Open\",\n        icon: \"lucide:folder-open\",\n        category: \"files\"\n    },\n    {\n        name: \"Folder Plus\",\n        icon: \"lucide:folder-plus\",\n        category: \"files\"\n    },\n    {\n        name: \"Archive\",\n        icon: \"lucide:archive\",\n        category: \"files\"\n    },\n    {\n        name: \"Package\",\n        icon: \"lucide:package\",\n        category: \"files\"\n    },\n    // Maps & Location\n    {\n        name: \"Map\",\n        icon: \"lucide:map\",\n        category: \"location\"\n    },\n    {\n        name: \"Map Pin\",\n        icon: \"lucide:map-pin\",\n        category: \"location\"\n    },\n    {\n        name: \"Navigation 2\",\n        icon: \"lucide:navigation-2\",\n        category: \"location\"\n    },\n    {\n        name: \"Route\",\n        icon: \"lucide:route\",\n        category: \"location\"\n    },\n    {\n        name: \"Signpost\",\n        icon: \"lucide:signpost\",\n        category: \"location\"\n    },\n    {\n        name: \"Milestone\",\n        icon: \"lucide:milestone\",\n        category: \"location\"\n    },\n    {\n        name: \"Locate\",\n        icon: \"lucide:locate\",\n        category: \"location\"\n    },\n    {\n        name: \"Locate Fixed\",\n        icon: \"lucide:locate-fixed\",\n        category: \"location\"\n    },\n    {\n        name: \"Locate Off\",\n        icon: \"lucide:locate-off\",\n        category: \"location\"\n    },\n    // Notifications & Alerts\n    {\n        name: \"Bell\",\n        icon: \"lucide:bell\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Bell Off\",\n        icon: \"lucide:bell-off\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Bell Ring\",\n        icon: \"lucide:bell-ring\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Alert Circle\",\n        icon: \"lucide:alert-circle\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Alert Triangle\",\n        icon: \"lucide:alert-triangle\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Alert Octagon\",\n        icon: \"lucide:alert-octagon\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Info\",\n        icon: \"lucide:info\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Help Circle\",\n        icon: \"lucide:help-circle\",\n        category: \"notifications\"\n    },\n    {\n        name: \"Question Mark\",\n        icon: \"lucide:question-mark-circle\",\n        category: \"notifications\"\n    },\n    // E-commerce & Shopping\n    {\n        name: \"Shopping Cart\",\n        icon: \"lucide:shopping-cart\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Shopping Bag\",\n        icon: \"lucide:shopping-bag\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Gift\",\n        icon: \"lucide:gift\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Tag\",\n        icon: \"lucide:tag\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Tags\",\n        icon: \"lucide:tags\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Receipt\",\n        icon: \"lucide:receipt\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Percent\",\n        icon: \"lucide:percent\",\n        category: \"shopping\"\n    },\n    {\n        name: \"Ticket\",\n        icon: \"lucide:ticket\",\n        category: \"shopping\"\n    },\n    // Design & Creative\n    {\n        name: \"Brush\",\n        icon: \"lucide:brush\",\n        category: \"design\"\n    },\n    {\n        name: \"Pen Tool\",\n        icon: \"lucide:pen-tool\",\n        category: \"design\"\n    },\n    {\n        name: \"Pipette\",\n        icon: \"lucide:pipette\",\n        category: \"design\"\n    },\n    {\n        name: \"Layers\",\n        icon: \"lucide:layers\",\n        category: \"design\"\n    },\n    {\n        name: \"Layout Grid\",\n        icon: \"lucide:layout-grid\",\n        category: \"design\"\n    },\n    {\n        name: \"Crop\",\n        icon: \"lucide:crop\",\n        category: \"design\"\n    },\n    {\n        name: \"Scissors\",\n        icon: \"lucide:scissors\",\n        category: \"design\"\n    },\n    {\n        name: \"Combine\",\n        icon: \"lucide:combine\",\n        category: \"design\"\n    },\n    {\n        name: \"Separate\",\n        icon: \"lucide:separate-horizontal\",\n        category: \"design\"\n    },\n    // Emoji & Expressions\n    {\n        name: \"Heart\",\n        icon: \"lucide:heart\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Heart Broken\",\n        icon: \"lucide:heart-crack\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Kiss\",\n        icon: \"lucide:kiss\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Angry\",\n        icon: \"lucide:angry\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Surprised\",\n        icon: \"lucide:surprised\",\n        category: \"emoji\"\n    },\n    {\n        name: \"Wink\",\n        icon: \"lucide:eye-off\",\n        category: \"emoji\"\n    },\n    // Miscellaneous\n    {\n        name: \"Lightbulb\",\n        icon: \"lucide:lightbulb\",\n        category: \"misc\"\n    },\n    {\n        name: \"Zap\",\n        icon: \"lucide:zap\",\n        category: \"misc\"\n    },\n    {\n        name: \"Fire\",\n        icon: \"lucide:flame\",\n        category: \"misc\"\n    },\n    {\n        name: \"Snowflake\",\n        icon: \"lucide:snowflake\",\n        category: \"misc\"\n    },\n    {\n        name: \"Droplet\",\n        icon: \"lucide:droplet\",\n        category: \"misc\"\n    },\n    {\n        name: \"Feather\",\n        icon: \"lucide:feather\",\n        category: \"misc\"\n    },\n    {\n        name: \"Anchor\",\n        icon: \"lucide:anchor\",\n        category: \"misc\"\n    },\n    {\n        name: \"Infinity\",\n        icon: \"lucide:infinity\",\n        category: \"misc\"\n    },\n    {\n        name: \"Atom\",\n        icon: \"lucide:atom\",\n        category: \"misc\"\n    },\n    {\n        name: \"Dna\",\n        icon: \"lucide:dna\",\n        category: \"misc\"\n    },\n    {\n        name: \"Microscope\",\n        icon: \"lucide:microscope\",\n        category: \"misc\"\n    },\n    {\n        name: \"Telescope\",\n        icon: \"lucide:telescope\",\n        category: \"misc\"\n    },\n    {\n        name: \"Rocket\",\n        icon: \"lucide:rocket\",\n        category: \"misc\"\n    },\n    {\n        name: \"Satellite\",\n        icon: \"lucide:satellite\",\n        category: \"misc\"\n    },\n    {\n        name: \"Orbit\",\n        icon: \"lucide:orbit\",\n        category: \"misc\"\n    },\n    // Additional Popular Icons\n    {\n        name: \"Bookmark Plus\",\n        icon: \"lucide:bookmark-plus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Bookmark Minus\",\n        icon: \"lucide:bookmark-minus\",\n        category: \"actions\"\n    },\n    {\n        name: \"Bookmark Check\",\n        icon: \"lucide:bookmark-check\",\n        category: \"actions\"\n    },\n    {\n        name: \"Bookmark X\",\n        icon: \"lucide:bookmark-x\",\n        category: \"actions\"\n    },\n    {\n        name: \"Filter\",\n        icon: \"lucide:filter\",\n        category: \"actions\"\n    },\n    {\n        name: \"Sort Asc\",\n        icon: \"lucide:sort-asc\",\n        category: \"actions\"\n    },\n    {\n        name: \"Sort Desc\",\n        icon: \"lucide:sort-desc\",\n        category: \"actions\"\n    },\n    {\n        name: \"Maximize\",\n        icon: \"lucide:maximize\",\n        category: \"actions\"\n    },\n    {\n        name: \"Minimize\",\n        icon: \"lucide:minimize\",\n        category: \"actions\"\n    },\n    {\n        name: \"Expand\",\n        icon: \"lucide:expand\",\n        category: \"actions\"\n    },\n    {\n        name: \"Shrink\",\n        icon: \"lucide:shrink\",\n        category: \"actions\"\n    },\n    {\n        name: \"Fullscreen\",\n        icon: \"lucide:fullscreen\",\n        category: \"actions\"\n    },\n    {\n        name: \"Exit Fullscreen\",\n        icon: \"lucide:minimize-2\",\n        category: \"actions\"\n    }\n];\nconst ShapeSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    // Filter icons based on search term\n    const filteredIcons = searchTerm ? iconifyElements.filter((element)=>element.name.toLowerCase().includes(searchTerm.toLowerCase()) || element.category.toLowerCase().includes(searchTerm.toLowerCase())) : iconifyElements.filter((element)=>element.featured);\n    // Get display icons (6 featured by default, all filtered when searching)\n    const displayIcons = searchTerm ? filteredIcons : filteredIcons.slice(0, 6);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"shapes\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_5__.ToolSidebarHeader, {\n                title: \"Elements\",\n                description: \"Add elements to your canvas\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                    placeholder: \"Search elements...\",\n                    value: searchTerm,\n                    onChange: (e)=>setSearchTerm(e.target.value),\n                    className: \"w-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium mb-3 text-gray-700\",\n                            children: \"Basic Shapes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addCircle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCircle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addSoftRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquare\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addRectangle(),\n                                    icon: _barrel_optimize_names_FaCircle_FaSquare_FaSquareFull_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSquareFull\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addInverseTriangle(),\n                                    icon: _barrel_optimize_names_IoTriangle_react_icons_io5__WEBPACK_IMPORTED_MODULE_10__.IoTriangle,\n                                    iconClassName: \"rotate-180\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_shape_tool__WEBPACK_IMPORTED_MODULE_3__.ShapeTool, {\n                                    onClick: ()=>editor === null || editor === void 0 ? void 0 : editor.addDiamond(),\n                                    icon: _barrel_optimize_names_FaDiamond_react_icons_fa6__WEBPACK_IMPORTED_MODULE_11__.FaDiamond\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Icons & Elements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, undefined),\n                                searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        filteredIcons.length,\n                                        \" found\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, undefined),\n                                !searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        iconifyElements.length - 6,\n                                        \" more available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-4\",\n                            children: displayIcons.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // Add the icon as an SVG element to the canvas\n                                        // This is a placeholder - you might need to implement addIcon method in your editor\n                                        console.log(\"Adding \".concat(element.name, \" icon\"));\n                                    },\n                                    className: \"aspect-square border rounded-md p-3 hover:bg-gray-50 transition-colors flex items-center justify-center group\",\n                                    title: element.name,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iconify_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        icon: element.icon,\n                                        className: \"h-6 w-6 text-gray-700 group-hover:text-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, element.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, undefined),\n                        !searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-gray-50 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 text-center\",\n                                children: [\n                                    \"\\uD83D\\uDD0D Search to discover \",\n                                    iconifyElements.length - 6,\n                                    \" more icons across categories like weather, food, business, and more!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, undefined),\n                        searchTerm && filteredIcons.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-gray-50 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 text-center\",\n                                children: [\n                                    'No icons found for \"',\n                                    searchTerm,\n                                    '\". Try searching for categories like \"arrow\", \"weather\", \"food\", or \"business\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_4__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\shape-sidebar.tsx\",\n        lineNumber: 403,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShapeSidebar, \"+YdqPTpSlp4r5CWiFEQiF/UjThM=\");\n_c = ShapeSidebar;\nvar _c;\n$RefreshReg$(_c, \"ShapeSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/shape-sidebar.tsx\n"));

/***/ })

});