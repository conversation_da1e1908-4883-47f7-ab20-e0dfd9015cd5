"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n// Icon SVG paths mapping for actual icon rendering\nconst ICON_PATHS = {\n    \"lucide:heart\": \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\",\n    \"lucide:star\": \"m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\",\n    \"lucide:arrow-right\": \"M5 12h14m-7-7 7 7-7 7\",\n    \"lucide:arrow-left\": \"m12 19-7-7 7-7m-7 7h14\",\n    \"lucide:arrow-up\": \"m18 15-6-6-6 6\",\n    \"lucide:arrow-down\": \"m6 9 6 6 6-6\",\n    \"lucide:home\": \"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z M9 22V12h6v10\",\n    \"lucide:user\": \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2 M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z\",\n    \"lucide:settings\": \"M12.22 2h-.44a2 2 0 0 0-2 2.18l.2 1.81c-.26.14-.51.3-.74.48l-1.8-.37a2 2 0 0 0-2.26 1.3l-.22.44a2 2 0 0 0 .47 2.26l1.31 1.31c-.18.23-.34.48-.48.74l-1.81.2a2 2 0 0 0-1.82 2v.44a2 2 0 0 0 1.82 2l1.81.2c.14.26.3.51.48.74l-1.31 1.31a2 2 0 0 0-.47 2.26l.22.44a2 2 0 0 0 2.26 1.3l1.8-.37c.23.18.48.34.74.48l.2 1.81a2 2 0 0 0 2 1.82h.44a2 2 0 0 0 2-1.82l.2-1.81c.26-.14.51-.3.74-.48l1.8.37a2 2 0 0 0 2.26-1.3l.22-.44a2 2 0 0 0-.47-2.26l-1.31-1.31c.18-.23.34-.48.48-.74l1.81-.2a2 2 0 0 0 1.82-2v-.44a2 2 0 0 0-1.82-2l-1.81-.2c-.14-.26-.3-.51-.48-.74l1.31-1.31a2 2 0 0 0 .47-2.26l-.22-.44a2 2 0 0 0-2.26-1.3l-1.8.37c-.23-.18-.48-.34-.74-.48l-.2-1.81a2 2 0 0 0-2-1.82Z M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z\",\n    \"lucide:mail\": \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z m0 4 8 5 8-5\",\n    \"lucide:phone\": \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\",\n    \"lucide:calendar\": \"M8 2v4m8-4v4M3 10h18M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z\",\n    \"lucide:clock\": \"M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z m5 10-5 3V7\",\n    \"lucide:camera\": \"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z M12 17a3 3 0 1 1 0-6 3 3 0 0 1 0 6z\",\n    \"lucide:music\": \"M9 18V5l12-2v13M9 13a3 3 0 1 0 0 6 3 3 0 0 0 0-6z m12-2a3 3 0 1 0 0 6 3 3 0 0 0 0-6z\",\n    \"lucide:play\": \"m9 18 6-6-6-6v12z\",\n    \"lucide:pause\": \"M6 4h4v16H6z m8 0h4v16h-4z\",\n    \"lucide:volume-2\": \"M11 5 6 9H2v6h4l5 4V5z m8.54 8.54a8 8 0 0 0 0-11.32M15.54 8.46a4 4 0 0 1 0 5.66\",\n    \"lucide:wifi\": \"m1 9 2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9z m4 4 2 2a7.07 7.07 0 0 1 10 0l2-2a10.94 10.94 0 0 0-14 0z m4 4 2 2 2-2a3.53 3.53 0 0 0-4 0z\",\n    \"lucide:battery\": \"M15 7h1a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h7z m7 4v2\",\n    \"lucide:bluetooth\": \"m7 7 10 5-5 5V2l5 5L7 17\",\n    \"lucide:download\": \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3\",\n    \"lucide:upload\": \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12\",\n    \"lucide:search\": \"m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z\",\n    \"lucide:plus\": \"M12 5v14m-7-7h14\",\n    \"lucide:minus\": \"M5 12h14\",\n    \"lucide:check\": \"m9 12 2 2 4-4\",\n    \"lucide:x\": \"m18 6-12 12M6 6l12 12\"\n};\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        addIcon: (iconName)=>{\n            const iconPath = ICON_PATHS[iconName];\n            if (iconPath) {\n                // Create proper SVG string for the icon with better formatting\n                const svgString = '<svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"'.concat(iconPath, '\" fill=\"none\" stroke=\"').concat(strokeColor || \"#000000\", '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>');\n                // Load SVG and add to canvas\n                fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.loadSVGFromString(svgString, (objects, options)=>{\n                    const svgObject = fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.util.groupSVGElements(objects, options);\n                    if (svgObject) {\n                        svgObject.set({\n                            left: 100,\n                            top: 100,\n                            scaleX: 1,\n                            scaleY: 1,\n                            // Lock the icon so it can't be resized\n                            lockScalingX: true,\n                            lockScalingY: true,\n                            lockUniScaling: true,\n                            // Keep other transformations available\n                            lockMovementX: false,\n                            lockMovementY: false,\n                            lockRotation: false,\n                            // Ensure it's selectable and movable\n                            selectable: true,\n                            evented: true\n                        });\n                        addToCanvas(svgObject);\n                    }\n                });\n            } else {\n                // Fallback for unknown icons - create a simple icon placeholder\n                const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                    width: 80,\n                    height: 80,\n                    fill: \"transparent\",\n                    stroke: strokeColor || \"#000000\",\n                    strokeWidth: 2,\n                    strokeDashArray: [\n                        5,\n                        5\n                    ],\n                    rx: 8,\n                    ry: 8,\n                    lockScalingX: true,\n                    lockScalingY: true,\n                    lockUniScaling: true\n                });\n                addToCanvas(object);\n            }\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback, setCanvasIsSelected } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback,\n        setCanvasIsSelected\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWVkaXRvci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDK0I7QUFtQjlCO0FBQ2dDO0FBTWhDO0FBQ2dDO0FBQ0s7QUFDRTtBQUNJO0FBQ0o7QUFDSTtBQUNOO0FBRXRFLG1EQUFtRDtBQUNuRCxNQUFNOEIsYUFBcUM7SUFDekMsZ0JBQWdCO0lBQ2hCLGVBQWU7SUFDZixzQkFBc0I7SUFDdEIscUJBQXFCO0lBQ3JCLG1CQUFtQjtJQUNuQixxQkFBcUI7SUFDckIsZUFBZTtJQUNmLGVBQWU7SUFDZixtQkFBbUI7SUFDbkIsZUFBZTtJQUNmLGdCQUFnQjtJQUNoQixtQkFBbUI7SUFDbkIsZ0JBQWdCO0lBQ2hCLGlCQUFpQjtJQUNqQixnQkFBZ0I7SUFDaEIsZUFBZTtJQUNmLGdCQUFnQjtJQUNoQixtQkFBbUI7SUFDbkIsZUFBZTtJQUNmLGtCQUFrQjtJQUNsQixvQkFBb0I7SUFDcEIsbUJBQW1CO0lBQ25CLGlCQUFpQjtJQUNqQixpQkFBaUI7SUFDakIsZUFBZTtJQUNmLGdCQUFnQjtJQUNoQixnQkFBZ0I7SUFDaEIsWUFBWTtBQUNkO0FBRUEsTUFBTUMsY0FBYztRQUFDLEVBQ25CQyxJQUFJLEVBQ0pDLElBQUksRUFDSkMsSUFBSSxFQUNKQyxPQUFPLEVBQ1BDLE9BQU8sRUFDUEMsUUFBUSxFQUNSQyxJQUFJLEVBQ0pDLEtBQUssRUFDTEMsTUFBTSxFQUNOQyxTQUFTLEVBQ1RDLFVBQVUsRUFDVkMsYUFBYSxFQUNiQyxZQUFZLEVBQ1pDLFdBQVcsRUFDWEMsY0FBYyxFQUNkQyxXQUFXLEVBQ1hDLGNBQWMsRUFDZEMsZUFBZSxFQUNmQyxlQUFlLEVBQ2ZDLGtCQUFrQixFQUNEO0lBQ2pCLE1BQU1DLHNCQUFzQjtRQUMxQixNQUFNLEVBQUVDLEtBQUssRUFBRUMsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEdBQUcsRUFBRSxHQUFHQztRQUVyQyxPQUFPO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RQO1lBQ0FDO1lBQ0FDO1lBQ0FDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1LLFVBQVU7UUFDZCxNQUFNQyxVQUFVVjtRQUVoQlosT0FBT3VCLG9CQUFvQixDQUFDO1lBQUM7WUFBRztZQUFHO1lBQUc7WUFBRztZQUFHO1NBQUU7UUFDOUMsTUFBTUMsVUFBVXhCLE9BQU95QixTQUFTLENBQUNIO1FBRWpDMUMsb0VBQVlBLENBQUM0QyxTQUFTO1FBQ3RCM0I7SUFDRjtJQUVBLE1BQU02QixVQUFVO1FBQ2QsTUFBTUosVUFBVVY7UUFFaEJaLE9BQU91QixvQkFBb0IsQ0FBQztZQUFDO1lBQUc7WUFBRztZQUFHO1lBQUc7WUFBRztTQUFFO1FBQzlDLE1BQU1DLFVBQVV4QixPQUFPeUIsU0FBUyxDQUFDSDtRQUVqQzFDLG9FQUFZQSxDQUFDNEMsU0FBUztRQUN0QjNCO0lBQ0Y7SUFFQSxNQUFNOEIsVUFBVTtRQUNkLE1BQU1MLFVBQVVWO1FBRWhCWixPQUFPdUIsb0JBQW9CLENBQUM7WUFBQztZQUFHO1lBQUc7WUFBRztZQUFHO1lBQUc7U0FBRTtRQUM5QyxNQUFNQyxVQUFVeEIsT0FBT3lCLFNBQVMsQ0FBQ0g7UUFFakMxQyxvRUFBWUEsQ0FBQzRDLFNBQVM7UUFDdEIzQjtJQUNGO0lBRUEsTUFBTStCLFdBQVc7UUFDZixNQUFNSixVQUFVeEIsT0FBTzZCLE1BQU0sQ0FBQ3BELDZEQUFTQTtRQUV2QyxNQUFNSyxxRUFBYUEsQ0FBQzBDLFFBQVFNLE9BQU87UUFDbkMsTUFBTUMsYUFBYSxnQ0FFakIsT0FGaURDLG1CQUNqREMsS0FBS0MsU0FBUyxDQUFDVixTQUFTLE1BQU07UUFFaEM1QyxvRUFBWUEsQ0FBQ21ELFlBQVk7SUFDM0I7SUFFQSxNQUFNSSxXQUFXLENBQUNDO1FBQ2hCLE1BQU1DLE9BQU9KLEtBQUtLLEtBQUssQ0FBQ0Y7UUFFeEJwQyxPQUFPdUMsWUFBWSxDQUFDRixNQUFNO1lBQ3hCeEM7UUFDRjtJQUNGO0lBRUEsTUFBTW9CLGVBQWU7UUFDbkIsT0FBT2pCLE9BQ053QyxVQUFVLEdBQ1ZDLElBQUksQ0FBQyxDQUFDQyxTQUFXQSxPQUFPeEIsSUFBSSxLQUFLO0lBQ3BDO0lBRUEsTUFBTXlCLFNBQVMsQ0FBQ0Q7UUFDZCxNQUFNRSxZQUFZM0I7UUFDbEIsTUFBTTBCLFNBQVNDLHNCQUFBQSxnQ0FBQUEsVUFBV0MsY0FBYztRQUV4QyxJQUFJLENBQUNGLFFBQVE7UUFFYixhQUFhO1FBQ2IzQyxPQUFPOEMsYUFBYSxDQUFDSixRQUFRQztJQUMvQjtJQUVBLE1BQU1JLGNBQWMsQ0FBQ0w7UUFDbkJDLE9BQU9EO1FBQ1AxQyxPQUFPZ0QsR0FBRyxDQUFDTjtRQUNYMUMsT0FBT2lELGVBQWUsQ0FBQ1A7SUFDekI7SUFFQSxPQUFPO1FBQ0xyQjtRQUNBTTtRQUNBRDtRQUNBRTtRQUNBTztRQUNBdkM7UUFDQUQ7UUFDQUU7UUFDQW9CO1FBQ0FpQyxRQUFRO1lBQ04sSUFBSUMsWUFBWW5ELE9BQU9vRCxPQUFPO1lBQzlCRCxhQUFhO1lBQ2IsTUFBTVIsU0FBUzNDLE9BQU9xRCxTQUFTO1lBQy9CckQsT0FBT3NELFdBQVcsQ0FDaEIsSUFBSTlGLDBDQUFNQSxDQUFDK0YsS0FBSyxDQUFDWixPQUFPNUIsSUFBSSxFQUFFNEIsT0FBTzNCLEdBQUcsR0FDeENtQyxZQUFZLElBQUksSUFBSUE7UUFFeEI7UUFDQUssU0FBUztZQUNQLElBQUlMLFlBQVluRCxPQUFPb0QsT0FBTztZQUM5QkQsYUFBYTtZQUNiLE1BQU1SLFNBQVMzQyxPQUFPcUQsU0FBUztZQUMvQnJELE9BQU9zRCxXQUFXLENBQ2hCLElBQUk5RiwwQ0FBTUEsQ0FBQytGLEtBQUssQ0FBQ1osT0FBTzVCLElBQUksRUFBRTRCLE9BQU8zQixHQUFHLEdBQ3hDbUMsWUFBWSxNQUFNLE1BQU1BO1FBRTVCO1FBQ0FNLFlBQVksQ0FBQ0M7WUFDWCxNQUFNZCxZQUFZM0I7WUFFbEIyQixzQkFBQUEsZ0NBQUFBLFVBQVdlLEdBQUcsQ0FBQ0Q7WUFDZjdEO1lBQ0FMO1FBQ0Y7UUFDQW9FLGtCQUFrQixDQUFDRjtZQUNqQixNQUFNZCxZQUFZM0I7WUFDbEIyQixzQkFBQUEsZ0NBQUFBLFVBQVdlLEdBQUcsQ0FBQztnQkFBRUUsTUFBTUg7WUFBTTtZQUM3QjFELE9BQU84RCxTQUFTO1lBQ2hCdEU7UUFDRjtRQUNBdUUsbUJBQW1CO1lBQ2pCL0QsT0FBT2dFLG1CQUFtQjtZQUMxQmhFLE9BQU84RCxTQUFTO1lBQ2hCOUQsT0FBT2lFLGFBQWEsR0FBRztZQUN2QmpFLE9BQU9rRSxnQkFBZ0IsQ0FBQ3JELEtBQUssR0FBR047WUFDaENQLE9BQU9rRSxnQkFBZ0IsQ0FBQ0MsS0FBSyxHQUFHOUQ7UUFDbEM7UUFDQStELG9CQUFvQjtZQUNsQnBFLE9BQU9pRSxhQUFhLEdBQUc7UUFDekI7UUFDQUksUUFBUSxJQUFNNUU7UUFDZDZFLFFBQVEsSUFBTTVFO1FBQ2Q2RSxRQUFRLElBQU16RTtRQUNkMEUsU0FBUyxJQUFNekU7UUFDZjBFLG1CQUFtQixDQUFDZjtZQUNsQixNQUFNNUIsVUFBVTlCLE9BQU8wRSxnQkFBZ0I7WUFDdkM1QyxRQUFRNkMsT0FBTyxDQUFDLENBQUNqQztnQkFDZixJQUFJQSxPQUFPa0MsSUFBSSxLQUFLLFNBQVM7b0JBQzNCLE1BQU1DLGNBQWNuQztvQkFFcEIsTUFBTW9DLFNBQVNuRyxvRUFBWUEsQ0FBQytFO29CQUU1Qm1CLFlBQVlFLE9BQU8sR0FBR0QsU0FBUzt3QkFBQ0E7cUJBQU8sR0FBRyxFQUFFO29CQUM1Q0QsWUFBWUcsWUFBWTtvQkFDeEJoRixPQUFPOEQsU0FBUztnQkFDbEI7WUFDRjtRQUNGO1FBQ0FtQixVQUFVLENBQUN2QjtZQUNUbEcsMENBQU1BLENBQUMwSCxLQUFLLENBQUNDLE9BQU8sQ0FDbEJ6QixPQUNBLENBQUMwQjtnQkFDQyxNQUFNeEMsWUFBWTNCO2dCQUVsQm1FLE1BQU1DLFlBQVksQ0FBQ3pDLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVy9CLEtBQUssS0FBSTtnQkFDdkN1RSxNQUFNRSxhQUFhLENBQUMxQyxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVc5QixNQUFNLEtBQUk7Z0JBRXpDaUMsWUFBWXFDO1lBQ2QsR0FDQTtnQkFDRUcsYUFBYTtZQUNmO1FBRUo7UUFDQUMsUUFBUTtZQUNOeEYsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDLFNBQVcxQyxPQUFPeUYsTUFBTSxDQUFDL0M7WUFDNUQxQyxPQUFPZ0UsbUJBQW1CO1lBQzFCaEUsT0FBTzhELFNBQVM7UUFDbEI7UUFDQTRCLFNBQVMsQ0FBQ2hDLE9BQU9wQztZQUNmLE1BQU1vQixTQUFTLElBQUlsRiwwQ0FBTUEsQ0FBQ21JLE9BQU8sQ0FBQ2pDLE9BQU87Z0JBQ3ZDLEdBQUdyRixnRUFBWTtnQkFDZndGLE1BQU01RDtnQkFDTixHQUFHcUIsT0FBTztZQUNaO1lBRUF5QixZQUFZTDtRQUNkO1FBQ0FrRCxrQkFBa0I7WUFDaEIsTUFBTUMsaUJBQWlCcEYsZUFBZSxDQUFDLEVBQUU7WUFFekMsSUFBSSxDQUFDb0YsZ0JBQWdCO2dCQUNuQixPQUFPO1lBQ1Q7WUFFQSxNQUFNbkMsUUFBUW1DLGVBQWVDLEdBQUcsQ0FBQyxjQUFjO1lBRS9DLE9BQU9wQztRQUNUO1FBQ0FxQyxnQkFBZ0IsQ0FBQ3JDO1lBQ2YxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDLElBQUk3RCxrRUFBVUEsQ0FBQzZELE9BQU9rQyxJQUFJLEdBQUc7b0JBQzNCLGFBQWE7b0JBQ2Isc0NBQXNDO29CQUN0Q2xDLE9BQU9pQixHQUFHLENBQUM7d0JBQUVxQyxVQUFVdEM7b0JBQU07Z0JBQy9CO1lBQ0Y7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0FtQyxtQkFBbUI7WUFDakIsTUFBTUosaUJBQWlCcEYsZUFBZSxDQUFDLEVBQUU7WUFFekMsSUFBSSxDQUFDb0YsZ0JBQWdCO2dCQUNuQixPQUFPckgsNkRBQVNBO1lBQ2xCO1lBRUEsYUFBYTtZQUNiLHNDQUFzQztZQUN0QyxNQUFNa0YsUUFBUW1DLGVBQWVDLEdBQUcsQ0FBQyxlQUFldEgsNkRBQVNBO1lBRXpELE9BQU9rRjtRQUNUO1FBQ0F3QyxpQkFBaUIsQ0FBQ3hDO1lBQ2hCMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJN0Qsa0VBQVVBLENBQUM2RCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQixhQUFhO29CQUNiLHVDQUF1QztvQkFDdkNsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFd0MsV0FBV3pDO29CQUFNO2dCQUNoQztZQUNGO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBc0Msb0JBQW9CO1lBQ2xCLE1BQU1QLGlCQUFpQnBGLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ29GLGdCQUFnQjtnQkFDbkIsT0FBTztZQUNUO1lBRUEsYUFBYTtZQUNiLHVDQUF1QztZQUN2QyxNQUFNbkMsUUFBUW1DLGVBQWVDLEdBQUcsQ0FBQyxnQkFBZ0I7WUFFakQsT0FBT3BDO1FBQ1Q7UUFDQTJDLHFCQUFxQixDQUFDM0M7WUFDcEIxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDLElBQUk3RCxrRUFBVUEsQ0FBQzZELE9BQU9rQyxJQUFJLEdBQUc7b0JBQzNCLGFBQWE7b0JBQ2IsdUNBQXVDO29CQUN2Q2xDLE9BQU9pQixHQUFHLENBQUM7d0JBQUUyQyxXQUFXNUM7b0JBQU07Z0JBQ2hDO1lBQ0Y7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0F5Qyx3QkFBd0I7WUFDdEIsTUFBTVYsaUJBQWlCcEYsZUFBZSxDQUFDLEVBQUU7WUFFekMsSUFBSSxDQUFDb0YsZ0JBQWdCO2dCQUNuQixPQUFPO1lBQ1Q7WUFFQSxhQUFhO1lBQ2IsdUNBQXVDO1lBQ3ZDLE1BQU1uQyxRQUFRbUMsZUFBZUMsR0FBRyxDQUFDLGdCQUFnQjtZQUVqRCxPQUFPcEM7UUFDVDtRQUNBOEMsdUJBQXVCLENBQUM5QztZQUN0QjFELE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQztnQkFDakMsSUFBSTdELGtFQUFVQSxDQUFDNkQsT0FBT2tDLElBQUksR0FBRztvQkFDM0IsYUFBYTtvQkFDYix5Q0FBeUM7b0JBQ3pDbEMsT0FBT2lCLEdBQUcsQ0FBQzt3QkFBRThDLGFBQWEvQztvQkFBTTtnQkFDbEM7WUFDRjtZQUNBMUQsT0FBTzhELFNBQVM7UUFDbEI7UUFDQTRDLDBCQUEwQjtZQUN4QixNQUFNYixpQkFBaUJwRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNvRixnQkFBZ0I7Z0JBQ25CLE9BQU87WUFDVDtZQUVBLGFBQWE7WUFDYix5Q0FBeUM7WUFDekMsTUFBTW5DLFFBQVFtQyxlQUFlQyxHQUFHLENBQUMsa0JBQWtCO1lBRW5ELE9BQU9wQztRQUNUO1FBQ0FpRCxpQkFBaUIsQ0FBQ2pEO1lBQ2hCMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJN0Qsa0VBQVVBLENBQUM2RCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQixhQUFhO29CQUNiLHVDQUF1QztvQkFDdkNsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFaUQsV0FBV2xEO29CQUFNO2dCQUNoQztZQUNGO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBK0Msb0JBQW9CO1lBQ2xCLE1BQU1oQixpQkFBaUJwRixlQUFlLENBQUMsRUFBRTtZQUV6QyxJQUFJLENBQUNvRixnQkFBZ0I7Z0JBQ25CLE9BQU87WUFDVDtZQUVBLGFBQWE7WUFDYix1Q0FBdUM7WUFDdkMsTUFBTW5DLFFBQVFtQyxlQUFlQyxHQUFHLENBQUMsZ0JBQWdCO1lBRWpELE9BQU9wQztRQUNUO1FBQ0FvRCxrQkFBa0IsQ0FBQ3BEO1lBQ2pCMUQsT0FBTzBFLGdCQUFnQixHQUFHQyxPQUFPLENBQUMsQ0FBQ2pDO2dCQUNqQyxJQUFJN0Qsa0VBQVVBLENBQUM2RCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQixhQUFhO29CQUNiLHdDQUF3QztvQkFDeENsQyxPQUFPaUIsR0FBRyxDQUFDO3dCQUFFb0QsWUFBWXJEO29CQUFNO2dCQUNqQztZQUNGO1lBQ0ExRCxPQUFPOEQsU0FBUztRQUNsQjtRQUNBa0QsZUFBZSxDQUFDdEQ7WUFDZDFELE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQztnQkFDakNBLE9BQU9pQixHQUFHLENBQUM7b0JBQUVzRCxTQUFTdkQ7Z0JBQU07WUFDOUI7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0FvRCxjQUFjO1lBQ1psSCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDMUMsT0FBT2tILFlBQVksQ0FBQ3hFO1lBQ3RCO1lBRUExQyxPQUFPOEQsU0FBUztZQUVoQixNQUFNbEIsWUFBWTNCO1lBQ2xCMkIsc0JBQUFBLGdDQUFBQSxVQUFXdUUsVUFBVTtRQUN2QjtRQUNBQyxlQUFlO1lBQ2JwSCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDMUMsT0FBT29ILGFBQWEsQ0FBQzFFO1lBQ3ZCO1lBRUExQyxPQUFPOEQsU0FBUztZQUNoQixNQUFNbEIsWUFBWTNCO1lBQ2xCMkIsc0JBQUFBLGdDQUFBQSxVQUFXdUUsVUFBVTtRQUN2QjtRQUNBRSxrQkFBa0IsQ0FBQzNEO1lBQ2pCdkQsY0FBY3VEO1lBQ2QxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDLElBQUk3RCxrRUFBVUEsQ0FBQzZELE9BQU9rQyxJQUFJLEdBQUc7b0JBQzNCLGFBQWE7b0JBQ2Isd0NBQXdDO29CQUN4Q2xDLE9BQU9pQixHQUFHLENBQUM7d0JBQUV6RCxZQUFZd0Q7b0JBQU07Z0JBQ2pDO1lBQ0Y7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0F3RCxpQkFBaUIsQ0FBQzVEO1lBQ2hCdEQsYUFBYXNEO1lBQ2IxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDQSxPQUFPaUIsR0FBRyxDQUFDO29CQUFFRSxNQUFNSDtnQkFBTTtZQUMzQjtZQUNBMUQsT0FBTzhELFNBQVM7UUFDbEI7UUFDQXlELG1CQUFtQixDQUFDN0Q7WUFDbEJwRCxlQUFlb0Q7WUFDZjFELE9BQU8wRSxnQkFBZ0IsR0FBR0MsT0FBTyxDQUFDLENBQUNqQztnQkFDakMsK0JBQStCO2dCQUMvQixJQUFJN0Qsa0VBQVVBLENBQUM2RCxPQUFPa0MsSUFBSSxHQUFHO29CQUMzQmxDLE9BQU9pQixHQUFHLENBQUM7d0JBQUVFLE1BQU1IO29CQUFNO29CQUN6QjtnQkFDRjtnQkFFQWhCLE9BQU9pQixHQUFHLENBQUM7b0JBQUU2RCxRQUFROUQ7Z0JBQU07WUFDN0I7WUFDQTFELE9BQU9rRSxnQkFBZ0IsQ0FBQ0MsS0FBSyxHQUFHVDtZQUNoQzFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0EyRCxtQkFBbUIsQ0FBQy9EO1lBQ2xCbEQsZUFBZWtEO1lBQ2YxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDQSxPQUFPaUIsR0FBRyxDQUFDO29CQUFFcEQsYUFBYW1EO2dCQUFNO1lBQ2xDO1lBQ0ExRCxPQUFPa0UsZ0JBQWdCLENBQUNyRCxLQUFLLEdBQUc2QztZQUNoQzFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0E0RCx1QkFBdUIsQ0FBQ2hFO1lBQ3RCL0MsbUJBQW1CK0M7WUFDbkIxRCxPQUFPMEUsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxDQUFDakM7Z0JBQ2pDQSxPQUFPaUIsR0FBRyxDQUFDO29CQUFFakQsaUJBQWlCZ0Q7Z0JBQU07WUFDdEM7WUFDQTFELE9BQU84RCxTQUFTO1FBQ2xCO1FBQ0E2RCxXQUFXO1lBQ1QsTUFBTWpGLFNBQVMsSUFBSWxGLDBDQUFNQSxDQUFDb0ssTUFBTSxDQUFDO2dCQUMvQixHQUFHNUosa0VBQWM7Z0JBQ2pCNkYsTUFBTTVEO2dCQUNOdUgsUUFBUW5IO2dCQUNSRSxhQUFhQTtnQkFDYkcsaUJBQWlCQTtZQUNuQjtZQUVBcUMsWUFBWUw7UUFDZDtRQUNBbUYsa0JBQWtCO1lBQ2hCLE1BQU1uRixTQUFTLElBQUlsRiwwQ0FBTUEsQ0FBQ3NLLElBQUksQ0FBQztnQkFDN0IsR0FBRzNKLHFFQUFpQjtnQkFDcEI0SixJQUFJO2dCQUNKQyxJQUFJO2dCQUNKbkUsTUFBTTVEO2dCQUNOdUgsUUFBUW5IO2dCQUNSRSxhQUFhQTtnQkFDYkcsaUJBQWlCQTtZQUNuQjtZQUVBcUMsWUFBWUw7UUFDZDtRQUNBdUYsY0FBYztZQUNaLE1BQU12RixTQUFTLElBQUlsRiwwQ0FBTUEsQ0FBQ3NLLElBQUksQ0FBQztnQkFDN0IsR0FBRzNKLHFFQUFpQjtnQkFDcEIwRixNQUFNNUQ7Z0JBQ051SCxRQUFRbkg7Z0JBQ1JFLGFBQWFBO2dCQUNiRyxpQkFBaUJBO1lBQ25CO1lBRUFxQyxZQUFZTDtRQUNkO1FBQ0F3RixhQUFhO1lBQ1gsTUFBTXhGLFNBQVMsSUFBSWxGLDBDQUFNQSxDQUFDMkssUUFBUSxDQUFDO2dCQUNqQyxHQUFHakssb0VBQWdCO2dCQUNuQjJGLE1BQU01RDtnQkFDTnVILFFBQVFuSDtnQkFDUkUsYUFBYUE7Z0JBQ2JHLGlCQUFpQkE7WUFDbkI7WUFFQXFDLFlBQVlMO1FBQ2Q7UUFDQTBGLG9CQUFvQjtZQUNsQixNQUFNQyxTQUFTbkssb0VBQWdCQSxDQUFDNEMsTUFBTTtZQUN0QyxNQUFNd0gsUUFBUXBLLG9FQUFnQkEsQ0FBQzJDLEtBQUs7WUFFcEMsTUFBTTZCLFNBQVMsSUFBSWxGLDBDQUFNQSxDQUFDK0ssT0FBTyxDQUMvQjtnQkFDRTtvQkFBRUMsR0FBRztvQkFBR0MsR0FBRztnQkFBRTtnQkFDYjtvQkFBRUQsR0FBR0Y7b0JBQU9HLEdBQUc7Z0JBQUU7Z0JBQ2pCO29CQUFFRCxHQUFHRixRQUFRO29CQUFHRyxHQUFHSjtnQkFBTzthQUMzQixFQUNEO2dCQUNFLEdBQUduSyxvRUFBZ0I7Z0JBQ25CMkYsTUFBTTVEO2dCQUNOdUgsUUFBUW5IO2dCQUNSRSxhQUFhQTtnQkFDYkcsaUJBQWlCQTtZQUNuQjtZQUdGcUMsWUFBWUw7UUFDZDtRQUNBZ0csWUFBWTtZQUNWLE1BQU1MLFNBQVNwSyxtRUFBZUEsQ0FBQzZDLE1BQU07WUFDckMsTUFBTXdILFFBQVFySyxtRUFBZUEsQ0FBQzRDLEtBQUs7WUFFbkMsTUFBTTZCLFNBQVMsSUFBSWxGLDBDQUFNQSxDQUFDK0ssT0FBTyxDQUMvQjtnQkFDRTtvQkFBRUMsR0FBR0YsUUFBUTtvQkFBR0csR0FBRztnQkFBRTtnQkFDckI7b0JBQUVELEdBQUdGO29CQUFPRyxHQUFHSixTQUFTO2dCQUFFO2dCQUMxQjtvQkFBRUcsR0FBR0YsUUFBUTtvQkFBR0csR0FBR0o7Z0JBQU87Z0JBQzFCO29CQUFFRyxHQUFHO29CQUFHQyxHQUFHSixTQUFTO2dCQUFFO2FBQ3ZCLEVBQ0Q7Z0JBQ0UsR0FBR3BLLG1FQUFlO2dCQUNsQjRGLE1BQU01RDtnQkFDTnVILFFBQVFuSDtnQkFDUkUsYUFBYUE7Z0JBQ2JHLGlCQUFpQkE7WUFDbkI7WUFFRnFDLFlBQVlMO1FBQ2Q7UUFDQTFDO1FBQ0EySSxxQkFBcUI7WUFDbkIsTUFBTTlDLGlCQUFpQnBGLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ29GLGdCQUFnQjtnQkFDbkIsT0FBT3RILCtEQUFXQTtZQUNwQjtZQUVBLGFBQWE7WUFDYix3Q0FBd0M7WUFDeEMsTUFBTW1GLFFBQVFtQyxlQUFlQyxHQUFHLENBQUMsaUJBQWlCdkgsK0RBQVdBO1lBRTdELE9BQU9tRjtRQUNUO1FBQ0FrRixxQkFBcUI7WUFDbkIsTUFBTS9DLGlCQUFpQnBGLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ29GLGdCQUFnQjtnQkFDbkIsT0FBTzNGO1lBQ1Q7WUFFQSxhQUFhO1lBQ2Isd0NBQXdDO1lBQ3hDLE1BQU13RCxRQUFRbUMsZUFBZUMsR0FBRyxDQUFDLGlCQUFpQjVGO1lBRWxELE9BQU93RDtRQUNUO1FBQ0FtRixvQkFBb0I7WUFDbEIsTUFBTWhELGlCQUFpQnBGLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ29GLGdCQUFnQjtnQkFDbkIsT0FBTzVGO1lBQ1Q7WUFFQSxNQUFNeUQsUUFBUW1DLGVBQWVDLEdBQUcsQ0FBQyxXQUFXN0Y7WUFFNUMsb0RBQW9EO1lBQ3BELE9BQU95RDtRQUNUO1FBQ0FvRixzQkFBc0I7WUFDcEIsTUFBTWpELGlCQUFpQnBGLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ29GLGdCQUFnQjtnQkFDbkIsT0FBT3hGO1lBQ1Q7WUFFQSxNQUFNcUQsUUFBUW1DLGVBQWVDLEdBQUcsQ0FBQyxhQUFhekY7WUFFOUMsT0FBT3FEO1FBQ1Q7UUFDQXFGLHNCQUFzQjtZQUNwQixNQUFNbEQsaUJBQWlCcEYsZUFBZSxDQUFDLEVBQUU7WUFFekMsSUFBSSxDQUFDb0YsZ0JBQWdCO2dCQUNuQixPQUFPdEY7WUFDVDtZQUVBLE1BQU1tRCxRQUFRbUMsZUFBZUMsR0FBRyxDQUFDLGtCQUFrQnZGO1lBRW5ELE9BQU9tRDtRQUNUO1FBQ0FzRiwwQkFBMEI7WUFDeEIsTUFBTW5ELGlCQUFpQnBGLGVBQWUsQ0FBQyxFQUFFO1lBRXpDLElBQUksQ0FBQ29GLGdCQUFnQjtnQkFDbkIsT0FBT25GO1lBQ1Q7WUFFQSxNQUFNZ0QsUUFBUW1DLGVBQWVDLEdBQUcsQ0FBQyxzQkFBc0JwRjtZQUV2RCxPQUFPZ0Q7UUFDVDtRQUNBdUYsU0FBUyxDQUFDQztZQUNSLE1BQU1DLFdBQVc3SixVQUFVLENBQUM0SixTQUFTO1lBRXJDLElBQUlDLFVBQVU7Z0JBQ1osK0RBQStEO2dCQUMvRCxNQUFNQyxZQUFZLCtGQUFnSS9JLE9BQWpDOEksVUFBUywwQkFBaUQsT0FBekI5SSxlQUFlLFdBQVU7Z0JBRTNLLDZCQUE2QjtnQkFDN0I3QywwQ0FBTUEsQ0FBQzZMLGlCQUFpQixDQUFDRCxXQUFXLENBQUN0SCxTQUFTUjtvQkFDNUMsTUFBTWdJLFlBQVk5TCwwQ0FBTUEsQ0FBQytMLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUMxSCxTQUFTUjtvQkFDeEQsSUFBSWdJLFdBQVc7d0JBQ2JBLFVBQVUzRixHQUFHLENBQUM7NEJBQ1o1QyxNQUFNOzRCQUNOQyxLQUFLOzRCQUNMeUksUUFBUTs0QkFDUkMsUUFBUTs0QkFDUix1Q0FBdUM7NEJBQ3ZDQyxjQUFjOzRCQUNkQyxjQUFjOzRCQUNkQyxnQkFBZ0I7NEJBQ2hCLHVDQUF1Qzs0QkFDdkNDLGVBQWU7NEJBQ2ZDLGVBQWU7NEJBQ2ZDLGNBQWM7NEJBQ2QscUNBQXFDOzRCQUNyQ0MsWUFBWTs0QkFDWkMsU0FBUzt3QkFDWDt3QkFDQW5ILFlBQVl1RztvQkFDZDtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsZ0VBQWdFO2dCQUNoRSxNQUFNNUcsU0FBUyxJQUFJbEYsMENBQU1BLENBQUNzSyxJQUFJLENBQUM7b0JBQzdCakgsT0FBTztvQkFDUEMsUUFBUTtvQkFDUitDLE1BQU07b0JBQ04yRCxRQUFRbkgsZUFBZTtvQkFDdkJFLGFBQWE7b0JBQ2JHLGlCQUFpQjt3QkFBQzt3QkFBRztxQkFBRTtvQkFDdkJxSCxJQUFJO29CQUNKQyxJQUFJO29CQUNKMkIsY0FBYztvQkFDZEMsY0FBYztvQkFDZEMsZ0JBQWdCO2dCQUNsQjtnQkFFQTlHLFlBQVlMO1lBQ2Q7UUFDRjtRQUNBakM7SUFDRjtBQUNGO0FBRU8sTUFBTTBKLFlBQVk7UUFBQyxFQUN4QkMsWUFBWSxFQUNaQyxhQUFhLEVBQ2JDLFlBQVksRUFDWkMsc0JBQXNCLEVBQ3RCQyxZQUFZLEVBQ1pDLG1CQUFtQixFQUNIO0lBQ2hCLE1BQU1DLGVBQWU5TSw2Q0FBTUEsQ0FBQ3dNO0lBQzVCLE1BQU1PLGVBQWUvTSw2Q0FBTUEsQ0FBQzBNO0lBQzVCLE1BQU1NLGdCQUFnQmhOLDZDQUFNQSxDQUFDeU07SUFFN0IsTUFBTSxDQUFDckssUUFBUTZLLFVBQVUsR0FBR25OLCtDQUFRQSxDQUF1QjtJQUMzRCxNQUFNLENBQUNvTixXQUFXQyxhQUFhLEdBQUdyTiwrQ0FBUUEsQ0FBd0I7SUFDbEUsTUFBTSxDQUFDK0MsaUJBQWlCdUssbUJBQW1CLEdBQUd0TiwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUUxRSxNQUFNLENBQUN3QyxZQUFZQyxjQUFjLEdBQUd6QywrQ0FBUUEsQ0FBQ1ksK0RBQVdBO0lBQ3hELE1BQU0sQ0FBQzJCLFdBQVdHLGFBQWEsR0FBRzFDLCtDQUFRQSxDQUFDRyw4REFBVUE7SUFDckQsTUFBTSxDQUFDd0MsYUFBYUMsZUFBZSxHQUFHNUMsK0NBQVFBLENBQUNLLGdFQUFZQTtJQUMzRCxNQUFNLENBQUN3QyxhQUFhQyxlQUFlLEdBQUc5QywrQ0FBUUEsQ0FBQ0ksZ0VBQVlBO0lBQzNELE1BQU0sQ0FBQzRDLGlCQUFpQkMsbUJBQW1CLEdBQUdqRCwrQ0FBUUEsQ0FBV1UscUVBQWlCQTtJQUVsRmdCLDBGQUFlQTtJQUVmLE1BQU0sRUFDSkksSUFBSSxFQUNKRyxPQUFPLEVBQ1BDLE9BQU8sRUFDUEgsSUFBSSxFQUNKQyxJQUFJLEVBQ0p1TCxhQUFhLEVBQ2JDLGVBQWUsRUFDaEIsR0FBR3hNLDhFQUFVQSxDQUFDO1FBQ2JzQjtRQUNBd0s7SUFDRjtJQUVBLE1BQU0sRUFBRTFLLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUdmLGtGQUFZQSxDQUFDO1FBQUVnQjtJQUFPO0lBRTlDLE1BQU0sRUFBRUgsUUFBUSxFQUFFLEdBQUdaLHFGQUFhQSxDQUFDO1FBQ2pDZTtRQUNBOEs7SUFDRjtJQUVBNUwseUZBQWVBLENBQUM7UUFDZE07UUFDQVE7UUFDQWdMO1FBQ0FUO1FBQ0FFO0lBQ0Y7SUFFQXRMLHFGQUFhQSxDQUFDO1FBQ1phO0lBQ0Y7SUFFQWpCLDhFQUFVQSxDQUFDO1FBQ1RVO1FBQ0FDO1FBQ0FJO1FBQ0FDO1FBQ0FQO1FBQ0FRO0lBQ0Y7SUFFQVgsb0ZBQVlBLENBQUM7UUFDWFc7UUFDQUg7UUFDQTZLO1FBQ0FPO1FBQ0FDO0lBQ0Y7SUFFQSxNQUFNQyxTQUFTeE4sOENBQU9BLENBQUM7UUFDckIsSUFBSXFDLFFBQVE7WUFDVixPQUFPVCxZQUFZO2dCQUNqQkM7Z0JBQ0FDO2dCQUNBQztnQkFDQUU7Z0JBQ0FEO2dCQUNBRTtnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FNO2dCQUNBRjtnQkFDQUQ7Z0JBQ0FFO2dCQUNBRTtnQkFDQUU7Z0JBQ0FEO2dCQUNBRTtnQkFDQVQ7Z0JBQ0FDO1lBQ0Y7UUFDRjtRQUVBLE9BQU9pTDtJQUNULEdBQ0E7UUFDRXpMO1FBQ0FDO1FBQ0FIO1FBQ0FDO1FBQ0FGO1FBQ0FLO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FNO1FBQ0FGO1FBQ0FJO1FBQ0FDO1FBQ0FSO0tBQ0Q7SUFFRCxNQUFNbUwsT0FBTzVOLGtEQUFXQSxDQUN0QjtZQUFDLEVBQ0M2TixhQUFhLEVBQ2JDLGdCQUFnQixFQUlqQjtRQUNDL04sMENBQU1BLENBQUNnTyxNQUFNLENBQUNDLFNBQVMsQ0FBQzlILEdBQUcsQ0FBQztZQUMxQitILGFBQWE7WUFDYkMsYUFBYTtZQUNiQyxhQUFhO1lBQ2JDLG1CQUFtQjtZQUNuQkMsb0JBQW9CO1lBQ3BCQyx5QkFBeUI7WUFDekJDLG1CQUFtQjtRQUNyQjtRQUVBLE1BQU1DLG1CQUFtQixJQUFJek8sMENBQU1BLENBQUNzSyxJQUFJLENBQUM7WUFDdkNqSCxPQUFPOEosYUFBYXVCLE9BQU87WUFDM0JwTCxRQUFROEosY0FBY3NCLE9BQU87WUFDN0JoTCxNQUFNO1lBQ04yQyxNQUFNO1lBQ05vRyxZQUFZO1lBQ1prQyxhQUFhO1lBQ2JDLFFBQVEsSUFBSTVPLDBDQUFNQSxDQUFDNk8sTUFBTSxDQUFDO2dCQUN4QmxJLE9BQU87Z0JBQ1BtSSxNQUFNO1lBQ1I7UUFDRjtRQUVBaEIsY0FBY2lCLFFBQVEsQ0FBQ2hCLGlCQUFpQmlCLFdBQVc7UUFDbkRsQixjQUFjbUIsU0FBUyxDQUFDbEIsaUJBQWlCbUIsWUFBWTtRQUVyRHBCLGNBQWN0SSxHQUFHLENBQUNpSjtRQUNsQlgsY0FBY3FCLFlBQVksQ0FBQ1Y7UUFDM0JYLGNBQWNzQixRQUFRLEdBQUdYO1FBRXpCcEIsVUFBVVM7UUFDVlAsYUFBYVE7UUFFYixNQUFNc0IsZUFBZTVLLEtBQUtDLFNBQVMsQ0FDakNvSixjQUFjekosTUFBTSxDQUFDcEQsNkRBQVNBO1FBRWhDd00sY0FBY2lCLE9BQU8sR0FBRztZQUFDVztTQUFhO1FBQ3RDM0IsZ0JBQWdCO0lBQ2xCLEdBQ0E7UUFDRUQ7UUFDQUM7S0FDRDtJQUdILE9BQU87UUFBRUc7UUFBTUY7SUFBTztBQUN4QixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWVkaXRvci50cz9jOWFkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZhYnJpYyB9IGZyb20gXCJmYWJyaWNcIjtcclxuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVN0YXRlLCB1c2VNZW1vLCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmltcG9ydCB7IFxyXG4gIEVkaXRvciwgXHJcbiAgRklMTF9DT0xPUixcclxuICBTVFJPS0VfV0lEVEgsXHJcbiAgU1RST0tFX0NPTE9SLFxyXG4gIENJUkNMRV9PUFRJT05TLFxyXG4gIERJQU1PTkRfT1BUSU9OUyxcclxuICBUUklBTkdMRV9PUFRJT05TLFxyXG4gIEJ1aWxkRWRpdG9yUHJvcHMsIFxyXG4gIFJFQ1RBTkdMRV9PUFRJT05TLFxyXG4gIEVkaXRvckhvb2tQcm9wcyxcclxuICBTVFJPS0VfREFTSF9BUlJBWSxcclxuICBURVhUX09QVElPTlMsXHJcbiAgRk9OVF9GQU1JTFksXHJcbiAgRk9OVF9XRUlHSFQsXHJcbiAgRk9OVF9TSVpFLFxyXG4gIEpTT05fS0VZUyxcclxufSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvdHlwZXNcIjtcclxuaW1wb3J0IHsgdXNlSGlzdG9yeSB9IGZyb20gXCJAL2ZlYXR1cmVzL2VkaXRvci9ob29rcy91c2UtaGlzdG9yeVwiO1xyXG5pbXBvcnQgeyBcclxuICBjcmVhdGVGaWx0ZXIsIFxyXG4gIGRvd25sb2FkRmlsZSwgXHJcbiAgaXNUZXh0VHlwZSxcclxuICB0cmFuc2Zvcm1UZXh0XHJcbn0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL3V0aWxzXCI7XHJcbmltcG9ydCB7IHVzZUhvdGtleXMgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWhvdGtleXNcIjtcclxuaW1wb3J0IHsgdXNlQ2xpcGJvYXJkIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2hvb2tzLy91c2UtY2xpcGJvYXJkXCI7XHJcbmltcG9ydCB7IHVzZUF1dG9SZXNpemUgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLWF1dG8tcmVzaXplXCI7XHJcbmltcG9ydCB7IHVzZUNhbnZhc0V2ZW50cyB9IGZyb20gXCJAL2ZlYXR1cmVzL2VkaXRvci9ob29rcy91c2UtY2FudmFzLWV2ZW50c1wiO1xyXG5pbXBvcnQgeyB1c2Vab29tRXZlbnRzIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2hvb2tzL3VzZS16b29tLWV2ZW50c1wiO1xyXG5pbXBvcnQgeyB1c2VXaW5kb3dFdmVudHMgfSBmcm9tIFwiQC9mZWF0dXJlcy9lZGl0b3IvaG9va3MvdXNlLXdpbmRvdy1ldmVudHNcIjtcclxuaW1wb3J0IHsgdXNlTG9hZFN0YXRlIH0gZnJvbSBcIkAvZmVhdHVyZXMvZWRpdG9yL2hvb2tzL3VzZS1sb2FkLXN0YXRlXCI7XHJcblxyXG4vLyBJY29uIFNWRyBwYXRocyBtYXBwaW5nIGZvciBhY3R1YWwgaWNvbiByZW5kZXJpbmdcclxuY29uc3QgSUNPTl9QQVRIUzogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcclxuICBcImx1Y2lkZTpoZWFydFwiOiBcIk0yMC44NCA0LjYxYTUuNSA1LjUgMCAwIDAtNy43OCAwTDEyIDUuNjdsLTEuMDYtMS4wNmE1LjUgNS41IDAgMCAwLTcuNzggNy43OGwxLjA2IDEuMDZMMTIgMjEuMjNsNy43OC03Ljc4IDEuMDYtMS4wNmE1LjUgNS41IDAgMCAwIDAtNy43OHpcIixcclxuICBcImx1Y2lkZTpzdGFyXCI6IFwibTEyIDIgMy4wOSA2LjI2TDIyIDkuMjdsLTUgNC44NyAxLjE4IDYuODhMMTIgMTcuNzdsLTYuMTggMy4yNUw3IDE0LjE0IDIgOS4yN2w2LjkxLTEuMDFMMTIgMnpcIixcclxuICBcImx1Y2lkZTphcnJvdy1yaWdodFwiOiBcIk01IDEyaDE0bS03LTcgNyA3LTcgN1wiLFxyXG4gIFwibHVjaWRlOmFycm93LWxlZnRcIjogXCJtMTIgMTktNy03IDctN20tNyA3aDE0XCIsXHJcbiAgXCJsdWNpZGU6YXJyb3ctdXBcIjogXCJtMTggMTUtNi02LTYgNlwiLFxyXG4gIFwibHVjaWRlOmFycm93LWRvd25cIjogXCJtNiA5IDYgNiA2LTZcIixcclxuICBcImx1Y2lkZTpob21lXCI6IFwibTMgOSA5LTcgOSA3djExYTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiBNOSAyMlYxMmg2djEwXCIsXHJcbiAgXCJsdWNpZGU6dXNlclwiOiBcIk0yMCAyMXYtMmE0IDQgMCAwIDAtNC00SDhhNCA0IDAgMCAwLTQgNHYyIE0xMiAxMWE0IDQgMCAxIDAgMC04IDQgNCAwIDAgMCAwIDh6XCIsXHJcbiAgXCJsdWNpZGU6c2V0dGluZ3NcIjogXCJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDIuMThsLjIgMS44MWMtLjI2LjE0LS41MS4zLS43NC40OGwtMS44LS4zN2EyIDIgMCAwIDAtMi4yNiAxLjNsLS4yMi40NGEyIDIgMCAwIDAgLjQ3IDIuMjZsMS4zMSAxLjMxYy0uMTguMjMtLjM0LjQ4LS40OC43NGwtMS44MS4yYTIgMiAwIDAgMC0xLjgyIDJ2LjQ0YTIgMiAwIDAgMCAxLjgyIDJsMS44MS4yYy4xNC4yNi4zLjUxLjQ4Ljc0bC0xLjMxIDEuMzFhMiAyIDAgMCAwLS40NyAyLjI2bC4yMi40NGEyIDIgMCAwIDAgMi4yNiAxLjNsMS44LS4zN2MuMjMuMTguNDguMzQuNzQuNDhsLjIgMS44MWEyIDIgMCAwIDAgMiAxLjgyaC40NGEyIDIgMCAwIDAgMi0xLjgybC4yLTEuODFjLjI2LS4xNC41MS0uMy43NC0uNDhsMS44LjM3YTIgMiAwIDAgMCAyLjI2LTEuM2wuMjItLjQ0YTIgMiAwIDAgMC0uNDctMi4yNmwtMS4zMS0xLjMxYy4xOC0uMjMuMzQtLjQ4LjQ4LS43NGwxLjgxLS4yYTIgMiAwIDAgMCAxLjgyLTJ2LS40NGEyIDIgMCAwIDAtMS44Mi0ybC0xLjgxLS4yYy0uMTQtLjI2LS4zLS41MS0uNDgtLjc0bDEuMzEtMS4zMWEyIDIgMCAwIDAgLjQ3LTIuMjZsLS4yMi0uNDRhMiAyIDAgMCAwLTIuMjYtMS4zbC0xLjguMzdjLS4yMy0uMTgtLjQ4LS4zNC0uNzQtLjQ4bC0uMi0xLjgxYTIgMiAwIDAgMC0yLTEuODJaIE0xNSAxMmEzIDMgMCAxIDEtNiAwIDMgMyAwIDAgMSA2IDB6XCIsXHJcbiAgXCJsdWNpZGU6bWFpbFwiOiBcIk00IDRoMTZjMS4xIDAgMiAuOSAyIDJ2MTJjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNmMwLTEuMS45LTIgMi0yeiBtMCA0IDggNSA4LTVcIixcclxuICBcImx1Y2lkZTpwaG9uZVwiOiBcIk0yMiAxNi45MnYzYTIgMiAwIDAgMS0yLjE4IDIgMTkuNzkgMTkuNzkgMCAwIDEtOC42My0zLjA3IDE5LjUgMTkuNSAwIDAgMS02LTYgMTkuNzkgMTkuNzkgMCAwIDEtMy4wNy04LjY3QTIgMiAwIDAgMSA0LjExIDJoM2EyIDIgMCAwIDEgMiAxLjcyIDEyLjg0IDEyLjg0IDAgMCAwIC43IDIuODEgMiAyIDAgMCAxLS40NSAyLjExTDguMDkgOS45MWExNiAxNiAwIDAgMCA2IDZsMS4yNy0xLjI3YTIgMiAwIDAgMSAyLjExLS40NSAxMi44NCAxMi44NCAwIDAgMCAyLjgxLjdBMiAyIDAgMCAxIDIyIDE2LjkyelwiLFxyXG4gIFwibHVjaWRlOmNhbGVuZGFyXCI6IFwiTTggMnY0bTgtNHY0TTMgMTBoMThNNSA0aDE0YTIgMiAwIDAgMSAyIDJ2MTRhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNmEyIDIgMCAwIDEgMi0yelwiLFxyXG4gIFwibHVjaWRlOmNsb2NrXCI6IFwiTTEyIDJhMTAgMTAgMCAxIDAgMCAyMCAxMCAxMCAwIDAgMCAwLTIweiBtNSAxMC01IDNWN1wiLFxyXG4gIFwibHVjaWRlOmNhbWVyYVwiOiBcIk0xNC41IDRoLTVMNyA3SDRhMiAyIDAgMCAwLTIgMnY5YTIgMiAwIDAgMCAyIDJoMTZhMiAyIDAgMCAwIDItMlY5YTIgMiAwIDAgMC0yLTJoLTNsLTIuNS0zeiBNMTIgMTdhMyAzIDAgMSAxIDAtNiAzIDMgMCAwIDEgMCA2elwiLFxyXG4gIFwibHVjaWRlOm11c2ljXCI6IFwiTTkgMThWNWwxMi0ydjEzTTkgMTNhMyAzIDAgMSAwIDAgNiAzIDMgMCAwIDAgMC02eiBtMTItMmEzIDMgMCAxIDAgMCA2IDMgMyAwIDAgMCAwLTZ6XCIsXHJcbiAgXCJsdWNpZGU6cGxheVwiOiBcIm05IDE4IDYtNi02LTZ2MTJ6XCIsXHJcbiAgXCJsdWNpZGU6cGF1c2VcIjogXCJNNiA0aDR2MTZINnogbTggMGg0djE2aC00elwiLFxyXG4gIFwibHVjaWRlOnZvbHVtZS0yXCI6IFwiTTExIDUgNiA5SDJ2Nmg0bDUgNFY1eiBtOC41NCA4LjU0YTggOCAwIDAgMCAwLTExLjMyTTE1LjU0IDguNDZhNCA0IDAgMCAxIDAgNS42NlwiLFxyXG4gIFwibHVjaWRlOndpZmlcIjogXCJtMSA5IDIgMmM0Ljk3LTQuOTcgMTMuMDMtNC45NyAxOCAwbDItMkMxNi45MyAyLjkzIDcuMDggMi45MyAxIDl6IG00IDQgMiAyYTcuMDcgNy4wNyAwIDAgMSAxMCAwbDItMmExMC45NCAxMC45NCAwIDAgMC0xNCAweiBtNCA0IDIgMiAyLTJhMy41MyAzLjUzIDAgMCAwLTQgMHpcIixcclxuICBcImx1Y2lkZTpiYXR0ZXJ5XCI6IFwiTTE1IDdoMWEyIDIgMCAwIDEgMiAydjZhMiAyIDAgMCAxLTIgMkg4YTIgMiAwIDAgMS0yLTJWOWEyIDIgMCAwIDEgMi0yaDd6IG03IDR2MlwiLFxyXG4gIFwibHVjaWRlOmJsdWV0b290aFwiOiBcIm03IDcgMTAgNS01IDVWMmw1IDVMNyAxN1wiLFxyXG4gIFwibHVjaWRlOmRvd25sb2FkXCI6IFwiTTIxIDE1djRhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJ2LTRNNyAxMGw1IDUgNS01TTEyIDE1VjNcIixcclxuICBcImx1Y2lkZTp1cGxvYWRcIjogXCJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNE0xNyA4bC01LTUtNSA1TTEyIDN2MTJcIixcclxuICBcImx1Y2lkZTpzZWFyY2hcIjogXCJtMjEgMjEtNi02bTItNWE3IDcgMCAxIDEtMTQgMCA3IDcgMCAwIDEgMTQgMHpcIixcclxuICBcImx1Y2lkZTpwbHVzXCI6IFwiTTEyIDV2MTRtLTctN2gxNFwiLFxyXG4gIFwibHVjaWRlOm1pbnVzXCI6IFwiTTUgMTJoMTRcIixcclxuICBcImx1Y2lkZTpjaGVja1wiOiBcIm05IDEyIDIgMiA0LTRcIixcclxuICBcImx1Y2lkZTp4XCI6IFwibTE4IDYtMTIgMTJNNiA2bDEyIDEyXCIsXHJcbn07XHJcblxyXG5jb25zdCBidWlsZEVkaXRvciA9ICh7XHJcbiAgc2F2ZSxcclxuICB1bmRvLFxyXG4gIHJlZG8sXHJcbiAgY2FuUmVkbyxcclxuICBjYW5VbmRvLFxyXG4gIGF1dG9ab29tLFxyXG4gIGNvcHksXHJcbiAgcGFzdGUsXHJcbiAgY2FudmFzLFxyXG4gIGZpbGxDb2xvcixcclxuICBmb250RmFtaWx5LFxyXG4gIHNldEZvbnRGYW1pbHksXHJcbiAgc2V0RmlsbENvbG9yLFxyXG4gIHN0cm9rZUNvbG9yLFxyXG4gIHNldFN0cm9rZUNvbG9yLFxyXG4gIHN0cm9rZVdpZHRoLFxyXG4gIHNldFN0cm9rZVdpZHRoLFxyXG4gIHNlbGVjdGVkT2JqZWN0cyxcclxuICBzdHJva2VEYXNoQXJyYXksXHJcbiAgc2V0U3Ryb2tlRGFzaEFycmF5LFxyXG59OiBCdWlsZEVkaXRvclByb3BzKTogRWRpdG9yID0+IHtcclxuICBjb25zdCBnZW5lcmF0ZVNhdmVPcHRpb25zID0gKCkgPT4ge1xyXG4gICAgY29uc3QgeyB3aWR0aCwgaGVpZ2h0LCBsZWZ0LCB0b3AgfSA9IGdldFdvcmtzcGFjZSgpIGFzIGZhYnJpYy5SZWN0O1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIG5hbWU6IFwiSW1hZ2VcIixcclxuICAgICAgZm9ybWF0OiBcInBuZ1wiLFxyXG4gICAgICBxdWFsaXR5OiAxLFxyXG4gICAgICB3aWR0aCxcclxuICAgICAgaGVpZ2h0LFxyXG4gICAgICBsZWZ0LFxyXG4gICAgICB0b3AsXHJcbiAgICB9O1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHNhdmVQbmcgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBvcHRpb25zID0gZ2VuZXJhdGVTYXZlT3B0aW9ucygpO1xyXG5cclxuICAgIGNhbnZhcy5zZXRWaWV3cG9ydFRyYW5zZm9ybShbMSwgMCwgMCwgMSwgMCwgMF0pO1xyXG4gICAgY29uc3QgZGF0YVVybCA9IGNhbnZhcy50b0RhdGFVUkwob3B0aW9ucyk7XHJcblxyXG4gICAgZG93bmxvYWRGaWxlKGRhdGFVcmwsIFwicG5nXCIpO1xyXG4gICAgYXV0b1pvb20oKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzYXZlU3ZnID0gKCkgPT4ge1xyXG4gICAgY29uc3Qgb3B0aW9ucyA9IGdlbmVyYXRlU2F2ZU9wdGlvbnMoKTtcclxuXHJcbiAgICBjYW52YXMuc2V0Vmlld3BvcnRUcmFuc2Zvcm0oWzEsIDAsIDAsIDEsIDAsIDBdKTtcclxuICAgIGNvbnN0IGRhdGFVcmwgPSBjYW52YXMudG9EYXRhVVJMKG9wdGlvbnMpO1xyXG5cclxuICAgIGRvd25sb2FkRmlsZShkYXRhVXJsLCBcInN2Z1wiKTtcclxuICAgIGF1dG9ab29tKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2F2ZUpwZyA9ICgpID0+IHtcclxuICAgIGNvbnN0IG9wdGlvbnMgPSBnZW5lcmF0ZVNhdmVPcHRpb25zKCk7XHJcblxyXG4gICAgY2FudmFzLnNldFZpZXdwb3J0VHJhbnNmb3JtKFsxLCAwLCAwLCAxLCAwLCAwXSk7XHJcbiAgICBjb25zdCBkYXRhVXJsID0gY2FudmFzLnRvRGF0YVVSTChvcHRpb25zKTtcclxuXHJcbiAgICBkb3dubG9hZEZpbGUoZGF0YVVybCwgXCJqcGdcIik7XHJcbiAgICBhdXRvWm9vbSgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHNhdmVKc29uID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgZGF0YVVybCA9IGNhbnZhcy50b0pTT04oSlNPTl9LRVlTKTtcclxuXHJcbiAgICBhd2FpdCB0cmFuc2Zvcm1UZXh0KGRhdGFVcmwub2JqZWN0cyk7XHJcbiAgICBjb25zdCBmaWxlU3RyaW5nID0gYGRhdGE6dGV4dC9qc29uO2NoYXJzZXQ9dXRmLTgsJHtlbmNvZGVVUklDb21wb25lbnQoXHJcbiAgICAgIEpTT04uc3RyaW5naWZ5KGRhdGFVcmwsIG51bGwsIFwiXFx0XCIpLFxyXG4gICAgKX1gO1xyXG4gICAgZG93bmxvYWRGaWxlKGZpbGVTdHJpbmcsIFwianNvblwiKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBsb2FkSnNvbiA9IChqc29uOiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGpzb24pO1xyXG5cclxuICAgIGNhbnZhcy5sb2FkRnJvbUpTT04oZGF0YSwgKCkgPT4ge1xyXG4gICAgICBhdXRvWm9vbSgpO1xyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0V29ya3NwYWNlID0gKCkgPT4ge1xyXG4gICAgcmV0dXJuIGNhbnZhc1xyXG4gICAgLmdldE9iamVjdHMoKVxyXG4gICAgLmZpbmQoKG9iamVjdCkgPT4gb2JqZWN0Lm5hbWUgPT09IFwiY2xpcFwiKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBjZW50ZXIgPSAob2JqZWN0OiBmYWJyaWMuT2JqZWN0KSA9PiB7XHJcbiAgICBjb25zdCB3b3Jrc3BhY2UgPSBnZXRXb3Jrc3BhY2UoKTtcclxuICAgIGNvbnN0IGNlbnRlciA9IHdvcmtzcGFjZT8uZ2V0Q2VudGVyUG9pbnQoKTtcclxuXHJcbiAgICBpZiAoIWNlbnRlcikgcmV0dXJuO1xyXG5cclxuICAgIC8vIEB0cy1pZ25vcmVcclxuICAgIGNhbnZhcy5fY2VudGVyT2JqZWN0KG9iamVjdCwgY2VudGVyKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBhZGRUb0NhbnZhcyA9IChvYmplY3Q6IGZhYnJpYy5PYmplY3QpID0+IHtcclxuICAgIGNlbnRlcihvYmplY3QpO1xyXG4gICAgY2FudmFzLmFkZChvYmplY3QpO1xyXG4gICAgY2FudmFzLnNldEFjdGl2ZU9iamVjdChvYmplY3QpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBzYXZlUG5nLFxyXG4gICAgc2F2ZUpwZyxcclxuICAgIHNhdmVTdmcsXHJcbiAgICBzYXZlSnNvbixcclxuICAgIGxvYWRKc29uLFxyXG4gICAgY2FuVW5kbyxcclxuICAgIGNhblJlZG8sXHJcbiAgICBhdXRvWm9vbSxcclxuICAgIGdldFdvcmtzcGFjZSxcclxuICAgIHpvb21JbjogKCkgPT4ge1xyXG4gICAgICBsZXQgem9vbVJhdGlvID0gY2FudmFzLmdldFpvb20oKTtcclxuICAgICAgem9vbVJhdGlvICs9IDAuMDU7XHJcbiAgICAgIGNvbnN0IGNlbnRlciA9IGNhbnZhcy5nZXRDZW50ZXIoKTtcclxuICAgICAgY2FudmFzLnpvb21Ub1BvaW50KFxyXG4gICAgICAgIG5ldyBmYWJyaWMuUG9pbnQoY2VudGVyLmxlZnQsIGNlbnRlci50b3ApLFxyXG4gICAgICAgIHpvb21SYXRpbyA+IDEgPyAxIDogem9vbVJhdGlvXHJcbiAgICAgICk7XHJcbiAgICB9LFxyXG4gICAgem9vbU91dDogKCkgPT4ge1xyXG4gICAgICBsZXQgem9vbVJhdGlvID0gY2FudmFzLmdldFpvb20oKTtcclxuICAgICAgem9vbVJhdGlvIC09IDAuMDU7XHJcbiAgICAgIGNvbnN0IGNlbnRlciA9IGNhbnZhcy5nZXRDZW50ZXIoKTtcclxuICAgICAgY2FudmFzLnpvb21Ub1BvaW50KFxyXG4gICAgICAgIG5ldyBmYWJyaWMuUG9pbnQoY2VudGVyLmxlZnQsIGNlbnRlci50b3ApLFxyXG4gICAgICAgIHpvb21SYXRpbyA8IDAuMiA/IDAuMiA6IHpvb21SYXRpbyxcclxuICAgICAgKTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VTaXplOiAodmFsdWU6IHsgd2lkdGg6IG51bWJlcjsgaGVpZ2h0OiBudW1iZXIgfSkgPT4ge1xyXG4gICAgICBjb25zdCB3b3Jrc3BhY2UgPSBnZXRXb3Jrc3BhY2UoKTtcclxuXHJcbiAgICAgIHdvcmtzcGFjZT8uc2V0KHZhbHVlKTtcclxuICAgICAgYXV0b1pvb20oKTtcclxuICAgICAgc2F2ZSgpO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUJhY2tncm91bmQ6ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIGNvbnN0IHdvcmtzcGFjZSA9IGdldFdvcmtzcGFjZSgpO1xyXG4gICAgICB3b3Jrc3BhY2U/LnNldCh7IGZpbGw6IHZhbHVlIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICAgIHNhdmUoKTtcclxuICAgIH0sXHJcbiAgICBlbmFibGVEcmF3aW5nTW9kZTogKCkgPT4ge1xyXG4gICAgICBjYW52YXMuZGlzY2FyZEFjdGl2ZU9iamVjdCgpO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICAgIGNhbnZhcy5pc0RyYXdpbmdNb2RlID0gdHJ1ZTtcclxuICAgICAgY2FudmFzLmZyZWVEcmF3aW5nQnJ1c2gud2lkdGggPSBzdHJva2VXaWR0aDtcclxuICAgICAgY2FudmFzLmZyZWVEcmF3aW5nQnJ1c2guY29sb3IgPSBzdHJva2VDb2xvcjtcclxuICAgIH0sXHJcbiAgICBkaXNhYmxlRHJhd2luZ01vZGU6ICgpID0+IHtcclxuICAgICAgY2FudmFzLmlzRHJhd2luZ01vZGUgPSBmYWxzZTtcclxuICAgIH0sXHJcbiAgICBvblVuZG86ICgpID0+IHVuZG8oKSxcclxuICAgIG9uUmVkbzogKCkgPT4gcmVkbygpLFxyXG4gICAgb25Db3B5OiAoKSA9PiBjb3B5KCksXHJcbiAgICBvblBhc3RlOiAoKSA9PiBwYXN0ZSgpLFxyXG4gICAgY2hhbmdlSW1hZ2VGaWx0ZXI6ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9iamVjdHMgPSBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpO1xyXG4gICAgICBvYmplY3RzLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChvYmplY3QudHlwZSA9PT0gXCJpbWFnZVwiKSB7XHJcbiAgICAgICAgICBjb25zdCBpbWFnZU9iamVjdCA9IG9iamVjdCBhcyBmYWJyaWMuSW1hZ2U7XHJcblxyXG4gICAgICAgICAgY29uc3QgZWZmZWN0ID0gY3JlYXRlRmlsdGVyKHZhbHVlKTtcclxuXHJcbiAgICAgICAgICBpbWFnZU9iamVjdC5maWx0ZXJzID0gZWZmZWN0ID8gW2VmZmVjdF0gOiBbXTtcclxuICAgICAgICAgIGltYWdlT2JqZWN0LmFwcGx5RmlsdGVycygpO1xyXG4gICAgICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9LFxyXG4gICAgYWRkSW1hZ2U6ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIGZhYnJpYy5JbWFnZS5mcm9tVVJMKFxyXG4gICAgICAgIHZhbHVlLFxyXG4gICAgICAgIChpbWFnZSkgPT4ge1xyXG4gICAgICAgICAgY29uc3Qgd29ya3NwYWNlID0gZ2V0V29ya3NwYWNlKCk7XHJcblxyXG4gICAgICAgICAgaW1hZ2Uuc2NhbGVUb1dpZHRoKHdvcmtzcGFjZT8ud2lkdGggfHwgMCk7XHJcbiAgICAgICAgICBpbWFnZS5zY2FsZVRvSGVpZ2h0KHdvcmtzcGFjZT8uaGVpZ2h0IHx8IDApO1xyXG5cclxuICAgICAgICAgIGFkZFRvQ2FudmFzKGltYWdlKTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIGNyb3NzT3JpZ2luOiBcImFub255bW91c1wiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICk7XHJcbiAgICB9LFxyXG4gICAgZGVsZXRlOiAoKSA9PiB7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiBjYW52YXMucmVtb3ZlKG9iamVjdCkpO1xyXG4gICAgICBjYW52YXMuZGlzY2FyZEFjdGl2ZU9iamVjdCgpO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgYWRkVGV4dDogKHZhbHVlLCBvcHRpb25zKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9iamVjdCA9IG5ldyBmYWJyaWMuVGV4dGJveCh2YWx1ZSwge1xyXG4gICAgICAgIC4uLlRFWFRfT1BUSU9OUyxcclxuICAgICAgICBmaWxsOiBmaWxsQ29sb3IsXHJcbiAgICAgICAgLi4ub3B0aW9ucyxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBhZGRUb0NhbnZhcyhvYmplY3QpO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZU9wYWNpdHk6ICgpID0+IHtcclxuICAgICAgY29uc3Qgc2VsZWN0ZWRPYmplY3QgPSBzZWxlY3RlZE9iamVjdHNbMF07XHJcblxyXG4gICAgICBpZiAoIXNlbGVjdGVkT2JqZWN0KSB7XHJcbiAgICAgICAgcmV0dXJuIDE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwib3BhY2l0eVwiKSB8fCAxO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZvbnRTaXplOiAodmFsdWU6IG51bWJlcikgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIGZvbnRTaXplIGV4aXN0cy5cclxuICAgICAgICAgIG9iamVjdC5zZXQoeyBmb250U2l6ZTogdmFsdWUgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGdldEFjdGl2ZUZvbnRTaXplOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBGT05UX1NJWkU7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIGZvbnRTaXplIGV4aXN0cy5cclxuICAgICAgY29uc3QgdmFsdWUgPSBzZWxlY3RlZE9iamVjdC5nZXQoXCJmb250U2l6ZVwiKSB8fCBGT05UX1NJWkU7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlVGV4dEFsaWduOiAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIHRleHRBbGlnbiBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgdGV4dEFsaWduOiB2YWx1ZSB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlVGV4dEFsaWduOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBcImxlZnRcIjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgdGV4dEFsaWduIGV4aXN0cy5cclxuICAgICAgY29uc3QgdmFsdWUgPSBzZWxlY3RlZE9iamVjdC5nZXQoXCJ0ZXh0QWxpZ25cIikgfHwgXCJsZWZ0XCI7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlRm9udFVuZGVybGluZTogKHZhbHVlOiBib29sZWFuKSA9PiB7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgaWYgKGlzVGV4dFR5cGUob2JqZWN0LnR5cGUpKSB7XHJcbiAgICAgICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgdW5kZXJsaW5lIGV4aXN0cy5cclxuICAgICAgICAgIG9iamVjdC5zZXQoeyB1bmRlcmxpbmU6IHZhbHVlIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVGb250VW5kZXJsaW5lOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgdW5kZXJsaW5lIGV4aXN0cy5cclxuICAgICAgY29uc3QgdmFsdWUgPSBzZWxlY3RlZE9iamVjdC5nZXQoXCJ1bmRlcmxpbmVcIikgfHwgZmFsc2U7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlRm9udExpbmV0aHJvdWdoOiAodmFsdWU6IGJvb2xlYW4pID0+IHtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBpZiAoaXNUZXh0VHlwZShvYmplY3QudHlwZSkpIHtcclxuICAgICAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBsaW5ldGhyb3VnaCBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgbGluZXRocm91Z2g6IHZhbHVlIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVGb250TGluZXRocm91Z2g6ICgpID0+IHtcclxuICAgICAgY29uc3Qgc2VsZWN0ZWRPYmplY3QgPSBzZWxlY3RlZE9iamVjdHNbMF07XHJcblxyXG4gICAgICBpZiAoIXNlbGVjdGVkT2JqZWN0KSB7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBsaW5ldGhyb3VnaCBleGlzdHMuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwibGluZXRocm91Z2hcIikgfHwgZmFsc2U7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlRm9udFN0eWxlOiAodmFsdWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGlmIChpc1RleHRUeXBlKG9iamVjdC50eXBlKSkge1xyXG4gICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgLy8gRmF1bHR5IFRTIGxpYnJhcnksIGZvbnRTdHlsZSBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgZm9udFN0eWxlOiB2YWx1ZSB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlRm9udFN0eWxlOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBcIm5vcm1hbFwiO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBmb250U3R5bGUgZXhpc3RzLlxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcImZvbnRTdHlsZVwiKSB8fCBcIm5vcm1hbFwiO1xyXG5cclxuICAgICAgcmV0dXJuIHZhbHVlO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZvbnRXZWlnaHQ6ICh2YWx1ZTogbnVtYmVyKSA9PiB7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgaWYgKGlzVGV4dFR5cGUob2JqZWN0LnR5cGUpKSB7XHJcbiAgICAgICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgZm9udFdlaWdodCBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgZm9udFdlaWdodDogdmFsdWUgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZU9wYWNpdHk6ICh2YWx1ZTogbnVtYmVyKSA9PiB7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgb2JqZWN0LnNldCh7IG9wYWNpdHk6IHZhbHVlIH0pO1xyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGJyaW5nRm9yd2FyZDogKCkgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGNhbnZhcy5icmluZ0ZvcndhcmQob2JqZWN0KTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCB3b3Jrc3BhY2UgPSBnZXRXb3Jrc3BhY2UoKTtcclxuICAgICAgd29ya3NwYWNlPy5zZW5kVG9CYWNrKCk7XHJcbiAgICB9LFxyXG4gICAgc2VuZEJhY2t3YXJkczogKCkgPT4ge1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIGNhbnZhcy5zZW5kQmFja3dhcmRzKG9iamVjdCk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgICBjb25zdCB3b3Jrc3BhY2UgPSBnZXRXb3Jrc3BhY2UoKTtcclxuICAgICAgd29ya3NwYWNlPy5zZW5kVG9CYWNrKCk7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlRm9udEZhbWlseTogKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICAgICAgc2V0Rm9udEZhbWlseSh2YWx1ZSk7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgaWYgKGlzVGV4dFR5cGUob2JqZWN0LnR5cGUpKSB7XHJcbiAgICAgICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgZm9udEZhbWlseSBleGlzdHMuXHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgZm9udEZhbWlseTogdmFsdWUgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgY2FudmFzLnJlbmRlckFsbCgpO1xyXG4gICAgfSxcclxuICAgIGNoYW5nZUZpbGxDb2xvcjogKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICAgICAgc2V0RmlsbENvbG9yKHZhbHVlKTtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICBvYmplY3Quc2V0KHsgZmlsbDogdmFsdWUgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlU3Ryb2tlQ29sb3I6ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIHNldFN0cm9rZUNvbG9yKHZhbHVlKTtcclxuICAgICAgY2FudmFzLmdldEFjdGl2ZU9iamVjdHMoKS5mb3JFYWNoKChvYmplY3QpID0+IHtcclxuICAgICAgICAvLyBUZXh0IHR5cGVzIGRvbid0IGhhdmUgc3Ryb2tlXHJcbiAgICAgICAgaWYgKGlzVGV4dFR5cGUob2JqZWN0LnR5cGUpKSB7XHJcbiAgICAgICAgICBvYmplY3Quc2V0KHsgZmlsbDogdmFsdWUgfSk7XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBvYmplY3Quc2V0KHsgc3Ryb2tlOiB2YWx1ZSB9KTtcclxuICAgICAgfSk7XHJcbiAgICAgIGNhbnZhcy5mcmVlRHJhd2luZ0JydXNoLmNvbG9yID0gdmFsdWU7XHJcbiAgICAgIGNhbnZhcy5yZW5kZXJBbGwoKTtcclxuICAgIH0sXHJcbiAgICBjaGFuZ2VTdHJva2VXaWR0aDogKHZhbHVlOiBudW1iZXIpID0+IHtcclxuICAgICAgc2V0U3Ryb2tlV2lkdGgodmFsdWUpO1xyXG4gICAgICBjYW52YXMuZ2V0QWN0aXZlT2JqZWN0cygpLmZvckVhY2goKG9iamVjdCkgPT4ge1xyXG4gICAgICAgIG9iamVjdC5zZXQoeyBzdHJva2VXaWR0aDogdmFsdWUgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMuZnJlZURyYXdpbmdCcnVzaC53aWR0aCA9IHZhbHVlO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgY2hhbmdlU3Ryb2tlRGFzaEFycmF5OiAodmFsdWU6IG51bWJlcltdKSA9PiB7XHJcbiAgICAgIHNldFN0cm9rZURhc2hBcnJheSh2YWx1ZSk7XHJcbiAgICAgIGNhbnZhcy5nZXRBY3RpdmVPYmplY3RzKCkuZm9yRWFjaCgob2JqZWN0KSA9PiB7XHJcbiAgICAgICAgb2JqZWN0LnNldCh7IHN0cm9rZURhc2hBcnJheTogdmFsdWUgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgICBjYW52YXMucmVuZGVyQWxsKCk7XHJcbiAgICB9LFxyXG4gICAgYWRkQ2lyY2xlOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9iamVjdCA9IG5ldyBmYWJyaWMuQ2lyY2xlKHtcclxuICAgICAgICAuLi5DSVJDTEVfT1BUSU9OUyxcclxuICAgICAgICBmaWxsOiBmaWxsQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlOiBzdHJva2VDb2xvcixcclxuICAgICAgICBzdHJva2VXaWR0aDogc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgc3Ryb2tlRGFzaEFycmF5OiBzdHJva2VEYXNoQXJyYXksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBhZGRTb2Z0UmVjdGFuZ2xlOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IG9iamVjdCA9IG5ldyBmYWJyaWMuUmVjdCh7XHJcbiAgICAgICAgLi4uUkVDVEFOR0xFX09QVElPTlMsXHJcbiAgICAgICAgcng6IDUwLFxyXG4gICAgICAgIHJ5OiA1MCxcclxuICAgICAgICBmaWxsOiBmaWxsQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlOiBzdHJva2VDb2xvcixcclxuICAgICAgICBzdHJva2VXaWR0aDogc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgc3Ryb2tlRGFzaEFycmF5OiBzdHJva2VEYXNoQXJyYXksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBhZGRSZWN0YW5nbGU6ICgpID0+IHtcclxuICAgICAgY29uc3Qgb2JqZWN0ID0gbmV3IGZhYnJpYy5SZWN0KHtcclxuICAgICAgICAuLi5SRUNUQU5HTEVfT1BUSU9OUyxcclxuICAgICAgICBmaWxsOiBmaWxsQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlOiBzdHJva2VDb2xvcixcclxuICAgICAgICBzdHJva2VXaWR0aDogc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgc3Ryb2tlRGFzaEFycmF5OiBzdHJva2VEYXNoQXJyYXksXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBhZGRUcmlhbmdsZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBvYmplY3QgPSBuZXcgZmFicmljLlRyaWFuZ2xlKHtcclxuICAgICAgICAuLi5UUklBTkdMRV9PUFRJT05TLFxyXG4gICAgICAgIGZpbGw6IGZpbGxDb2xvcixcclxuICAgICAgICBzdHJva2U6IHN0cm9rZUNvbG9yLFxyXG4gICAgICAgIHN0cm9rZVdpZHRoOiBzdHJva2VXaWR0aCxcclxuICAgICAgICBzdHJva2VEYXNoQXJyYXk6IHN0cm9rZURhc2hBcnJheSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBhZGRUb0NhbnZhcyhvYmplY3QpO1xyXG4gICAgfSxcclxuICAgIGFkZEludmVyc2VUcmlhbmdsZTogKCkgPT4ge1xyXG4gICAgICBjb25zdCBIRUlHSFQgPSBUUklBTkdMRV9PUFRJT05TLmhlaWdodDtcclxuICAgICAgY29uc3QgV0lEVEggPSBUUklBTkdMRV9PUFRJT05TLndpZHRoO1xyXG5cclxuICAgICAgY29uc3Qgb2JqZWN0ID0gbmV3IGZhYnJpYy5Qb2x5Z29uKFxyXG4gICAgICAgIFtcclxuICAgICAgICAgIHsgeDogMCwgeTogMCB9LFxyXG4gICAgICAgICAgeyB4OiBXSURUSCwgeTogMCB9LFxyXG4gICAgICAgICAgeyB4OiBXSURUSCAvIDIsIHk6IEhFSUdIVCB9LFxyXG4gICAgICAgIF0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgLi4uVFJJQU5HTEVfT1BUSU9OUyxcclxuICAgICAgICAgIGZpbGw6IGZpbGxDb2xvcixcclxuICAgICAgICAgIHN0cm9rZTogc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgICBzdHJva2VXaWR0aDogc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgICBzdHJva2VEYXNoQXJyYXk6IHN0cm9rZURhc2hBcnJheSxcclxuICAgICAgICB9XHJcbiAgICAgICk7XHJcblxyXG4gICAgICBhZGRUb0NhbnZhcyhvYmplY3QpO1xyXG4gICAgfSxcclxuICAgIGFkZERpYW1vbmQ6ICgpID0+IHtcclxuICAgICAgY29uc3QgSEVJR0hUID0gRElBTU9ORF9PUFRJT05TLmhlaWdodDtcclxuICAgICAgY29uc3QgV0lEVEggPSBESUFNT05EX09QVElPTlMud2lkdGg7XHJcblxyXG4gICAgICBjb25zdCBvYmplY3QgPSBuZXcgZmFicmljLlBvbHlnb24oXHJcbiAgICAgICAgW1xyXG4gICAgICAgICAgeyB4OiBXSURUSCAvIDIsIHk6IDAgfSxcclxuICAgICAgICAgIHsgeDogV0lEVEgsIHk6IEhFSUdIVCAvIDIgfSxcclxuICAgICAgICAgIHsgeDogV0lEVEggLyAyLCB5OiBIRUlHSFQgfSxcclxuICAgICAgICAgIHsgeDogMCwgeTogSEVJR0hUIC8gMiB9LFxyXG4gICAgICAgIF0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgLi4uRElBTU9ORF9PUFRJT05TLFxyXG4gICAgICAgICAgZmlsbDogZmlsbENvbG9yLFxyXG4gICAgICAgICAgc3Ryb2tlOiBzdHJva2VDb2xvcixcclxuICAgICAgICAgIHN0cm9rZVdpZHRoOiBzdHJva2VXaWR0aCxcclxuICAgICAgICAgIHN0cm9rZURhc2hBcnJheTogc3Ryb2tlRGFzaEFycmF5LFxyXG4gICAgICAgIH1cclxuICAgICAgKTtcclxuICAgICAgYWRkVG9DYW52YXMob2JqZWN0KTtcclxuICAgIH0sXHJcbiAgICBjYW52YXMsXHJcbiAgICBnZXRBY3RpdmVGb250V2VpZ2h0OiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBGT05UX1dFSUdIVDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAvLyBGYXVsdHkgVFMgbGlicmFyeSwgZm9udFdlaWdodCBleGlzdHMuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwiZm9udFdlaWdodFwiKSB8fCBGT05UX1dFSUdIVDtcclxuXHJcbiAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVGb250RmFtaWx5OiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBmb250RmFtaWx5O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgIC8vIEZhdWx0eSBUUyBsaWJyYXJ5LCBmb250RmFtaWx5IGV4aXN0cy5cclxuICAgICAgY29uc3QgdmFsdWUgPSBzZWxlY3RlZE9iamVjdC5nZXQoXCJmb250RmFtaWx5XCIpIHx8IGZvbnRGYW1pbHk7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlRmlsbENvbG9yOiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBmaWxsQ29sb3I7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwiZmlsbFwiKSB8fCBmaWxsQ29sb3I7XHJcblxyXG4gICAgICAvLyBDdXJyZW50bHksIGdyYWRpZW50cyAmIHBhdHRlcm5zIGFyZSBub3Qgc3VwcG9ydGVkXHJcbiAgICAgIHJldHVybiB2YWx1ZSBhcyBzdHJpbmc7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlU3Ryb2tlQ29sb3I6ICgpID0+IHtcclxuICAgICAgY29uc3Qgc2VsZWN0ZWRPYmplY3QgPSBzZWxlY3RlZE9iamVjdHNbMF07XHJcblxyXG4gICAgICBpZiAoIXNlbGVjdGVkT2JqZWN0KSB7XHJcbiAgICAgICAgcmV0dXJuIHN0cm9rZUNvbG9yO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCB2YWx1ZSA9IHNlbGVjdGVkT2JqZWN0LmdldChcInN0cm9rZVwiKSB8fCBzdHJva2VDb2xvcjtcclxuXHJcbiAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIH0sXHJcbiAgICBnZXRBY3RpdmVTdHJva2VXaWR0aDogKCkgPT4ge1xyXG4gICAgICBjb25zdCBzZWxlY3RlZE9iamVjdCA9IHNlbGVjdGVkT2JqZWN0c1swXTtcclxuXHJcbiAgICAgIGlmICghc2VsZWN0ZWRPYmplY3QpIHtcclxuICAgICAgICByZXR1cm4gc3Ryb2tlV2lkdGg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwic3Ryb2tlV2lkdGhcIikgfHwgc3Ryb2tlV2lkdGg7XHJcblxyXG4gICAgICByZXR1cm4gdmFsdWU7XHJcbiAgICB9LFxyXG4gICAgZ2V0QWN0aXZlU3Ryb2tlRGFzaEFycmF5OiAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkT2JqZWN0ID0gc2VsZWN0ZWRPYmplY3RzWzBdO1xyXG5cclxuICAgICAgaWYgKCFzZWxlY3RlZE9iamVjdCkge1xyXG4gICAgICAgIHJldHVybiBzdHJva2VEYXNoQXJyYXk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHZhbHVlID0gc2VsZWN0ZWRPYmplY3QuZ2V0KFwic3Ryb2tlRGFzaEFycmF5XCIpIHx8IHN0cm9rZURhc2hBcnJheTtcclxuXHJcbiAgICAgIHJldHVybiB2YWx1ZTtcclxuICAgIH0sXHJcbiAgICBhZGRJY29uOiAoaWNvbk5hbWU6IHN0cmluZykgPT4ge1xyXG4gICAgICBjb25zdCBpY29uUGF0aCA9IElDT05fUEFUSFNbaWNvbk5hbWVdO1xyXG5cclxuICAgICAgaWYgKGljb25QYXRoKSB7XHJcbiAgICAgICAgLy8gQ3JlYXRlIHByb3BlciBTVkcgc3RyaW5nIGZvciB0aGUgaWNvbiB3aXRoIGJldHRlciBmb3JtYXR0aW5nXHJcbiAgICAgICAgY29uc3Qgc3ZnU3RyaW5nID0gYDxzdmcgd2lkdGg9XCI4MFwiIGhlaWdodD1cIjgwXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj48cGF0aCBkPVwiJHtpY29uUGF0aH1cIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cIiR7c3Ryb2tlQ29sb3IgfHwgJyMwMDAwMDAnfVwiIHN0cm9rZS13aWR0aD1cIjJcIiBzdHJva2UtbGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlLWxpbmVqb2luPVwicm91bmRcIi8+PC9zdmc+YDtcclxuXHJcbiAgICAgICAgLy8gTG9hZCBTVkcgYW5kIGFkZCB0byBjYW52YXNcclxuICAgICAgICBmYWJyaWMubG9hZFNWR0Zyb21TdHJpbmcoc3ZnU3RyaW5nLCAob2JqZWN0cywgb3B0aW9ucykgPT4ge1xyXG4gICAgICAgICAgY29uc3Qgc3ZnT2JqZWN0ID0gZmFicmljLnV0aWwuZ3JvdXBTVkdFbGVtZW50cyhvYmplY3RzLCBvcHRpb25zKTtcclxuICAgICAgICAgIGlmIChzdmdPYmplY3QpIHtcclxuICAgICAgICAgICAgc3ZnT2JqZWN0LnNldCh7XHJcbiAgICAgICAgICAgICAgbGVmdDogMTAwLFxyXG4gICAgICAgICAgICAgIHRvcDogMTAwLFxyXG4gICAgICAgICAgICAgIHNjYWxlWDogMSxcclxuICAgICAgICAgICAgICBzY2FsZVk6IDEsXHJcbiAgICAgICAgICAgICAgLy8gTG9jayB0aGUgaWNvbiBzbyBpdCBjYW4ndCBiZSByZXNpemVkXHJcbiAgICAgICAgICAgICAgbG9ja1NjYWxpbmdYOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGxvY2tTY2FsaW5nWTogdHJ1ZSxcclxuICAgICAgICAgICAgICBsb2NrVW5pU2NhbGluZzogdHJ1ZSxcclxuICAgICAgICAgICAgICAvLyBLZWVwIG90aGVyIHRyYW5zZm9ybWF0aW9ucyBhdmFpbGFibGVcclxuICAgICAgICAgICAgICBsb2NrTW92ZW1lbnRYOiBmYWxzZSxcclxuICAgICAgICAgICAgICBsb2NrTW92ZW1lbnRZOiBmYWxzZSxcclxuICAgICAgICAgICAgICBsb2NrUm90YXRpb246IGZhbHNlLFxyXG4gICAgICAgICAgICAgIC8vIEVuc3VyZSBpdCdzIHNlbGVjdGFibGUgYW5kIG1vdmFibGVcclxuICAgICAgICAgICAgICBzZWxlY3RhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgIGV2ZW50ZWQ6IHRydWUsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBhZGRUb0NhbnZhcyhzdmdPYmplY3QpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIEZhbGxiYWNrIGZvciB1bmtub3duIGljb25zIC0gY3JlYXRlIGEgc2ltcGxlIGljb24gcGxhY2Vob2xkZXJcclxuICAgICAgICBjb25zdCBvYmplY3QgPSBuZXcgZmFicmljLlJlY3Qoe1xyXG4gICAgICAgICAgd2lkdGg6IDgwLFxyXG4gICAgICAgICAgaGVpZ2h0OiA4MCxcclxuICAgICAgICAgIGZpbGw6ICd0cmFuc3BhcmVudCcsXHJcbiAgICAgICAgICBzdHJva2U6IHN0cm9rZUNvbG9yIHx8ICcjMDAwMDAwJyxcclxuICAgICAgICAgIHN0cm9rZVdpZHRoOiAyLFxyXG4gICAgICAgICAgc3Ryb2tlRGFzaEFycmF5OiBbNSwgNV0sXHJcbiAgICAgICAgICByeDogOCxcclxuICAgICAgICAgIHJ5OiA4LFxyXG4gICAgICAgICAgbG9ja1NjYWxpbmdYOiB0cnVlLFxyXG4gICAgICAgICAgbG9ja1NjYWxpbmdZOiB0cnVlLFxyXG4gICAgICAgICAgbG9ja1VuaVNjYWxpbmc6IHRydWUsXHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGFkZFRvQ2FudmFzKG9iamVjdCk7XHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBzZWxlY3RlZE9iamVjdHMsXHJcbiAgfTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VFZGl0b3IgPSAoe1xyXG4gIGRlZmF1bHRTdGF0ZSxcclxuICBkZWZhdWx0SGVpZ2h0LFxyXG4gIGRlZmF1bHRXaWR0aCxcclxuICBjbGVhclNlbGVjdGlvbkNhbGxiYWNrLFxyXG4gIHNhdmVDYWxsYmFjayxcclxuICBzZXRDYW52YXNJc1NlbGVjdGVkLFxyXG59OiBFZGl0b3JIb29rUHJvcHMpID0+IHtcclxuICBjb25zdCBpbml0aWFsU3RhdGUgPSB1c2VSZWYoZGVmYXVsdFN0YXRlKTtcclxuICBjb25zdCBpbml0aWFsV2lkdGggPSB1c2VSZWYoZGVmYXVsdFdpZHRoKTtcclxuICBjb25zdCBpbml0aWFsSGVpZ2h0ID0gdXNlUmVmKGRlZmF1bHRIZWlnaHQpO1xyXG5cclxuICBjb25zdCBbY2FudmFzLCBzZXRDYW52YXNdID0gdXNlU3RhdGU8ZmFicmljLkNhbnZhcyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtjb250YWluZXIsIHNldENvbnRhaW5lcl0gPSB1c2VTdGF0ZTxIVE1MRGl2RWxlbWVudCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZE9iamVjdHMsIHNldFNlbGVjdGVkT2JqZWN0c10gPSB1c2VTdGF0ZTxmYWJyaWMuT2JqZWN0W10+KFtdKTtcclxuXHJcbiAgY29uc3QgW2ZvbnRGYW1pbHksIHNldEZvbnRGYW1pbHldID0gdXNlU3RhdGUoRk9OVF9GQU1JTFkpO1xyXG4gIGNvbnN0IFtmaWxsQ29sb3IsIHNldEZpbGxDb2xvcl0gPSB1c2VTdGF0ZShGSUxMX0NPTE9SKTtcclxuICBjb25zdCBbc3Ryb2tlQ29sb3IsIHNldFN0cm9rZUNvbG9yXSA9IHVzZVN0YXRlKFNUUk9LRV9DT0xPUik7XHJcbiAgY29uc3QgW3N0cm9rZVdpZHRoLCBzZXRTdHJva2VXaWR0aF0gPSB1c2VTdGF0ZShTVFJPS0VfV0lEVEgpO1xyXG4gIGNvbnN0IFtzdHJva2VEYXNoQXJyYXksIHNldFN0cm9rZURhc2hBcnJheV0gPSB1c2VTdGF0ZTxudW1iZXJbXT4oU1RST0tFX0RBU0hfQVJSQVkpO1xyXG5cclxuICB1c2VXaW5kb3dFdmVudHMoKTtcclxuXHJcbiAgY29uc3QgeyBcclxuICAgIHNhdmUsIFxyXG4gICAgY2FuUmVkbywgXHJcbiAgICBjYW5VbmRvLCBcclxuICAgIHVuZG8sIFxyXG4gICAgcmVkbyxcclxuICAgIGNhbnZhc0hpc3RvcnksXHJcbiAgICBzZXRIaXN0b3J5SW5kZXgsXHJcbiAgfSA9IHVzZUhpc3RvcnkoeyBcclxuICAgIGNhbnZhcyxcclxuICAgIHNhdmVDYWxsYmFja1xyXG4gIH0pO1xyXG5cclxuICBjb25zdCB7IGNvcHksIHBhc3RlIH0gPSB1c2VDbGlwYm9hcmQoeyBjYW52YXMgfSk7XHJcblxyXG4gIGNvbnN0IHsgYXV0b1pvb20gfSA9IHVzZUF1dG9SZXNpemUoe1xyXG4gICAgY2FudmFzLFxyXG4gICAgY29udGFpbmVyLFxyXG4gIH0pO1xyXG5cclxuICB1c2VDYW52YXNFdmVudHMoe1xyXG4gICAgc2F2ZSxcclxuICAgIGNhbnZhcyxcclxuICAgIHNldFNlbGVjdGVkT2JqZWN0cyxcclxuICAgIGNsZWFyU2VsZWN0aW9uQ2FsbGJhY2ssXHJcbiAgICBzZXRDYW52YXNJc1NlbGVjdGVkLFxyXG4gIH0pO1xyXG5cclxuICB1c2Vab29tRXZlbnRzKHtcclxuICAgIGNhbnZhcyxcclxuICB9KTtcclxuXHJcbiAgdXNlSG90a2V5cyh7XHJcbiAgICB1bmRvLFxyXG4gICAgcmVkbyxcclxuICAgIGNvcHksXHJcbiAgICBwYXN0ZSxcclxuICAgIHNhdmUsXHJcbiAgICBjYW52YXMsXHJcbiAgfSk7XHJcblxyXG4gIHVzZUxvYWRTdGF0ZSh7XHJcbiAgICBjYW52YXMsXHJcbiAgICBhdXRvWm9vbSxcclxuICAgIGluaXRpYWxTdGF0ZSxcclxuICAgIGNhbnZhc0hpc3RvcnksXHJcbiAgICBzZXRIaXN0b3J5SW5kZXgsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IGVkaXRvciA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgaWYgKGNhbnZhcykge1xyXG4gICAgICByZXR1cm4gYnVpbGRFZGl0b3Ioe1xyXG4gICAgICAgIHNhdmUsXHJcbiAgICAgICAgdW5kbyxcclxuICAgICAgICByZWRvLFxyXG4gICAgICAgIGNhblVuZG8sXHJcbiAgICAgICAgY2FuUmVkbyxcclxuICAgICAgICBhdXRvWm9vbSxcclxuICAgICAgICBjb3B5LFxyXG4gICAgICAgIHBhc3RlLFxyXG4gICAgICAgIGNhbnZhcyxcclxuICAgICAgICBmaWxsQ29sb3IsXHJcbiAgICAgICAgc3Ryb2tlV2lkdGgsXHJcbiAgICAgICAgc3Ryb2tlQ29sb3IsXHJcbiAgICAgICAgc2V0RmlsbENvbG9yLFxyXG4gICAgICAgIHNldFN0cm9rZUNvbG9yLFxyXG4gICAgICAgIHNldFN0cm9rZVdpZHRoLFxyXG4gICAgICAgIHN0cm9rZURhc2hBcnJheSxcclxuICAgICAgICBzZWxlY3RlZE9iamVjdHMsXHJcbiAgICAgICAgc2V0U3Ryb2tlRGFzaEFycmF5LFxyXG4gICAgICAgIGZvbnRGYW1pbHksXHJcbiAgICAgICAgc2V0Rm9udEZhbWlseSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcclxuICB9LCBcclxuICBbXHJcbiAgICBjYW5SZWRvLFxyXG4gICAgY2FuVW5kbyxcclxuICAgIHVuZG8sXHJcbiAgICByZWRvLFxyXG4gICAgc2F2ZSxcclxuICAgIGF1dG9ab29tLFxyXG4gICAgY29weSxcclxuICAgIHBhc3RlLFxyXG4gICAgY2FudmFzLFxyXG4gICAgZmlsbENvbG9yLFxyXG4gICAgc3Ryb2tlV2lkdGgsXHJcbiAgICBzdHJva2VDb2xvcixcclxuICAgIHNlbGVjdGVkT2JqZWN0cyxcclxuICAgIHN0cm9rZURhc2hBcnJheSxcclxuICAgIGZvbnRGYW1pbHksXHJcbiAgXSk7XHJcblxyXG4gIGNvbnN0IGluaXQgPSB1c2VDYWxsYmFjayhcclxuICAgICh7XHJcbiAgICAgIGluaXRpYWxDYW52YXMsXHJcbiAgICAgIGluaXRpYWxDb250YWluZXIsXHJcbiAgICB9OiB7XHJcbiAgICAgIGluaXRpYWxDYW52YXM6IGZhYnJpYy5DYW52YXM7XHJcbiAgICAgIGluaXRpYWxDb250YWluZXI6IEhUTUxEaXZFbGVtZW50O1xyXG4gICAgfSkgPT4ge1xyXG4gICAgICBmYWJyaWMuT2JqZWN0LnByb3RvdHlwZS5zZXQoe1xyXG4gICAgICAgIGNvcm5lckNvbG9yOiBcIiNGRkZcIixcclxuICAgICAgICBjb3JuZXJTdHlsZTogXCJjaXJjbGVcIixcclxuICAgICAgICBib3JkZXJDb2xvcjogXCIjM2I4MmY2XCIsXHJcbiAgICAgICAgYm9yZGVyU2NhbGVGYWN0b3I6IDEuNSxcclxuICAgICAgICB0cmFuc3BhcmVudENvcm5lcnM6IGZhbHNlLFxyXG4gICAgICAgIGJvcmRlck9wYWNpdHlXaGVuTW92aW5nOiAxLFxyXG4gICAgICAgIGNvcm5lclN0cm9rZUNvbG9yOiBcIiMzYjgyZjZcIixcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zdCBpbml0aWFsV29ya3NwYWNlID0gbmV3IGZhYnJpYy5SZWN0KHtcclxuICAgICAgICB3aWR0aDogaW5pdGlhbFdpZHRoLmN1cnJlbnQsXHJcbiAgICAgICAgaGVpZ2h0OiBpbml0aWFsSGVpZ2h0LmN1cnJlbnQsXHJcbiAgICAgICAgbmFtZTogXCJjbGlwXCIsXHJcbiAgICAgICAgZmlsbDogXCJ3aGl0ZVwiLFxyXG4gICAgICAgIHNlbGVjdGFibGU6IGZhbHNlLFxyXG4gICAgICAgIGhhc0NvbnRyb2xzOiBmYWxzZSxcclxuICAgICAgICBzaGFkb3c6IG5ldyBmYWJyaWMuU2hhZG93KHtcclxuICAgICAgICAgIGNvbG9yOiBcInJnYmEoMCwwLDAsMC44KVwiLFxyXG4gICAgICAgICAgYmx1cjogNSxcclxuICAgICAgICB9KSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpbml0aWFsQ2FudmFzLnNldFdpZHRoKGluaXRpYWxDb250YWluZXIub2Zmc2V0V2lkdGgpO1xyXG4gICAgICBpbml0aWFsQ2FudmFzLnNldEhlaWdodChpbml0aWFsQ29udGFpbmVyLm9mZnNldEhlaWdodCk7XHJcblxyXG4gICAgICBpbml0aWFsQ2FudmFzLmFkZChpbml0aWFsV29ya3NwYWNlKTtcclxuICAgICAgaW5pdGlhbENhbnZhcy5jZW50ZXJPYmplY3QoaW5pdGlhbFdvcmtzcGFjZSk7XHJcbiAgICAgIGluaXRpYWxDYW52YXMuY2xpcFBhdGggPSBpbml0aWFsV29ya3NwYWNlO1xyXG5cclxuICAgICAgc2V0Q2FudmFzKGluaXRpYWxDYW52YXMpO1xyXG4gICAgICBzZXRDb250YWluZXIoaW5pdGlhbENvbnRhaW5lcik7XHJcblxyXG4gICAgICBjb25zdCBjdXJyZW50U3RhdGUgPSBKU09OLnN0cmluZ2lmeShcclxuICAgICAgICBpbml0aWFsQ2FudmFzLnRvSlNPTihKU09OX0tFWVMpXHJcbiAgICAgICk7XHJcbiAgICAgIGNhbnZhc0hpc3RvcnkuY3VycmVudCA9IFtjdXJyZW50U3RhdGVdO1xyXG4gICAgICBzZXRIaXN0b3J5SW5kZXgoMCk7XHJcbiAgICB9LFxyXG4gICAgW1xyXG4gICAgICBjYW52YXNIaXN0b3J5LCAvLyBObyBuZWVkLCB0aGlzIGlzIGZyb20gdXNlUmVmXHJcbiAgICAgIHNldEhpc3RvcnlJbmRleCwgLy8gTm8gbmVlZCwgdGhpcyBpcyBmcm9tIHVzZVN0YXRlXHJcbiAgICBdXHJcbiAgKTtcclxuXHJcbiAgcmV0dXJuIHsgaW5pdCwgZWRpdG9yIH07XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJmYWJyaWMiLCJ1c2VDYWxsYmFjayIsInVzZVN0YXRlIiwidXNlTWVtbyIsInVzZVJlZiIsIkZJTExfQ09MT1IiLCJTVFJPS0VfV0lEVEgiLCJTVFJPS0VfQ09MT1IiLCJDSVJDTEVfT1BUSU9OUyIsIkRJQU1PTkRfT1BUSU9OUyIsIlRSSUFOR0xFX09QVElPTlMiLCJSRUNUQU5HTEVfT1BUSU9OUyIsIlNUUk9LRV9EQVNIX0FSUkFZIiwiVEVYVF9PUFRJT05TIiwiRk9OVF9GQU1JTFkiLCJGT05UX1dFSUdIVCIsIkZPTlRfU0laRSIsIkpTT05fS0VZUyIsInVzZUhpc3RvcnkiLCJjcmVhdGVGaWx0ZXIiLCJkb3dubG9hZEZpbGUiLCJpc1RleHRUeXBlIiwidHJhbnNmb3JtVGV4dCIsInVzZUhvdGtleXMiLCJ1c2VDbGlwYm9hcmQiLCJ1c2VBdXRvUmVzaXplIiwidXNlQ2FudmFzRXZlbnRzIiwidXNlWm9vbUV2ZW50cyIsInVzZVdpbmRvd0V2ZW50cyIsInVzZUxvYWRTdGF0ZSIsIklDT05fUEFUSFMiLCJidWlsZEVkaXRvciIsInNhdmUiLCJ1bmRvIiwicmVkbyIsImNhblJlZG8iLCJjYW5VbmRvIiwiYXV0b1pvb20iLCJjb3B5IiwicGFzdGUiLCJjYW52YXMiLCJmaWxsQ29sb3IiLCJmb250RmFtaWx5Iiwic2V0Rm9udEZhbWlseSIsInNldEZpbGxDb2xvciIsInN0cm9rZUNvbG9yIiwic2V0U3Ryb2tlQ29sb3IiLCJzdHJva2VXaWR0aCIsInNldFN0cm9rZVdpZHRoIiwic2VsZWN0ZWRPYmplY3RzIiwic3Ryb2tlRGFzaEFycmF5Iiwic2V0U3Ryb2tlRGFzaEFycmF5IiwiZ2VuZXJhdGVTYXZlT3B0aW9ucyIsIndpZHRoIiwiaGVpZ2h0IiwibGVmdCIsInRvcCIsImdldFdvcmtzcGFjZSIsIm5hbWUiLCJmb3JtYXQiLCJxdWFsaXR5Iiwic2F2ZVBuZyIsIm9wdGlvbnMiLCJzZXRWaWV3cG9ydFRyYW5zZm9ybSIsImRhdGFVcmwiLCJ0b0RhdGFVUkwiLCJzYXZlU3ZnIiwic2F2ZUpwZyIsInNhdmVKc29uIiwidG9KU09OIiwib2JqZWN0cyIsImZpbGVTdHJpbmciLCJlbmNvZGVVUklDb21wb25lbnQiLCJKU09OIiwic3RyaW5naWZ5IiwibG9hZEpzb24iLCJqc29uIiwiZGF0YSIsInBhcnNlIiwibG9hZEZyb21KU09OIiwiZ2V0T2JqZWN0cyIsImZpbmQiLCJvYmplY3QiLCJjZW50ZXIiLCJ3b3Jrc3BhY2UiLCJnZXRDZW50ZXJQb2ludCIsIl9jZW50ZXJPYmplY3QiLCJhZGRUb0NhbnZhcyIsImFkZCIsInNldEFjdGl2ZU9iamVjdCIsInpvb21JbiIsInpvb21SYXRpbyIsImdldFpvb20iLCJnZXRDZW50ZXIiLCJ6b29tVG9Qb2ludCIsIlBvaW50Iiwiem9vbU91dCIsImNoYW5nZVNpemUiLCJ2YWx1ZSIsInNldCIsImNoYW5nZUJhY2tncm91bmQiLCJmaWxsIiwicmVuZGVyQWxsIiwiZW5hYmxlRHJhd2luZ01vZGUiLCJkaXNjYXJkQWN0aXZlT2JqZWN0IiwiaXNEcmF3aW5nTW9kZSIsImZyZWVEcmF3aW5nQnJ1c2giLCJjb2xvciIsImRpc2FibGVEcmF3aW5nTW9kZSIsIm9uVW5kbyIsIm9uUmVkbyIsIm9uQ29weSIsIm9uUGFzdGUiLCJjaGFuZ2VJbWFnZUZpbHRlciIsImdldEFjdGl2ZU9iamVjdHMiLCJmb3JFYWNoIiwidHlwZSIsImltYWdlT2JqZWN0IiwiZWZmZWN0IiwiZmlsdGVycyIsImFwcGx5RmlsdGVycyIsImFkZEltYWdlIiwiSW1hZ2UiLCJmcm9tVVJMIiwiaW1hZ2UiLCJzY2FsZVRvV2lkdGgiLCJzY2FsZVRvSGVpZ2h0IiwiY3Jvc3NPcmlnaW4iLCJkZWxldGUiLCJyZW1vdmUiLCJhZGRUZXh0IiwiVGV4dGJveCIsImdldEFjdGl2ZU9wYWNpdHkiLCJzZWxlY3RlZE9iamVjdCIsImdldCIsImNoYW5nZUZvbnRTaXplIiwiZm9udFNpemUiLCJnZXRBY3RpdmVGb250U2l6ZSIsImNoYW5nZVRleHRBbGlnbiIsInRleHRBbGlnbiIsImdldEFjdGl2ZVRleHRBbGlnbiIsImNoYW5nZUZvbnRVbmRlcmxpbmUiLCJ1bmRlcmxpbmUiLCJnZXRBY3RpdmVGb250VW5kZXJsaW5lIiwiY2hhbmdlRm9udExpbmV0aHJvdWdoIiwibGluZXRocm91Z2giLCJnZXRBY3RpdmVGb250TGluZXRocm91Z2giLCJjaGFuZ2VGb250U3R5bGUiLCJmb250U3R5bGUiLCJnZXRBY3RpdmVGb250U3R5bGUiLCJjaGFuZ2VGb250V2VpZ2h0IiwiZm9udFdlaWdodCIsImNoYW5nZU9wYWNpdHkiLCJvcGFjaXR5IiwiYnJpbmdGb3J3YXJkIiwic2VuZFRvQmFjayIsInNlbmRCYWNrd2FyZHMiLCJjaGFuZ2VGb250RmFtaWx5IiwiY2hhbmdlRmlsbENvbG9yIiwiY2hhbmdlU3Ryb2tlQ29sb3IiLCJzdHJva2UiLCJjaGFuZ2VTdHJva2VXaWR0aCIsImNoYW5nZVN0cm9rZURhc2hBcnJheSIsImFkZENpcmNsZSIsIkNpcmNsZSIsImFkZFNvZnRSZWN0YW5nbGUiLCJSZWN0IiwicngiLCJyeSIsImFkZFJlY3RhbmdsZSIsImFkZFRyaWFuZ2xlIiwiVHJpYW5nbGUiLCJhZGRJbnZlcnNlVHJpYW5nbGUiLCJIRUlHSFQiLCJXSURUSCIsIlBvbHlnb24iLCJ4IiwieSIsImFkZERpYW1vbmQiLCJnZXRBY3RpdmVGb250V2VpZ2h0IiwiZ2V0QWN0aXZlRm9udEZhbWlseSIsImdldEFjdGl2ZUZpbGxDb2xvciIsImdldEFjdGl2ZVN0cm9rZUNvbG9yIiwiZ2V0QWN0aXZlU3Ryb2tlV2lkdGgiLCJnZXRBY3RpdmVTdHJva2VEYXNoQXJyYXkiLCJhZGRJY29uIiwiaWNvbk5hbWUiLCJpY29uUGF0aCIsInN2Z1N0cmluZyIsImxvYWRTVkdGcm9tU3RyaW5nIiwic3ZnT2JqZWN0IiwidXRpbCIsImdyb3VwU1ZHRWxlbWVudHMiLCJzY2FsZVgiLCJzY2FsZVkiLCJsb2NrU2NhbGluZ1giLCJsb2NrU2NhbGluZ1kiLCJsb2NrVW5pU2NhbGluZyIsImxvY2tNb3ZlbWVudFgiLCJsb2NrTW92ZW1lbnRZIiwibG9ja1JvdGF0aW9uIiwic2VsZWN0YWJsZSIsImV2ZW50ZWQiLCJ1c2VFZGl0b3IiLCJkZWZhdWx0U3RhdGUiLCJkZWZhdWx0SGVpZ2h0IiwiZGVmYXVsdFdpZHRoIiwiY2xlYXJTZWxlY3Rpb25DYWxsYmFjayIsInNhdmVDYWxsYmFjayIsInNldENhbnZhc0lzU2VsZWN0ZWQiLCJpbml0aWFsU3RhdGUiLCJpbml0aWFsV2lkdGgiLCJpbml0aWFsSGVpZ2h0Iiwic2V0Q2FudmFzIiwiY29udGFpbmVyIiwic2V0Q29udGFpbmVyIiwic2V0U2VsZWN0ZWRPYmplY3RzIiwiY2FudmFzSGlzdG9yeSIsInNldEhpc3RvcnlJbmRleCIsImVkaXRvciIsInVuZGVmaW5lZCIsImluaXQiLCJpbml0aWFsQ2FudmFzIiwiaW5pdGlhbENvbnRhaW5lciIsIk9iamVjdCIsInByb3RvdHlwZSIsImNvcm5lckNvbG9yIiwiY29ybmVyU3R5bGUiLCJib3JkZXJDb2xvciIsImJvcmRlclNjYWxlRmFjdG9yIiwidHJhbnNwYXJlbnRDb3JuZXJzIiwiYm9yZGVyT3BhY2l0eVdoZW5Nb3ZpbmciLCJjb3JuZXJTdHJva2VDb2xvciIsImluaXRpYWxXb3Jrc3BhY2UiLCJjdXJyZW50IiwiaGFzQ29udHJvbHMiLCJzaGFkb3ciLCJTaGFkb3ciLCJibHVyIiwic2V0V2lkdGgiLCJvZmZzZXRXaWR0aCIsInNldEhlaWdodCIsIm9mZnNldEhlaWdodCIsImNlbnRlck9iamVjdCIsImNsaXBQYXRoIiwiY3VycmVudFN0YXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});