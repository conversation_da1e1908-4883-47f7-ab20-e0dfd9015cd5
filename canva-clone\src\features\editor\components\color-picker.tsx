import { ChromePicker, CirclePicker } from "react-color";

import { colors } from "@/features/editor/types";
import { rgbaObjectToString } from "@/features/editor/utils";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
};

export const ColorPicker = ({
  value,
  onChange,
}: ColorPickerProps) => {
  const handleColorChange = (color: any) => {
    const formattedValue = rgbaObjectToString(color.rgb);
    onChange(formattedValue);
  };

  const handleColorChangeComplete = (color: any) => {
    const formattedValue = rgbaObjectToString(color.rgb);
    onChange(formattedValue);
  };

  return (
    <div
      className="w-full space-y-4"
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
    >
      <ChromePicker
        color={value}
        onChange={handleColorChange}
        onChangeComplete={handleColorChangeComplete}
        className="border rounded-lg"
      />
      <CirclePicker
        color={value}
        colors={colors}
        onChange={handleColorChange}
        onChangeComplete={handleColorChangeComplete}
      />
    </div>
  );
};
