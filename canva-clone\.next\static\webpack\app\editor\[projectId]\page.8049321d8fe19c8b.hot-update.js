"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/types.ts":
/*!**************************************!*\
  !*** ./src/features/editor/types.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CIRCLE_OPTIONS: function() { return /* binding */ CIRCLE_OPTIONS; },\n/* harmony export */   DIAMOND_OPTIONS: function() { return /* binding */ DIAMOND_OPTIONS; },\n/* harmony export */   FILL_COLOR: function() { return /* binding */ FILL_COLOR; },\n/* harmony export */   FONT_FAMILY: function() { return /* binding */ FONT_FAMILY; },\n/* harmony export */   FONT_SIZE: function() { return /* binding */ FONT_SIZE; },\n/* harmony export */   FONT_WEIGHT: function() { return /* binding */ FONT_WEIGHT; },\n/* harmony export */   JSON_KEYS: function() { return /* binding */ JSON_KEYS; },\n/* harmony export */   RECTANGLE_OPTIONS: function() { return /* binding */ RECTANGLE_OPTIONS; },\n/* harmony export */   STROKE_COLOR: function() { return /* binding */ STROKE_COLOR; },\n/* harmony export */   STROKE_DASH_ARRAY: function() { return /* binding */ STROKE_DASH_ARRAY; },\n/* harmony export */   STROKE_WIDTH: function() { return /* binding */ STROKE_WIDTH; },\n/* harmony export */   TEXT_OPTIONS: function() { return /* binding */ TEXT_OPTIONS; },\n/* harmony export */   TRIANGLE_OPTIONS: function() { return /* binding */ TRIANGLE_OPTIONS; },\n/* harmony export */   colors: function() { return /* binding */ colors; },\n/* harmony export */   filters: function() { return /* binding */ filters; },\n/* harmony export */   fonts: function() { return /* binding */ fonts; },\n/* harmony export */   selectionDependentTools: function() { return /* binding */ selectionDependentTools; }\n/* harmony export */ });\n/* harmony import */ var material_colors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! material-colors */ \"(app-pages-browser)/./node_modules/material-colors/dist/colors.es2015.js\");\n\nconst JSON_KEYS = [\n    \"name\",\n    \"gradientAngle\",\n    \"selectable\",\n    \"hasControls\",\n    \"linkData\",\n    \"editable\",\n    \"extensionType\",\n    \"extension\"\n];\nconst filters = [\n    \"none\",\n    \"polaroid\",\n    \"sepia\",\n    \"kodachrome\",\n    \"contrast\",\n    \"brightness\",\n    \"greyscale\",\n    \"brownie\",\n    \"vintage\",\n    \"technicolor\",\n    \"pixelate\",\n    \"invert\",\n    \"blur\",\n    \"sharpen\",\n    \"emboss\",\n    \"removecolor\",\n    \"blacknwhite\",\n    \"vibrance\",\n    \"blendcolor\",\n    \"huerotate\",\n    \"resize\",\n    \"saturation\",\n    \"gamma\"\n];\nconst fonts = [\n    \"Arial\",\n    \"Arial Black\",\n    \"Verdana\",\n    \"Helvetica\",\n    \"Tahoma\",\n    \"Trebuchet MS\",\n    \"Times New Roman\",\n    \"Georgia\",\n    \"Garamond\",\n    \"Courier New\",\n    \"Brush Script MT\",\n    \"Palatino\",\n    \"Bookman\",\n    \"Comic Sans MS\",\n    \"Impact\",\n    \"Lucida Sans Unicode\",\n    \"Geneva\",\n    \"Lucida Console\"\n];\nconst selectionDependentTools = [\n    \"fill\",\n    \"font\",\n    \"filter\",\n    \"opacity\",\n    \"remove-bg\",\n    \"stroke-color\",\n    \"stroke-width\"\n];\nconst colors = [\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.red[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.pink[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.purple[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.deepPurple[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.indigo[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.blue[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.lightBlue[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.cyan[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.teal[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.green[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.lightGreen[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.lime[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.yellow[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.amber[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.orange[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.deepOrange[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.brown[\"500\"],\n    material_colors__WEBPACK_IMPORTED_MODULE_0__.blueGrey[\"500\"],\n    \"transparent\"\n];\nconst FILL_COLOR = \"rgba(0,0,0,1)\";\nconst STROKE_COLOR = \"rgba(0,0,0,1)\";\nconst STROKE_WIDTH = 2;\nconst STROKE_DASH_ARRAY = [];\nconst FONT_FAMILY = \"Arial\";\nconst FONT_SIZE = 32;\nconst FONT_WEIGHT = 400;\nconst CIRCLE_OPTIONS = {\n    radius: 225,\n    left: 100,\n    top: 100,\n    fill: FILL_COLOR,\n    stroke: STROKE_COLOR,\n    strokeWidth: STROKE_WIDTH\n};\nconst RECTANGLE_OPTIONS = {\n    left: 100,\n    top: 100,\n    fill: FILL_COLOR,\n    stroke: STROKE_COLOR,\n    strokeWidth: STROKE_WIDTH,\n    width: 400,\n    height: 400,\n    angle: 0\n};\nconst DIAMOND_OPTIONS = {\n    left: 100,\n    top: 100,\n    fill: FILL_COLOR,\n    stroke: STROKE_COLOR,\n    strokeWidth: STROKE_WIDTH,\n    width: 600,\n    height: 600,\n    angle: 0\n};\nconst TRIANGLE_OPTIONS = {\n    left: 100,\n    top: 100,\n    fill: FILL_COLOR,\n    stroke: STROKE_COLOR,\n    strokeWidth: STROKE_WIDTH,\n    width: 400,\n    height: 400,\n    angle: 0\n};\nconst TEXT_OPTIONS = {\n    type: \"textbox\",\n    left: 100,\n    top: 100,\n    fill: FILL_COLOR,\n    fontSize: FONT_SIZE,\n    fontFamily: FONT_FAMILY\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/types.ts\n"));

/***/ })

});