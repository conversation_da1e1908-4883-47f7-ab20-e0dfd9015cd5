import { Io<PERSON>riangle } from "react-icons/io5";
import { FaDiamond } from "react-icons/fa6";
import { FaCircle, FaSquare, FaSquareFull } from "react-icons/fa";
import { Icon } from "@iconify/react";
import { useState } from "react";

import { ActiveTool, Editor } from "@/features/editor/types";
import { ShapeTool } from "@/features/editor/components/shape-tool";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";

import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";

interface ShapeSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
};

// Define available icons from Iconify
const iconifyElements = [
  // Popular/Featured icons (shown by default)
  { name: "Heart", icon: "lucide:heart", category: "shapes", featured: true },
  { name: "Star", icon: "lucide:star", category: "shapes", featured: true },
  { name: "Arrow Right", icon: "lucide:arrow-right", category: "arrows", featured: true },
  { name: "Arrow Left", icon: "lucide:arrow-left", category: "arrows", featured: true },
  { name: "Arrow Up", icon: "lucide:arrow-up", category: "arrows", featured: true },
  { name: "Arrow Down", icon: "lucide:arrow-down", category: "arrows", featured: true },

  // Additional arrows
  { name: "Arrow Up Right", icon: "lucide:arrow-up-right", category: "arrows" },
  { name: "Arrow Up Left", icon: "lucide:arrow-up-left", category: "arrows" },
  { name: "Arrow Down Right", icon: "lucide:arrow-down-right", category: "arrows" },
  { name: "Arrow Down Left", icon: "lucide:arrow-down-left", category: "arrows" },
  { name: "Chevron Right", icon: "lucide:chevron-right", category: "arrows" },
  { name: "Chevron Left", icon: "lucide:chevron-left", category: "arrows" },
  { name: "Chevron Up", icon: "lucide:chevron-up", category: "arrows" },
  { name: "Chevron Down", icon: "lucide:chevron-down", category: "arrows" },
  { name: "Move", icon: "lucide:move", category: "arrows" },
  { name: "Rotate CW", icon: "lucide:rotate-cw", category: "arrows" },
  { name: "Rotate CCW", icon: "lucide:rotate-ccw", category: "arrows" },

  // Interface icons
  { name: "Home", icon: "lucide:home", category: "interface" },
  { name: "User", icon: "lucide:user", category: "interface" },
  { name: "Users", icon: "lucide:users", category: "interface" },
  { name: "Settings", icon: "lucide:settings", category: "interface" },
  { name: "Menu", icon: "lucide:menu", category: "interface" },
  { name: "More Horizontal", icon: "lucide:more-horizontal", category: "interface" },
  { name: "More Vertical", icon: "lucide:more-vertical", category: "interface" },
  { name: "Grid", icon: "lucide:grid-3x3", category: "interface" },
  { name: "List", icon: "lucide:list", category: "interface" },
  { name: "Layout", icon: "lucide:layout", category: "interface" },
  { name: "Sidebar", icon: "lucide:sidebar", category: "interface" },
  { name: "Panel Left", icon: "lucide:panel-left", category: "interface" },
  { name: "Panel Right", icon: "lucide:panel-right", category: "interface" },

  // Communication
  { name: "Mail", icon: "lucide:mail", category: "communication" },
  { name: "Message Circle", icon: "lucide:message-circle", category: "communication" },
  { name: "Message Square", icon: "lucide:message-square", category: "communication" },
  { name: "Phone", icon: "lucide:phone", category: "communication" },
  { name: "Phone Call", icon: "lucide:phone-call", category: "communication" },
  { name: "Video", icon: "lucide:video", category: "communication" },
  { name: "Send", icon: "lucide:send", category: "communication" },
  { name: "Share", icon: "lucide:share", category: "communication" },
  { name: "Share 2", icon: "lucide:share-2", category: "communication" },

  // Time & Calendar
  { name: "Calendar", icon: "lucide:calendar", category: "time" },
  { name: "Calendar Days", icon: "lucide:calendar-days", category: "time" },
  { name: "Clock", icon: "lucide:clock", category: "time" },
  { name: "Timer", icon: "lucide:timer", category: "time" },
  { name: "Alarm Clock", icon: "lucide:alarm-clock", category: "time" },
  { name: "Hourglass", icon: "lucide:hourglass", category: "time" },

  // Media & Entertainment
  { name: "Camera", icon: "lucide:camera", category: "media" },
  { name: "Image", icon: "lucide:image", category: "media" },
  { name: "Images", icon: "lucide:images", category: "media" },
  { name: "Video Camera", icon: "lucide:video", category: "media" },
  { name: "Music", icon: "lucide:music", category: "media" },
  { name: "Play", icon: "lucide:play", category: "media" },
  { name: "Pause", icon: "lucide:pause", category: "media" },
  { name: "Stop", icon: "lucide:square", category: "media" },
  { name: "Skip Forward", icon: "lucide:skip-forward", category: "media" },
  { name: "Skip Back", icon: "lucide:skip-back", category: "media" },
  { name: "Fast Forward", icon: "lucide:fast-forward", category: "media" },
  { name: "Rewind", icon: "lucide:rewind", category: "media" },
  { name: "Volume", icon: "lucide:volume-2", category: "media" },
  { name: "Volume Off", icon: "lucide:volume-x", category: "media" },
  { name: "Volume Low", icon: "lucide:volume-1", category: "media" },
  { name: "Headphones", icon: "lucide:headphones", category: "media" },
  { name: "Mic", icon: "lucide:mic", category: "media" },
  { name: "Mic Off", icon: "lucide:mic-off", category: "media" },

  // Technology
  { name: "Wifi", icon: "lucide:wifi", category: "tech" },
  { name: "Wifi Off", icon: "lucide:wifi-off", category: "tech" },
  { name: "Battery", icon: "lucide:battery", category: "tech" },
  { name: "Battery Low", icon: "lucide:battery-low", category: "tech" },
  { name: "Bluetooth", icon: "lucide:bluetooth", category: "tech" },
  { name: "Smartphone", icon: "lucide:smartphone", category: "tech" },
  { name: "Laptop", icon: "lucide:laptop", category: "tech" },
  { name: "Monitor", icon: "lucide:monitor", category: "tech" },
  { name: "Tablet", icon: "lucide:tablet", category: "tech" },
  { name: "Hard Drive", icon: "lucide:hard-drive", category: "tech" },
  { name: "Server", icon: "lucide:server", category: "tech" },
  { name: "Database", icon: "lucide:database", category: "tech" },
  { name: "Cloud", icon: "lucide:cloud", category: "tech" },
  { name: "Globe", icon: "lucide:globe", category: "tech" },

  // Actions & Controls
  { name: "Download", icon: "lucide:download", category: "actions" },
  { name: "Upload", icon: "lucide:upload", category: "actions" },
  { name: "Search", icon: "lucide:search", category: "actions" },
  { name: "Plus", icon: "lucide:plus", category: "actions" },
  { name: "Minus", icon: "lucide:minus", category: "actions" },
  { name: "Check", icon: "lucide:check", category: "actions" },
  { name: "X", icon: "lucide:x", category: "actions" },
  { name: "Edit", icon: "lucide:edit", category: "actions" },
  { name: "Edit 2", icon: "lucide:edit-2", category: "actions" },
  { name: "Edit 3", icon: "lucide:edit-3", category: "actions" },
  { name: "Trash", icon: "lucide:trash", category: "actions" },
  { name: "Trash 2", icon: "lucide:trash-2", category: "actions" },
  { name: "Copy", icon: "lucide:copy", category: "actions" },
  { name: "Cut", icon: "lucide:scissors", category: "actions" },
  { name: "Paste", icon: "lucide:clipboard", category: "actions" },
  { name: "Save", icon: "lucide:save", category: "actions" },
  { name: "Undo", icon: "lucide:undo", category: "actions" },
  { name: "Redo", icon: "lucide:redo", category: "actions" },
  { name: "Refresh", icon: "lucide:refresh-cw", category: "actions" },
  { name: "Power", icon: "lucide:power", category: "actions" },
  { name: "Lock", icon: "lucide:lock", category: "actions" },
  { name: "Unlock", icon: "lucide:unlock", category: "actions" },
  { name: "Eye", icon: "lucide:eye", category: "actions" },
  { name: "Eye Off", icon: "lucide:eye-off", category: "actions" },

  // Shapes & Symbols
  { name: "Circle", icon: "lucide:circle", category: "shapes" },
  { name: "Square", icon: "lucide:square", category: "shapes" },
  { name: "Triangle", icon: "lucide:triangle", category: "shapes" },
  { name: "Hexagon", icon: "lucide:hexagon", category: "shapes" },
  { name: "Octagon", icon: "lucide:octagon", category: "shapes" },
  { name: "Diamond", icon: "lucide:diamond", category: "shapes" },
  { name: "Pentagon", icon: "lucide:pentagon", category: "shapes" },
  { name: "Bookmark", icon: "lucide:bookmark", category: "shapes" },
  { name: "Tag", icon: "lucide:tag", category: "shapes" },
  { name: "Flag", icon: "lucide:flag", category: "shapes" },
  { name: "Shield", icon: "lucide:shield", category: "shapes" },
  { name: "Award", icon: "lucide:award", category: "shapes" },
  { name: "Medal", icon: "lucide:medal", category: "shapes" },
  { name: "Crown", icon: "lucide:crown", category: "shapes" },
  { name: "Gem", icon: "lucide:gem", category: "shapes" },

  // Weather & Nature
  { name: "Sun", icon: "lucide:sun", category: "weather" },
  { name: "Moon", icon: "lucide:moon", category: "weather" },
  { name: "Cloud", icon: "lucide:cloud", category: "weather" },
  { name: "Cloud Rain", icon: "lucide:cloud-rain", category: "weather" },
  { name: "Cloud Snow", icon: "lucide:cloud-snow", category: "weather" },
  { name: "Lightning", icon: "lucide:zap", category: "weather" },
  { name: "Umbrella", icon: "lucide:umbrella", category: "weather" },
  { name: "Thermometer", icon: "lucide:thermometer", category: "weather" },
  { name: "Wind", icon: "lucide:wind", category: "weather" },
  { name: "Tree", icon: "lucide:tree-pine", category: "nature" },
  { name: "Leaf", icon: "lucide:leaf", category: "nature" },
  { name: "Flower", icon: "lucide:flower", category: "nature" },
  { name: "Sprout", icon: "lucide:sprout", category: "nature" },

  // Transportation
  { name: "Car", icon: "lucide:car", category: "transport" },
  { name: "Truck", icon: "lucide:truck", category: "transport" },
  { name: "Bus", icon: "lucide:bus", category: "transport" },
  { name: "Bike", icon: "lucide:bike", category: "transport" },
  { name: "Plane", icon: "lucide:plane", category: "transport" },
  { name: "Train", icon: "lucide:train", category: "transport" },
  { name: "Ship", icon: "lucide:ship", category: "transport" },
  { name: "Fuel", icon: "lucide:fuel", category: "transport" },
  { name: "Map Pin", icon: "lucide:map-pin", category: "transport" },
  { name: "Navigation", icon: "lucide:navigation", category: "transport" },
  { name: "Compass", icon: "lucide:compass", category: "transport" },

  // Food & Drink
  { name: "Coffee", icon: "lucide:coffee", category: "food" },
  { name: "Cup", icon: "lucide:cup-soda", category: "food" },
  { name: "Wine", icon: "lucide:wine", category: "food" },
  { name: "Beer", icon: "lucide:beer", category: "food" },
  { name: "Pizza", icon: "lucide:pizza", category: "food" },
  { name: "Apple", icon: "lucide:apple", category: "food" },
  { name: "Cherry", icon: "lucide:cherry", category: "food" },
  { name: "Cake", icon: "lucide:cake", category: "food" },
  { name: "Ice Cream", icon: "lucide:ice-cream", category: "food" },

  // Business & Finance
  { name: "Briefcase", icon: "lucide:briefcase", category: "business" },
  { name: "Building", icon: "lucide:building", category: "business" },
  { name: "Building 2", icon: "lucide:building-2", category: "business" },
  { name: "Store", icon: "lucide:store", category: "business" },
  { name: "Factory", icon: "lucide:factory", category: "business" },
  { name: "Banknote", icon: "lucide:banknote", category: "finance" },
  { name: "Credit Card", icon: "lucide:credit-card", category: "finance" },
  { name: "Wallet", icon: "lucide:wallet", category: "finance" },
  { name: "Coins", icon: "lucide:coins", category: "finance" },
  { name: "Dollar Sign", icon: "lucide:dollar-sign", category: "finance" },
  { name: "Trending Up", icon: "lucide:trending-up", category: "finance" },
  { name: "Trending Down", icon: "lucide:trending-down", category: "finance" },
  { name: "Bar Chart", icon: "lucide:bar-chart", category: "finance" },
  { name: "Line Chart", icon: "lucide:line-chart", category: "finance" },
  { name: "Pie Chart", icon: "lucide:pie-chart", category: "finance" },

  // Health & Medical
  { name: "Heart Pulse", icon: "lucide:heart-pulse", category: "health" },
  { name: "Activity", icon: "lucide:activity", category: "health" },
  { name: "Pill", icon: "lucide:pill", category: "health" },
  { name: "Stethoscope", icon: "lucide:stethoscope", category: "health" },
  { name: "Cross", icon: "lucide:cross", category: "health" },
  { name: "Band Aid", icon: "lucide:bandage", category: "health" },

  // Sports & Games
  { name: "Trophy", icon: "lucide:trophy", category: "sports" },
  { name: "Target", icon: "lucide:target", category: "sports" },
  { name: "Dumbbell", icon: "lucide:dumbbell", category: "sports" },
  { name: "Football", icon: "lucide:football", category: "sports" },
  { name: "Gamepad", icon: "lucide:gamepad-2", category: "games" },
  { name: "Dice", icon: "lucide:dice-1", category: "games" },
  { name: "Puzzle", icon: "lucide:puzzle", category: "games" },

  // Tools & Utilities
  { name: "Wrench", icon: "lucide:wrench", category: "tools" },
  { name: "Hammer", icon: "lucide:hammer", category: "tools" },
  { name: "Screwdriver", icon: "lucide:screwdriver", category: "tools" },
  { name: "Paintbrush", icon: "lucide:paintbrush", category: "tools" },
  { name: "Palette", icon: "lucide:palette", category: "tools" },
  { name: "Ruler", icon: "lucide:ruler", category: "tools" },
  { name: "Calculator", icon: "lucide:calculator", category: "tools" },
  { name: "Flashlight", icon: "lucide:flashlight", category: "tools" },
  { name: "Key", icon: "lucide:key", category: "tools" },
  { name: "Magnet", icon: "lucide:magnet", category: "tools" },
];

export const ShapeSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: ShapeSidebarProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const onClose = () => {
    onChangeActiveTool("select");
  };

  // Filter icons based on search term
  const filteredIcons = searchTerm
    ? iconifyElements.filter(element =>
        element.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        element.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : iconifyElements.filter(element => element.featured);

  // Get display icons (6 featured by default, all filtered when searching)
  const displayIcons = searchTerm ? filteredIcons : filteredIcons.slice(0, 6);

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "shapes" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="Elements"
        description="Add elements to your canvas"
      />

      {/* Search Bar */}
      <div className="p-4 border-b">
        <Input
          placeholder="Search elements..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full"
        />
      </div>

      <ScrollArea>
        {/* Basic Shapes Section */}
        <div className="p-4">
          <h3 className="text-sm font-medium mb-3 text-gray-700">Basic Shapes</h3>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <ShapeTool
              onClick={() => editor?.addCircle()}
              icon={FaCircle}
            />
            <ShapeTool
              onClick={() => editor?.addSoftRectangle()}
              icon={FaSquare}
            />
            <ShapeTool
              onClick={() => editor?.addRectangle()}
              icon={FaSquareFull}
            />
            <ShapeTool
              onClick={() => editor?.addTriangle()}
              icon={IoTriangle}
            />
            <ShapeTool
              onClick={() => editor?.addInverseTriangle()}
              icon={IoTriangle}
              iconClassName="rotate-180"
            />
            <ShapeTool
              onClick={() => editor?.addDiamond()}
              icon={FaDiamond}
            />
          </div>

          {/* Iconify Elements Section */}
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-700">Icons & Elements</h3>
            {searchTerm && (
              <span className="text-xs text-gray-500">
                {filteredIcons.length} found
              </span>
            )}
            {!searchTerm && (
              <span className="text-xs text-gray-500">
                {iconifyElements.length - 6} more available
              </span>
            )}
          </div>
          <div className="grid grid-cols-3 gap-4">
            {displayIcons.map((element) => (
              <button
                key={element.name}
                onClick={() => {
                  // Add the icon as an SVG element to the canvas
                  // This is a placeholder - you might need to implement addIcon method in your editor
                  console.log(`Adding ${element.name} icon`);
                }}
                className="aspect-square border rounded-md p-3 hover:bg-gray-50 transition-colors flex items-center justify-center group"
                title={element.name}
              >
                <Icon
                  icon={element.icon}
                  className="h-6 w-6 text-gray-700 group-hover:text-gray-900"
                />
              </button>
            ))}
          </div>

          {/* Search hint when no search term */}
          {!searchTerm && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-xs text-gray-600 text-center">
                🔍 Search to discover {iconifyElements.length - 6} more icons across categories like weather, food, business, and more!
              </p>
            </div>
          )}

          {/* No results message */}
          {searchTerm && filteredIcons.length === 0 && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-xs text-gray-600 text-center">
                No icons found for "{searchTerm}". Try searching for categories like "arrow", "weather", "food", or "business".
              </p>
            </div>
          )}
        </div>
      </ScrollArea>
      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
