import { Io<PERSON>riangle } from "react-icons/io5";
import { FaDiamond } from "react-icons/fa6";
import { FaCircle, FaSquare, FaSquareFull } from "react-icons/fa";
import { Icon } from "@iconify/react";
import { useState } from "react";

import { ActiveTool, Editor } from "@/features/editor/types";
import { ShapeTool } from "@/features/editor/components/shape-tool";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";

import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";

interface ShapeSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
};

// Define available icons from Iconify
const iconifyElements = [
  { name: "Heart", icon: "lucide:heart", category: "shapes" },
  { name: "Star", icon: "lucide:star", category: "shapes" },
  { name: "Arrow Right", icon: "lucide:arrow-right", category: "arrows" },
  { name: "Arrow Left", icon: "lucide:arrow-left", category: "arrows" },
  { name: "Arrow Up", icon: "lucide:arrow-up", category: "arrows" },
  { name: "Arrow Down", icon: "lucide:arrow-down", category: "arrows" },
  { name: "Home", icon: "lucide:home", category: "interface" },
  { name: "User", icon: "lucide:user", category: "interface" },
  { name: "Settings", icon: "lucide:settings", category: "interface" },
  { name: "Mail", icon: "lucide:mail", category: "interface" },
  { name: "Phone", icon: "lucide:phone", category: "interface" },
  { name: "Calendar", icon: "lucide:calendar", category: "interface" },
  { name: "Clock", icon: "lucide:clock", category: "interface" },
  { name: "Camera", icon: "lucide:camera", category: "interface" },
  { name: "Music", icon: "lucide:music", category: "media" },
  { name: "Play", icon: "lucide:play", category: "media" },
  { name: "Pause", icon: "lucide:pause", category: "media" },
  { name: "Volume", icon: "lucide:volume-2", category: "media" },
  { name: "Wifi", icon: "lucide:wifi", category: "tech" },
  { name: "Battery", icon: "lucide:battery", category: "tech" },
  { name: "Bluetooth", icon: "lucide:bluetooth", category: "tech" },
  { name: "Download", icon: "lucide:download", category: "actions" },
  { name: "Upload", icon: "lucide:upload", category: "actions" },
  { name: "Search", icon: "lucide:search", category: "actions" },
  { name: "Plus", icon: "lucide:plus", category: "actions" },
  { name: "Minus", icon: "lucide:minus", category: "actions" },
  { name: "Check", icon: "lucide:check", category: "actions" },
  { name: "X", icon: "lucide:x", category: "actions" },
];

export const ShapeSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: ShapeSidebarProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const onClose = () => {
    onChangeActiveTool("select");
  };

  // Filter icons based on search term
  const filteredIcons = iconifyElements.filter(element =>
    element.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    element.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "shapes" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="Elements"
        description="Add elements to your canvas"
      />

      {/* Search Bar */}
      <div className="p-4 border-b">
        <Input
          placeholder="Search elements..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full"
        />
      </div>

      <ScrollArea>
        {/* Basic Shapes Section */}
        <div className="p-4">
          <h3 className="text-sm font-medium mb-3 text-gray-700">Basic Shapes</h3>
          <div className="grid grid-cols-3 gap-4 mb-6">
            <ShapeTool
              onClick={() => editor?.addCircle()}
              icon={FaCircle}
            />
            <ShapeTool
              onClick={() => editor?.addSoftRectangle()}
              icon={FaSquare}
            />
            <ShapeTool
              onClick={() => editor?.addRectangle()}
              icon={FaSquareFull}
            />
            <ShapeTool
              onClick={() => editor?.addTriangle()}
              icon={IoTriangle}
            />
            <ShapeTool
              onClick={() => editor?.addInverseTriangle()}
              icon={IoTriangle}
              iconClassName="rotate-180"
            />
            <ShapeTool
              onClick={() => editor?.addDiamond()}
              icon={FaDiamond}
            />
          </div>

          {/* Iconify Elements Section */}
          <h3 className="text-sm font-medium mb-3 text-gray-700">Icons & Elements</h3>
          <div className="grid grid-cols-3 gap-4">
            {filteredIcons.map((element) => (
              <button
                key={element.name}
                onClick={() => {
                  // Add the icon as an SVG element to the canvas
                  // This is a placeholder - you might need to implement addIcon method in your editor
                  console.log(`Adding ${element.name} icon`);
                }}
                className="aspect-square border rounded-md p-3 hover:bg-gray-50 transition-colors flex items-center justify-center group"
                title={element.name}
              >
                <Icon
                  icon={element.icon}
                  className="h-6 w-6 text-gray-700 group-hover:text-gray-900"
                />
              </button>
            ))}
          </div>
        </div>
      </ScrollArea>
      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
