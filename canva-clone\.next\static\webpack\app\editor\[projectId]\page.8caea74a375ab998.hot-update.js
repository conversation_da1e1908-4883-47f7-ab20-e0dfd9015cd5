"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        addIcon: (iconName)=>{\n            // Create a simple SVG path for the icon\n            // This is a simplified approach - in a real implementation, you'd want to\n            // fetch the actual SVG data from Iconify\n            const svgString = '\\n        <svg width=\"100\" height=\"100\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n          <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2\" stroke=\"'.concat(strokeColor, '\" stroke-width=\"').concat(strokeWidth, '\" fill=\"').concat(fillColor, '\"/>\\n          <text x=\"12\" y=\"16\" text-anchor=\"middle\" font-size=\"8\" fill=\"').concat(strokeColor, '\">ICON</text>\\n        </svg>\\n      ');\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.loadSVGFromString(svgString, (objects, options)=>{\n                const obj = fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.util.groupSVGElements(objects, options);\n                obj.set({\n                    left: 0,\n                    top: 0,\n                    scaleX: 1,\n                    scaleY: 1\n                });\n                addToCanvas(obj);\n            });\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback, setCanvasIsSelected } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback,\n        setCanvasIsSelected\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});