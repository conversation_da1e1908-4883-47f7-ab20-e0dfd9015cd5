"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts":
/*!*****************************************************!*\
  !*** ./src/features/editor/hooks/use-load-state.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLoadState: function() { return /* binding */ useLoadState; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n\n\nconst useLoadState = (param)=>{\n    let { canvas, autoZoom, initialState, canvasHistory, setHistoryIndex } = param;\n    const initialized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!initialized.current && (initialState === null || initialState === void 0 ? void 0 : initialState.current) && canvas) {\n            try {\n                const data = JSON.parse(initialState.current);\n                // Double-check canvas is still available and has loadFromJSON method\n                if (canvas && typeof canvas.loadFromJSON === \"function\") {\n                    canvas.loadFromJSON(data, ()=>{\n                        // Triple-check canvas is still available in callback\n                        if (canvas && typeof canvas.toJSON === \"function\") {\n                            const currentState = JSON.stringify(canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_1__.JSON_KEYS));\n                            canvasHistory.current = [\n                                currentState\n                            ];\n                            setHistoryIndex(0);\n                            autoZoom();\n                        }\n                    });\n                    initialized.current = true;\n                }\n            } catch (error) {\n                console.error(\"Error loading canvas state:\", error);\n                // If there's an error, still mark as initialized to prevent infinite retries\n                initialized.current = true;\n            }\n        }\n    }, [\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    ]);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\n"));

/***/ })

});