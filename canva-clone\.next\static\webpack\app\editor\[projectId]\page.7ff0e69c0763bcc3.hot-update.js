"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/stroke-color-sidebar.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StrokeColorSidebar: function() { return /* binding */ StrokeColorSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _features_editor_components_color_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/color-picker */ \"(app-pages-browser)/./src/features/editor/components/color-picker.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n\n\n\n\n\n\n\nconst StrokeColorSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    const selectedObject = editor === null || editor === void 0 ? void 0 : editor.selectedObjects[0];\n    const isIcon = (selectedObject === null || selectedObject === void 0 ? void 0 : selectedObject.type) === \"icon\";\n    // Get the appropriate color value based on object type\n    const value = isIcon ? (editor === null || editor === void 0 ? void 0 : editor.getActiveIconColor()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_1__.STROKE_COLOR : (editor === null || editor === void 0 ? void 0 : editor.getActiveStrokeColor()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_1__.STROKE_COLOR;\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const onChange = (value)=>{\n        if (isIcon) {\n            editor === null || editor === void 0 ? void 0 : editor.changeIconColor(value);\n        } else {\n            editor === null || editor === void 0 ? void 0 : editor.changeStrokeColor(value);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"stroke-color\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_3__.ToolSidebarHeader, {\n                title: \"Stroke color\",\n                description: \"Add stroke color to your element\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_color_picker__WEBPACK_IMPORTED_MODULE_4__.ColorPicker, {\n                        value: value,\n                        onChange: onChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_2__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StrokeColorSidebar;\nvar _c;\n$RefreshReg$(_c, \"StrokeColorSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\n"));

/***/ })

});