import { fabric } from "fabric";
import { useCallback, useState, useMemo, useRef } from "react";

import { 
  Editor, 
  FILL_COLOR,
  STROKE_WIDTH,
  STROKE_COLOR,
  CIRCLE_OPTIONS,
  DIAMOND_OPTIONS,
  TRIANGLE_OPTIONS,
  BuildEditorProps, 
  RECTANGLE_OPTIONS,
  EditorHookProps,
  STROKE_DASH_ARRAY,
  TEXT_OPTIONS,
  FONT_FAMILY,
  FONT_WEIGHT,
  FONT_SIZE,
  JSON_KEYS,
} from "@/features/editor/types";
import { useHistory } from "@/features/editor/hooks/use-history";
import { 
  createFilter, 
  downloadFile, 
  isTextType,
  transformText
} from "@/features/editor/utils";
import { useHotkeys } from "@/features/editor/hooks/use-hotkeys";
import { useClipboard } from "@/features/editor/hooks//use-clipboard";
import { useAutoResize } from "@/features/editor/hooks/use-auto-resize";
import { useCanvasEvents } from "@/features/editor/hooks/use-canvas-events";
import { useZoomEvents } from "@/features/editor/hooks/use-zoom-events";
import { useWindowEvents } from "@/features/editor/hooks/use-window-events";
import { useLoadState } from "@/features/editor/hooks/use-load-state";
import * as LucideIcons from "lucide-react";

// Comprehensive icon mapping using Lucide React icons
const ICON_COMPONENTS: Record<string, React.ComponentType<any>> = {
  "lucide:heart": LucideIcons.Heart,
  "lucide:star": LucideIcons.Star,
  "lucide:arrow-right": LucideIcons.ArrowRight,
  "lucide:arrow-left": LucideIcons.ArrowLeft,
  "lucide:arrow-up": LucideIcons.ArrowUp,
  "lucide:arrow-down": LucideIcons.ArrowDown,
  "lucide:home": LucideIcons.Home,
  "lucide:user": LucideIcons.User,
  "lucide:settings": LucideIcons.Settings,
  "lucide:mail": LucideIcons.Mail,
  "lucide:phone": LucideIcons.Phone,
  "lucide:car": LucideIcons.Car,
  "lucide:camera": LucideIcons.Camera,
  "lucide:music": LucideIcons.Music,
  "lucide:video": LucideIcons.Video,
  "lucide:image": LucideIcons.Image,
  "lucide:file": LucideIcons.File,
  "lucide:folder": LucideIcons.Folder,
  "lucide:search": LucideIcons.Search,
  "lucide:plus": LucideIcons.Plus,
  "lucide:minus": LucideIcons.Minus,
  "lucide:x": LucideIcons.X,
  "lucide:check": LucideIcons.Check,
  "lucide:edit": LucideIcons.Edit,
  "lucide:trash": LucideIcons.Trash2,
  "lucide:download": LucideIcons.Download,
  "lucide:upload": LucideIcons.Upload,
  "lucide:share": LucideIcons.Share,
  "lucide:copy": LucideIcons.Copy,
  "lucide:save": LucideIcons.Save,
  "lucide:print": LucideIcons.Printer,
  "lucide:calendar": LucideIcons.Calendar,
  "lucide:clock": LucideIcons.Clock,
  "lucide:map": LucideIcons.Map,
  "lucide:globe": LucideIcons.Globe,
  "lucide:wifi": LucideIcons.Wifi,
  "lucide:battery": LucideIcons.Battery,
  "lucide:volume": LucideIcons.Volume2,
  "lucide:play": LucideIcons.Play,
  "lucide:pause": LucideIcons.Pause,
  "lucide:stop": LucideIcons.Square,
  "lucide:skip-forward": LucideIcons.SkipForward,
  "lucide:skip-back": LucideIcons.SkipBack,
  "lucide:refresh": LucideIcons.RefreshCw,
  "lucide:maximize": LucideIcons.Maximize,
  "lucide:minimize": LucideIcons.Minimize,
  "lucide:eye": LucideIcons.Eye,
  "lucide:eye-off": LucideIcons.EyeOff,
  "lucide:lock": LucideIcons.Lock,
  "lucide:unlock": LucideIcons.Unlock,
  "lucide:shield": LucideIcons.Shield,
  "lucide:alert": LucideIcons.AlertTriangle,
  "lucide:info": LucideIcons.Info,
  "lucide:help": LucideIcons.HelpCircle,
  "lucide:bookmark": LucideIcons.Bookmark,
  "lucide:tag": LucideIcons.Tag,
  "lucide:flag": LucideIcons.Flag,
  "lucide:thumbs-up": LucideIcons.ThumbsUp,
  "lucide:thumbs-down": LucideIcons.ThumbsDown,
  "lucide:message": LucideIcons.MessageCircle,
  "lucide:send": LucideIcons.Send,
  "lucide:bell": LucideIcons.Bell,
  "lucide:gift": LucideIcons.Gift,
  "lucide:shopping-cart": LucideIcons.ShoppingCart,
  "lucide:credit-card": LucideIcons.CreditCard,
  "lucide:dollar-sign": LucideIcons.DollarSign,
  "lucide:trending-up": LucideIcons.TrendingUp,
  "lucide:trending-down": LucideIcons.TrendingDown,
  "lucide:bar-chart": LucideIcons.BarChart3,
  "lucide:pie-chart": LucideIcons.PieChart,
  "lucide:activity": LucideIcons.Activity,
  "lucide:zap": LucideIcons.Zap,
  "lucide:sun": LucideIcons.Sun,
  "lucide:moon": LucideIcons.Moon,
  "lucide:cloud": LucideIcons.Cloud,
  "lucide:umbrella": LucideIcons.Umbrella,
  "lucide:snowflake": LucideIcons.Snowflake,
  "lucide:droplet": LucideIcons.Droplets,
  "lucide:flame": LucideIcons.Flame,
  "lucide:leaf": LucideIcons.Leaf,
  "lucide:tree": LucideIcons.Trees,
  "lucide:flower": LucideIcons.Flower,
  "lucide:coffee": LucideIcons.Coffee,
  "lucide:pizza": LucideIcons.Pizza,
  "lucide:utensils": LucideIcons.Utensils,
  "lucide:wine": LucideIcons.Wine,
  "lucide:gamepad": LucideIcons.Gamepad2,
  "lucide:headphones": LucideIcons.Headphones,
  "lucide:smartphone": LucideIcons.Smartphone,
  "lucide:laptop": LucideIcons.Laptop,
  "lucide:monitor": LucideIcons.Monitor,
  "lucide:keyboard": LucideIcons.Keyboard,
  "lucide:mouse": LucideIcons.Mouse,
  "lucide:printer": LucideIcons.Printer,
  "lucide:hard-drive": LucideIcons.HardDrive,
  "lucide:usb": LucideIcons.Usb,
  "lucide:bluetooth": LucideIcons.Bluetooth,
  "lucide:radio": LucideIcons.Radio,
  "lucide:tv": LucideIcons.Tv,
  "lucide:plane": LucideIcons.Plane,
  "lucide:train": LucideIcons.Train,
  "lucide:bus": LucideIcons.Bus,
  "lucide:bike": LucideIcons.Bike,
  "lucide:ship": LucideIcons.Ship,
  "lucide:anchor": LucideIcons.Anchor,
  "lucide:compass": LucideIcons.Compass,
  "lucide:map-pin": LucideIcons.MapPin,
  "lucide:navigation": LucideIcons.Navigation,
  "lucide:building": LucideIcons.Building,
  "lucide:school": LucideIcons.School,
  "lucide:hospital": LucideIcons.Hospital,
  "lucide:store": LucideIcons.Store,
  "lucide:factory": LucideIcons.Factory,
  "lucide:tent": LucideIcons.Tent,
  "lucide:mountain": LucideIcons.Mountain,
  "lucide:waves": LucideIcons.Waves,
  "lucide:sunrise": LucideIcons.Sunrise,
  "lucide:sunset": LucideIcons.Sunset,
};

// Helper function to get SVG paths for icons (static approach for reliability)
const getIconSVGPaths = (iconName: string): string => {
  const iconPaths: Record<string, string> = {
    "lucide:heart": `<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
    "lucide:star": `<polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>`,
    "lucide:arrow-right": `<line x1="5" y1="12" x2="19" y2="12"/><polyline points="12,5 19,12 12,19"/>`,
    "lucide:arrow-left": `<line x1="19" y1="12" x2="5" y2="12"/><polyline points="12,19 5,12 12,5"/>`,
    "lucide:arrow-up": `<line x1="12" y1="19" x2="12" y2="5"/><polyline points="5,12 12,5 19,12"/>`,
    "lucide:arrow-down": `<line x1="12" y1="5" x2="12" y2="19"/><polyline points="19,12 12,19 5,12"/>`,
    "lucide:home": `<path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
    "lucide:user": `<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/>`,
    "lucide:settings": `<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>`,
    "lucide:mail": `<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/><polyline points="22,6 12,13 2,6"/>`,
    "lucide:phone": `<path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>`,
    "lucide:car": `<path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18.4 10H5.6L3.5 11.1C2.7 11.3 2 12.1 2 13v3c0 .6.4 1 1 1h2"/><circle cx="7" cy="17" r="2"/><path d="M9 17h6"/><circle cx="17" cy="17" r="2"/>`,
    "lucide:camera": `<path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"/><circle cx="12" cy="13" r="3"/>`,
    "lucide:music": `<path d="M9 18V5l12-2v13"/><circle cx="6" cy="18" r="3"/><circle cx="18" cy="16" r="3"/>`,
    "lucide:video": `<path d="M23 7l-7 5 7 5V7z"/><rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>`,
    "lucide:image": `<rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>`,
    "lucide:file": `<path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/>`,
    "lucide:folder": `<path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"/>`,
    "lucide:search": `<circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>`,
    "lucide:plus": `<path d="M5 12h14"/><path d="M12 5v14"/>`,
    "lucide:minus": `<path d="M5 12h14"/>`,
    "lucide:x": `<path d="M18 6 6 18"/><path d="m6 6 12 12"/>`,
    "lucide:check": `<polyline points="20,6 9,17 4,12"/>`,
    "lucide:edit": `<path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>`,
    "lucide:trash": `<path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2"/>`,
    "lucide:download": `<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/>`,
    "lucide:upload": `<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17,8 12,3 7,8"/><line x1="12" y1="3" x2="12" y2="15"/>`,
    "lucide:share": `<path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/><polyline points="16,6 12,2 8,6"/><line x1="12" y1="2" x2="12" y2="15"/>`,
    "lucide:copy": `<rect x="9" y="9" width="13" height="13" rx="2" ry="2"/><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>`,
    "lucide:save": `<path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/><polyline points="17,21 17,13 7,13 7,21"/><polyline points="7,3 7,8 15,8"/>`,
    "lucide:print": `<polyline points="6,9 6,2 18,2 18,9"/><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"/><rect x="6" y="14" width="12" height="8"/>`,
    "lucide:calendar": `<rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/>`,
    "lucide:clock": `<circle cx="12" cy="12" r="10"/><polyline points="12,6 12,12 16,14"/>`,
    "lucide:map": `<polygon points="1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2"/>`,
    "lucide:globe": `<circle cx="12" cy="12" r="10"/><line x1="2" y1="12" x2="22" y2="12"/><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>`,
    "lucide:wifi": `<path d="M5 12.55a11 11 0 0 1 14.08 0"/><path d="M1.42 9a16 16 0 0 1 21.16 0"/><path d="M8.53 16.11a6 6 0 0 1 6.95 0"/><line x1="12" y1="20" x2="12.01" y2="20"/>`,
    "lucide:battery": `<rect x="1" y="6" width="18" height="12" rx="2" ry="2"/><line x1="23" y1="13" x2="23" y2="11"/>`,
    "lucide:volume": `<polygon points="11,5 6,9 2,9 2,15 6,15 11,19"/><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>`,
    "lucide:play": `<polygon points="5,3 19,12 5,21"/>`,
    "lucide:pause": `<rect x="6" y="4" width="4" height="16"/><rect x="14" y="4" width="4" height="16"/>`,
    "lucide:stop": `<rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>`,
    "lucide:skip-forward": `<polygon points="5,4 15,12 5,20"/><line x1="19" y1="5" x2="19" y2="19"/>`,
    "lucide:skip-back": `<polygon points="19,20 9,12 19,4"/><line x1="5" y1="19" x2="5" y2="5"/>`,
    "lucide:refresh": `<polyline points="23,4 23,10 17,10"/><polyline points="1,20 1,14 7,14"/><path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4-4.64 4.36A9 9 0 0 1 3.51 15"/>`,
  };

  return iconPaths[iconName] || `<rect x="2" y="2" width="20" height="20" rx="2" ry="2" stroke-dasharray="2,2"/>`;
};

const buildEditor = ({
  save,
  undo,
  redo,
  canRedo,
  canUndo,
  autoZoom,
  copy,
  paste,
  canvas,
  fillColor,
  fontFamily,
  setFontFamily,
  setFillColor,
  strokeColor,
  setStrokeColor,
  strokeWidth,
  setStrokeWidth,
  selectedObjects,
  strokeDashArray,
  setStrokeDashArray,
}: BuildEditorProps): Editor => {
  const generateSaveOptions = () => {
    const { width, height, left, top } = getWorkspace() as fabric.Rect;

    return {
      name: "Image",
      format: "png",
      quality: 1,
      width,
      height,
      left,
      top,
    };
  };

  const savePng = () => {
    const options = generateSaveOptions();

    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    const dataUrl = canvas.toDataURL(options);

    downloadFile(dataUrl, "png");
    autoZoom();
  };

  const saveSvg = () => {
    const options = generateSaveOptions();

    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    const dataUrl = canvas.toDataURL(options);

    downloadFile(dataUrl, "svg");
    autoZoom();
  };

  const saveJpg = () => {
    const options = generateSaveOptions();

    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    const dataUrl = canvas.toDataURL(options);

    downloadFile(dataUrl, "jpg");
    autoZoom();
  };

  const saveJson = async () => {
    const dataUrl = canvas.toJSON(JSON_KEYS);

    await transformText(dataUrl.objects);
    const fileString = `data:text/json;charset=utf-8,${encodeURIComponent(
      JSON.stringify(dataUrl, null, "\t"),
    )}`;
    downloadFile(fileString, "json");
  };

  const loadJson = (json: string) => {
    const data = JSON.parse(json);

    canvas.loadFromJSON(data, () => {
      autoZoom();
    });
  };

  const getWorkspace = () => {
    return canvas
    .getObjects()
    .find((object) => object.name === "clip");
  };

  const center = (object: fabric.Object) => {
    const workspace = getWorkspace();
    const center = workspace?.getCenterPoint();

    if (!center) return;

    // @ts-ignore
    canvas._centerObject(object, center);
  };

  const addToCanvas = (object: fabric.Object) => {
    center(object);
    canvas.add(object);
    canvas.setActiveObject(object);
  };

  return {
    savePng,
    saveJpg,
    saveSvg,
    saveJson,
    loadJson,
    canUndo,
    canRedo,
    autoZoom,
    getWorkspace,
    zoomIn: () => {
      let zoomRatio = canvas.getZoom();
      zoomRatio += 0.05;
      const center = canvas.getCenter();
      canvas.zoomToPoint(
        new fabric.Point(center.left, center.top),
        zoomRatio > 1 ? 1 : zoomRatio
      );
    },
    zoomOut: () => {
      let zoomRatio = canvas.getZoom();
      zoomRatio -= 0.05;
      const center = canvas.getCenter();
      canvas.zoomToPoint(
        new fabric.Point(center.left, center.top),
        zoomRatio < 0.2 ? 0.2 : zoomRatio,
      );
    },
    changeSize: (value: { width: number; height: number }) => {
      const workspace = getWorkspace();

      workspace?.set(value);
      autoZoom();
      save();
    },
    changeBackground: (value: string) => {
      const workspace = getWorkspace();
      workspace?.set({ fill: value });
      canvas.renderAll();
      save();
    },
    enableDrawingMode: () => {
      canvas.discardActiveObject();
      canvas.renderAll();
      canvas.isDrawingMode = true;
      canvas.freeDrawingBrush.width = strokeWidth;
      canvas.freeDrawingBrush.color = strokeColor;
    },
    disableDrawingMode: () => {
      canvas.isDrawingMode = false;
    },
    onUndo: () => undo(),
    onRedo: () => redo(),
    onCopy: () => copy(),
    onPaste: () => paste(),
    changeImageFilter: (value: string) => {
      const objects = canvas.getActiveObjects();
      objects.forEach((object) => {
        if (object.type === "image") {
          const imageObject = object as fabric.Image;

          const effect = createFilter(value);

          imageObject.filters = effect ? [effect] : [];
          imageObject.applyFilters();
          canvas.renderAll();
        }
      });
    },
    addImage: (value: string) => {
      fabric.Image.fromURL(
        value,
        (image) => {
          const workspace = getWorkspace();

          image.scaleToWidth(workspace?.width || 0);
          image.scaleToHeight(workspace?.height || 0);

          addToCanvas(image);
        },
        {
          crossOrigin: "anonymous",
        },
      );
    },
    delete: () => {
      canvas.getActiveObjects().forEach((object) => canvas.remove(object));
      canvas.discardActiveObject();
      canvas.renderAll();
    },
    addText: (value, options) => {
      const object = new fabric.Textbox(value, {
        ...TEXT_OPTIONS,
        fill: fillColor,
        ...options,
      });

      addToCanvas(object);
    },
    getActiveOpacity: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return 1;
      }

      const value = selectedObject.get("opacity") || 1;

      return value;
    },
    changeFontSize: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontSize exists.
          object.set({ fontSize: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontSize: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return FONT_SIZE;
      }

      // @ts-ignore
      // Faulty TS library, fontSize exists.
      const value = selectedObject.get("fontSize") || FONT_SIZE;

      return value;
    },
    changeTextAlign: (value: string) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, textAlign exists.
          object.set({ textAlign: value });
        }
      });
      canvas.renderAll();
    },
    getActiveTextAlign: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return "left";
      }

      // @ts-ignore
      // Faulty TS library, textAlign exists.
      const value = selectedObject.get("textAlign") || "left";

      return value;
    },
    changeFontUnderline: (value: boolean) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, underline exists.
          object.set({ underline: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontUnderline: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return false;
      }

      // @ts-ignore
      // Faulty TS library, underline exists.
      const value = selectedObject.get("underline") || false;

      return value;
    },
    changeFontLinethrough: (value: boolean) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, linethrough exists.
          object.set({ linethrough: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontLinethrough: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return false;
      }

      // @ts-ignore
      // Faulty TS library, linethrough exists.
      const value = selectedObject.get("linethrough") || false;

      return value;
    },
    changeFontStyle: (value: string) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontStyle exists.
          object.set({ fontStyle: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontStyle: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return "normal";
      }

      // @ts-ignore
      // Faulty TS library, fontStyle exists.
      const value = selectedObject.get("fontStyle") || "normal";

      return value;
    },
    changeFontWeight: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontWeight exists.
          object.set({ fontWeight: value });
        }
      });
      canvas.renderAll();
    },
    changeOpacity: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        object.set({ opacity: value });
      });
      canvas.renderAll();
    },
    bringForward: () => {
      canvas.getActiveObjects().forEach((object) => {
        canvas.bringForward(object);
      });

      canvas.renderAll();
      
      const workspace = getWorkspace();
      workspace?.sendToBack();
    },
    sendBackwards: () => {
      canvas.getActiveObjects().forEach((object) => {
        canvas.sendBackwards(object);
      });

      canvas.renderAll();
      const workspace = getWorkspace();
      workspace?.sendToBack();
    },
    changeFontFamily: (value: string) => {
      setFontFamily(value);
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontFamily exists.
          object.set({ fontFamily: value });
        }
      });
      canvas.renderAll();
    },
    changeFillColor: (value: string) => {
      setFillColor(value);
      canvas.getActiveObjects().forEach((object) => {
        object.set({ fill: value });
      });
      canvas.renderAll();
    },
    changeStrokeColor: (value: string) => {
      setStrokeColor(value);
      canvas.getActiveObjects().forEach((object) => {
        // Text types don't have stroke
        if (isTextType(object.type)) {
          object.set({ fill: value });
          return;
        }

        object.set({ stroke: value });
      });
      canvas.freeDrawingBrush.color = value;
      canvas.renderAll();
    },
    changeStrokeWidth: (value: number) => {
      setStrokeWidth(value);
      canvas.getActiveObjects().forEach((object) => {
        object.set({ strokeWidth: value });
      });
      canvas.freeDrawingBrush.width = value;
      canvas.renderAll();
    },
    changeStrokeDashArray: (value: number[]) => {
      setStrokeDashArray(value);
      canvas.getActiveObjects().forEach((object) => {
        object.set({ strokeDashArray: value });
      });
      canvas.renderAll();
    },
    addCircle: () => {
      const object = new fabric.Circle({
        ...CIRCLE_OPTIONS,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addSoftRectangle: () => {
      const object = new fabric.Rect({
        ...RECTANGLE_OPTIONS,
        rx: 50,
        ry: 50,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addRectangle: () => {
      const object = new fabric.Rect({
        ...RECTANGLE_OPTIONS,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addTriangle: () => {
      const object = new fabric.Triangle({
        ...TRIANGLE_OPTIONS,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addInverseTriangle: () => {
      const HEIGHT = TRIANGLE_OPTIONS.height;
      const WIDTH = TRIANGLE_OPTIONS.width;

      const object = new fabric.Polygon(
        [
          { x: 0, y: 0 },
          { x: WIDTH, y: 0 },
          { x: WIDTH / 2, y: HEIGHT },
        ],
        {
          ...TRIANGLE_OPTIONS,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          strokeDashArray: strokeDashArray,
        }
      );

      addToCanvas(object);
    },
    addDiamond: () => {
      const HEIGHT = DIAMOND_OPTIONS.height;
      const WIDTH = DIAMOND_OPTIONS.width;

      const object = new fabric.Polygon(
        [
          { x: WIDTH / 2, y: 0 },
          { x: WIDTH, y: HEIGHT / 2 },
          { x: WIDTH / 2, y: HEIGHT },
          { x: 0, y: HEIGHT / 2 },
        ],
        {
          ...DIAMOND_OPTIONS,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          strokeDashArray: strokeDashArray,
        }
      );
      addToCanvas(object);
    },
    canvas,
    getActiveFontWeight: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return FONT_WEIGHT;
      }

      // @ts-ignore
      // Faulty TS library, fontWeight exists.
      const value = selectedObject.get("fontWeight") || FONT_WEIGHT;

      return value;
    },
    getActiveFontFamily: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return fontFamily;
      }

      // @ts-ignore
      // Faulty TS library, fontFamily exists.
      const value = selectedObject.get("fontFamily") || fontFamily;

      return value;
    },
    getActiveFillColor: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return fillColor;
      }

      const value = selectedObject.get("fill") || fillColor;

      // Currently, gradients & patterns are not supported
      return value as string;
    },
    getActiveStrokeColor: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return strokeColor;
      }

      const value = selectedObject.get("stroke") || strokeColor;

      return value;
    },
    getActiveStrokeWidth: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return strokeWidth;
      }

      const value = selectedObject.get("strokeWidth") || strokeWidth;

      return value;
    },
    getActiveStrokeDashArray: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return strokeDashArray;
      }

      const value = selectedObject.get("strokeDashArray") || strokeDashArray;

      return value;
    },
    addIcon: (iconName: string) => {
      if (!canvas) return;

      // Check if the icon exists in our comprehensive library
      const IconComponent = ICON_COMPONENTS[iconName];
      if (!IconComponent) {
        console.warn(`Icon ${iconName} not found in library`);
        return;
      }

      // Create SVG string using the proper Lucide icon
      const iconSize = 80;
      const iconColor = strokeColor || '#000000';

      // Create a more reliable SVG generation approach
      const svgString = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
        <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="${iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
          ${getIconSVGPaths(iconName)}
        </svg>
      `)}`;

      // Create fabric image from the SVG data URL
      fabric.Image.fromURL(svgString, (img) => {
        if (img && canvas) {
          img.set({
            left: 100,
            top: 100,
            scaleX: 1,
            scaleY: 1,
            // Allow full control over the icon
            lockScalingX: false,
            lockScalingY: false,
            lockUniScaling: false,
            lockMovementX: false,
            lockMovementY: false,
            lockRotation: false,
            // Ensure it's selectable and movable
            selectable: true,
            evented: true,
            // Add metadata for identification
            type: 'icon',
            iconName: iconName,
            // Store original color for color changes
            originalColor: iconColor,
          });
          addToCanvas(img);
        }
      });
    },
    // Icon-specific methods
    changeIconColor: (color: string) => {
      canvas.getActiveObjects().forEach((object) => {
        if (object.type === 'icon' && object.iconName) {
          // Regenerate the SVG with new color
          const iconSize = Math.max(object.width || 80, object.height || 80);
          const svgString = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
            <svg width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
              ${getIconSVGPaths(object.iconName)}
            </svg>
          `)}`;

          // Update the image source
          fabric.Image.fromURL(svgString, (newImg) => {
            if (newImg && canvas) {
              // Preserve the current transformation
              const currentTransform = {
                left: object.left,
                top: object.top,
                scaleX: object.scaleX,
                scaleY: object.scaleY,
                angle: object.angle,
                flipX: object.flipX,
                flipY: object.flipY,
              };

              // Remove old object and add new one
              canvas.remove(object);
              newImg.set({
                ...currentTransform,
                type: 'icon',
                iconName: object.iconName,
                originalColor: color,
                selectable: true,
                evented: true,
              });
              canvas.add(newImg);
              canvas.setActiveObject(newImg);
              canvas.renderAll();
              save();
            }
          });
        }
      });
    },
    changeIconSize: (width: number, height: number) => {
      canvas.getActiveObjects().forEach((object) => {
        if (object.type === 'icon') {
          object.set({
            scaleX: width / (object.width || 80),
            scaleY: height / (object.height || 80),
          });
        }
      });
      canvas.renderAll();
      save();
    },
    getActiveIconColor: () => {
      const selectedObject = selectedObjects[0];
      if (selectedObject && selectedObject.type === 'icon') {
        return selectedObject.originalColor || strokeColor;
      }
      return strokeColor;
    },
    getActiveIconSize: () => {
      const selectedObject = selectedObjects[0];
      if (selectedObject && selectedObject.type === 'icon') {
        const width = (selectedObject.width || 80) * (selectedObject.scaleX || 1);
        const height = (selectedObject.height || 80) * (selectedObject.scaleY || 1);
        return { width: Math.round(width), height: Math.round(height) };
      }
      return { width: 80, height: 80 };
    },
    selectedObjects,
  };
};

export const useEditor = ({
  defaultState,
  defaultHeight,
  defaultWidth,
  clearSelectionCallback,
  saveCallback,
  setCanvasIsSelected,
}: EditorHookProps) => {
  const initialState = useRef(defaultState);
  const initialWidth = useRef(defaultWidth);
  const initialHeight = useRef(defaultHeight);

  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [selectedObjects, setSelectedObjects] = useState<fabric.Object[]>([]);

  const [fontFamily, setFontFamily] = useState(FONT_FAMILY);
  const [fillColor, setFillColor] = useState(FILL_COLOR);
  const [strokeColor, setStrokeColor] = useState(STROKE_COLOR);
  const [strokeWidth, setStrokeWidth] = useState(STROKE_WIDTH);
  const [strokeDashArray, setStrokeDashArray] = useState<number[]>(STROKE_DASH_ARRAY);

  useWindowEvents();

  const { 
    save, 
    canRedo, 
    canUndo, 
    undo, 
    redo,
    canvasHistory,
    setHistoryIndex,
  } = useHistory({ 
    canvas,
    saveCallback
  });

  const { copy, paste } = useClipboard({ canvas });

  const { autoZoom } = useAutoResize({
    canvas,
    container,
  });

  useCanvasEvents({
    save,
    canvas,
    setSelectedObjects,
    clearSelectionCallback,
    setCanvasIsSelected,
  });

  useZoomEvents({
    canvas,
  });

  useHotkeys({
    undo,
    redo,
    copy,
    paste,
    save,
    canvas,
  });

  useLoadState({
    canvas,
    autoZoom,
    initialState,
    canvasHistory,
    setHistoryIndex,
  });

  const editor = useMemo(() => {
    if (canvas) {
      return buildEditor({
        save,
        undo,
        redo,
        canUndo,
        canRedo,
        autoZoom,
        copy,
        paste,
        canvas,
        fillColor,
        strokeWidth,
        strokeColor,
        setFillColor,
        setStrokeColor,
        setStrokeWidth,
        strokeDashArray,
        selectedObjects,
        setStrokeDashArray,
        fontFamily,
        setFontFamily,
      });
    }

    return undefined;
  }, 
  [
    canRedo,
    canUndo,
    undo,
    redo,
    save,
    autoZoom,
    copy,
    paste,
    canvas,
    fillColor,
    strokeWidth,
    strokeColor,
    selectedObjects,
    strokeDashArray,
    fontFamily,
  ]);

  const init = useCallback(
    ({
      initialCanvas,
      initialContainer,
    }: {
      initialCanvas: fabric.Canvas;
      initialContainer: HTMLDivElement;
    }) => {
      fabric.Object.prototype.set({
        cornerColor: "#FFF",
        cornerStyle: "circle",
        borderColor: "#3b82f6",
        borderScaleFactor: 1.5,
        transparentCorners: false,
        borderOpacityWhenMoving: 1,
        cornerStrokeColor: "#3b82f6",
      });

      const initialWorkspace = new fabric.Rect({
        width: initialWidth.current,
        height: initialHeight.current,
        name: "clip",
        fill: "white",
        selectable: false,
        hasControls: false,
        shadow: new fabric.Shadow({
          color: "rgba(0,0,0,0.8)",
          blur: 5,
        }),
      });

      initialCanvas.setWidth(initialContainer.offsetWidth);
      initialCanvas.setHeight(initialContainer.offsetHeight);

      initialCanvas.add(initialWorkspace);
      initialCanvas.centerObject(initialWorkspace);
      initialCanvas.clipPath = initialWorkspace;

      setCanvas(initialCanvas);
      setContainer(initialContainer);

      const currentState = JSON.stringify(
        initialCanvas.toJSON(JSON_KEYS)
      );
      canvasHistory.current = [currentState];
      setHistoryIndex(0);
    },
    [
      canvasHistory, // No need, this is from useRef
      setHistoryIndex, // No need, this is from useState
    ]
  );

  return { init, editor };
};
