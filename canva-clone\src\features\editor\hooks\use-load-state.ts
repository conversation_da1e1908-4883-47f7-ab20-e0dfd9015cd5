import { fabric } from "fabric";
import { useEffect, useRef } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  autoZoom: () => void;
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
};

export const useLoadState = ({
  canvas,
  autoZoom,
  initialState,
  canvasHistory,
  setHistoryIndex,
}: UseLoadStateProps) => {
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current && initialState?.current && canvas) {
      try {
        const data = JSON.parse(initialState.current);

        // Double-check canvas is still available and has loadFromJSON method
        if (canvas && typeof canvas.loadFromJSON === 'function') {
          canvas.loadFromJSON(data, () => {
            // Triple-check canvas is still available in callback
            if (canvas && typeof canvas.toJSON === 'function') {
              const currentState = JSON.stringify(
                canvas.toJSON(JSON_KEYS),
              );

              canvasHistory.current = [currentState];
              setHistoryIndex(0);
              autoZoom();
            }
          });
          initialized.current = true;
        }
      } catch (error) {
        console.error('Error loading canvas state:', error);
        // If there's an error, still mark as initialized to prevent infinite retries
        initialized.current = true;
      }
    }
  },
  [
    canvas,
    autoZoom,
    initialState, // no need, this is a ref
    canvasHistory, // no need, this is a ref
    setHistoryIndex, // no need, this is a dispatch
  ]);
};
