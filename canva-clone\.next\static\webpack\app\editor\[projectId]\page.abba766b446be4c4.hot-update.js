"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/hooks/use-editor.ts":
/*!*************************************************!*\
  !*** ./src/features/editor/hooks/use-editor.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditor: function() { return /* binding */ useEditor; }\n/* harmony export */ });\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fabric */ \"(app-pages-browser)/./node_modules/fabric/dist/fabric.js\");\n/* harmony import */ var fabric__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fabric__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/hooks/use-history */ \"(app-pages-browser)/./src/features/editor/hooks/use-history.ts\");\n/* harmony import */ var _features_editor_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/utils */ \"(app-pages-browser)/./src/features/editor/utils.ts\");\n/* harmony import */ var _features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/editor/hooks/use-hotkeys */ \"(app-pages-browser)/./src/features/editor/hooks/use-hotkeys.ts\");\n/* harmony import */ var _features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/editor/hooks//use-clipboard */ \"(app-pages-browser)/./src/features/editor/hooks/use-clipboard.ts\");\n/* harmony import */ var _features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/editor/hooks/use-auto-resize */ \"(app-pages-browser)/./src/features/editor/hooks/use-auto-resize.ts\");\n/* harmony import */ var _features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/editor/hooks/use-canvas-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-canvas-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/features/editor/hooks/use-zoom-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-zoom-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/features/editor/hooks/use-window-events */ \"(app-pages-browser)/./src/features/editor/hooks/use-window-events.ts\");\n/* harmony import */ var _features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/editor/hooks/use-load-state */ \"(app-pages-browser)/./src/features/editor/hooks/use-load-state.ts\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/battery.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-forward.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/skip-back.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_60__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_61__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock-open.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_62__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_63__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_64__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_65__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_66__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_67__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_68__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_69__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_70__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_71__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_72__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_73__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_74__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_75__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_76__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_77__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_78__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_79__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_80__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_81__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_82__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_83__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_84__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_85__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_86__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_87__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/umbrella.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_88__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/snowflake.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_89__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_90__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_91__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_92__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trees.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_93__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flower.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_94__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_95__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pizza.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_96__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_97__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wine.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_98__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_99__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_100__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_101__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_102__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_103__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/keyboard.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_104__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_105__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_106__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/usb.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_107__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bluetooth.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_108__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_109__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tv.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_110__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_111__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tram-front.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_112__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bus.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_113__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_114__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ship.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_115__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/anchor.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_116__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/compass.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_117__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_118__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/navigation.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_119__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_120__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_121__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hospital.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_122__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_123__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_124__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tent.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_125__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_126__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/waves.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_127__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sunrise.js\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_128__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sunset.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Comprehensive icon mapping using Lucide React icons\nconst ICON_COMPONENTS = {\n    \"lucide:heart\": lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    \"lucide:star\": lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    \"lucide:arrow-right\": lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    \"lucide:arrow-left\": lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    \"lucide:arrow-up\": lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    \"lucide:arrow-down\": lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    \"lucide:home\": lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    \"lucide:user\": lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    \"lucide:settings\": lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    \"lucide:mail\": lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    \"lucide:phone\": lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    \"lucide:car\": lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    \"lucide:camera\": lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    \"lucide:music\": lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    \"lucide:video\": lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n    \"lucide:image\": lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n    \"lucide:file\": lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n    \"lucide:folder\": lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n    \"lucide:search\": lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n    \"lucide:plus\": lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n    \"lucide:minus\": lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n    \"lucide:x\": lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n    \"lucide:check\": lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n    \"lucide:edit\": lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n    \"lucide:trash\": lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n    \"lucide:download\": lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n    \"lucide:upload\": lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n    \"lucide:share\": lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"],\n    \"lucide:copy\": lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n    \"lucide:save\": lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n    \"lucide:print\": lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    \"lucide:calendar\": lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n    \"lucide:clock\": lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"],\n    \"lucide:map\": lucide_react__WEBPACK_IMPORTED_MODULE_45__[\"default\"],\n    \"lucide:globe\": lucide_react__WEBPACK_IMPORTED_MODULE_46__[\"default\"],\n    \"lucide:wifi\": lucide_react__WEBPACK_IMPORTED_MODULE_47__[\"default\"],\n    \"lucide:battery\": lucide_react__WEBPACK_IMPORTED_MODULE_48__[\"default\"],\n    \"lucide:volume\": lucide_react__WEBPACK_IMPORTED_MODULE_49__[\"default\"],\n    \"lucide:play\": lucide_react__WEBPACK_IMPORTED_MODULE_50__[\"default\"],\n    \"lucide:pause\": lucide_react__WEBPACK_IMPORTED_MODULE_51__[\"default\"],\n    \"lucide:stop\": lucide_react__WEBPACK_IMPORTED_MODULE_52__[\"default\"],\n    \"lucide:skip-forward\": lucide_react__WEBPACK_IMPORTED_MODULE_53__[\"default\"],\n    \"lucide:skip-back\": lucide_react__WEBPACK_IMPORTED_MODULE_54__[\"default\"],\n    \"lucide:refresh\": lucide_react__WEBPACK_IMPORTED_MODULE_55__[\"default\"],\n    \"lucide:maximize\": lucide_react__WEBPACK_IMPORTED_MODULE_56__[\"default\"],\n    \"lucide:minimize\": lucide_react__WEBPACK_IMPORTED_MODULE_57__[\"default\"],\n    \"lucide:eye\": lucide_react__WEBPACK_IMPORTED_MODULE_58__[\"default\"],\n    \"lucide:eye-off\": lucide_react__WEBPACK_IMPORTED_MODULE_59__[\"default\"],\n    \"lucide:lock\": lucide_react__WEBPACK_IMPORTED_MODULE_60__[\"default\"],\n    \"lucide:unlock\": lucide_react__WEBPACK_IMPORTED_MODULE_61__[\"default\"],\n    \"lucide:shield\": lucide_react__WEBPACK_IMPORTED_MODULE_62__[\"default\"],\n    \"lucide:alert\": lucide_react__WEBPACK_IMPORTED_MODULE_63__[\"default\"],\n    \"lucide:info\": lucide_react__WEBPACK_IMPORTED_MODULE_64__[\"default\"],\n    \"lucide:help\": lucide_react__WEBPACK_IMPORTED_MODULE_65__[\"default\"],\n    \"lucide:bookmark\": lucide_react__WEBPACK_IMPORTED_MODULE_66__[\"default\"],\n    \"lucide:tag\": lucide_react__WEBPACK_IMPORTED_MODULE_67__[\"default\"],\n    \"lucide:flag\": lucide_react__WEBPACK_IMPORTED_MODULE_68__[\"default\"],\n    \"lucide:thumbs-up\": lucide_react__WEBPACK_IMPORTED_MODULE_69__[\"default\"],\n    \"lucide:thumbs-down\": lucide_react__WEBPACK_IMPORTED_MODULE_70__[\"default\"],\n    \"lucide:message\": lucide_react__WEBPACK_IMPORTED_MODULE_71__[\"default\"],\n    \"lucide:send\": lucide_react__WEBPACK_IMPORTED_MODULE_72__[\"default\"],\n    \"lucide:bell\": lucide_react__WEBPACK_IMPORTED_MODULE_73__[\"default\"],\n    \"lucide:gift\": lucide_react__WEBPACK_IMPORTED_MODULE_74__[\"default\"],\n    \"lucide:shopping-cart\": lucide_react__WEBPACK_IMPORTED_MODULE_75__[\"default\"],\n    \"lucide:credit-card\": lucide_react__WEBPACK_IMPORTED_MODULE_76__[\"default\"],\n    \"lucide:dollar-sign\": lucide_react__WEBPACK_IMPORTED_MODULE_77__[\"default\"],\n    \"lucide:trending-up\": lucide_react__WEBPACK_IMPORTED_MODULE_78__[\"default\"],\n    \"lucide:trending-down\": lucide_react__WEBPACK_IMPORTED_MODULE_79__[\"default\"],\n    \"lucide:bar-chart\": lucide_react__WEBPACK_IMPORTED_MODULE_80__[\"default\"],\n    \"lucide:pie-chart\": lucide_react__WEBPACK_IMPORTED_MODULE_81__[\"default\"],\n    \"lucide:activity\": lucide_react__WEBPACK_IMPORTED_MODULE_82__[\"default\"],\n    \"lucide:zap\": lucide_react__WEBPACK_IMPORTED_MODULE_83__[\"default\"],\n    \"lucide:sun\": lucide_react__WEBPACK_IMPORTED_MODULE_84__[\"default\"],\n    \"lucide:moon\": lucide_react__WEBPACK_IMPORTED_MODULE_85__[\"default\"],\n    \"lucide:cloud\": lucide_react__WEBPACK_IMPORTED_MODULE_86__[\"default\"],\n    \"lucide:umbrella\": lucide_react__WEBPACK_IMPORTED_MODULE_87__[\"default\"],\n    \"lucide:snowflake\": lucide_react__WEBPACK_IMPORTED_MODULE_88__[\"default\"],\n    \"lucide:droplet\": lucide_react__WEBPACK_IMPORTED_MODULE_89__[\"default\"],\n    \"lucide:flame\": lucide_react__WEBPACK_IMPORTED_MODULE_90__[\"default\"],\n    \"lucide:leaf\": lucide_react__WEBPACK_IMPORTED_MODULE_91__[\"default\"],\n    \"lucide:tree\": lucide_react__WEBPACK_IMPORTED_MODULE_92__[\"default\"],\n    \"lucide:flower\": lucide_react__WEBPACK_IMPORTED_MODULE_93__[\"default\"],\n    \"lucide:coffee\": lucide_react__WEBPACK_IMPORTED_MODULE_94__[\"default\"],\n    \"lucide:pizza\": lucide_react__WEBPACK_IMPORTED_MODULE_95__[\"default\"],\n    \"lucide:utensils\": lucide_react__WEBPACK_IMPORTED_MODULE_96__[\"default\"],\n    \"lucide:wine\": lucide_react__WEBPACK_IMPORTED_MODULE_97__[\"default\"],\n    \"lucide:gamepad\": lucide_react__WEBPACK_IMPORTED_MODULE_98__[\"default\"],\n    \"lucide:headphones\": lucide_react__WEBPACK_IMPORTED_MODULE_99__[\"default\"],\n    \"lucide:smartphone\": lucide_react__WEBPACK_IMPORTED_MODULE_100__[\"default\"],\n    \"lucide:laptop\": lucide_react__WEBPACK_IMPORTED_MODULE_101__[\"default\"],\n    \"lucide:monitor\": lucide_react__WEBPACK_IMPORTED_MODULE_102__[\"default\"],\n    \"lucide:keyboard\": lucide_react__WEBPACK_IMPORTED_MODULE_103__[\"default\"],\n    \"lucide:mouse\": lucide_react__WEBPACK_IMPORTED_MODULE_104__[\"default\"],\n    \"lucide:printer\": lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    \"lucide:hard-drive\": lucide_react__WEBPACK_IMPORTED_MODULE_105__[\"default\"],\n    \"lucide:usb\": lucide_react__WEBPACK_IMPORTED_MODULE_106__[\"default\"],\n    \"lucide:bluetooth\": lucide_react__WEBPACK_IMPORTED_MODULE_107__[\"default\"],\n    \"lucide:radio\": lucide_react__WEBPACK_IMPORTED_MODULE_108__[\"default\"],\n    \"lucide:tv\": lucide_react__WEBPACK_IMPORTED_MODULE_109__[\"default\"],\n    \"lucide:plane\": lucide_react__WEBPACK_IMPORTED_MODULE_110__[\"default\"],\n    \"lucide:train\": lucide_react__WEBPACK_IMPORTED_MODULE_111__[\"default\"],\n    \"lucide:bus\": lucide_react__WEBPACK_IMPORTED_MODULE_112__[\"default\"],\n    \"lucide:bike\": lucide_react__WEBPACK_IMPORTED_MODULE_113__[\"default\"],\n    \"lucide:ship\": lucide_react__WEBPACK_IMPORTED_MODULE_114__[\"default\"],\n    \"lucide:anchor\": lucide_react__WEBPACK_IMPORTED_MODULE_115__[\"default\"],\n    \"lucide:compass\": lucide_react__WEBPACK_IMPORTED_MODULE_116__[\"default\"],\n    \"lucide:map-pin\": lucide_react__WEBPACK_IMPORTED_MODULE_117__[\"default\"],\n    \"lucide:navigation\": lucide_react__WEBPACK_IMPORTED_MODULE_118__[\"default\"],\n    \"lucide:building\": lucide_react__WEBPACK_IMPORTED_MODULE_119__[\"default\"],\n    \"lucide:school\": lucide_react__WEBPACK_IMPORTED_MODULE_120__[\"default\"],\n    \"lucide:hospital\": lucide_react__WEBPACK_IMPORTED_MODULE_121__[\"default\"],\n    \"lucide:store\": lucide_react__WEBPACK_IMPORTED_MODULE_122__[\"default\"],\n    \"lucide:factory\": lucide_react__WEBPACK_IMPORTED_MODULE_123__[\"default\"],\n    \"lucide:tent\": lucide_react__WEBPACK_IMPORTED_MODULE_124__[\"default\"],\n    \"lucide:mountain\": lucide_react__WEBPACK_IMPORTED_MODULE_125__[\"default\"],\n    \"lucide:waves\": lucide_react__WEBPACK_IMPORTED_MODULE_126__[\"default\"],\n    \"lucide:sunrise\": lucide_react__WEBPACK_IMPORTED_MODULE_127__[\"default\"],\n    \"lucide:sunset\": lucide_react__WEBPACK_IMPORTED_MODULE_128__[\"default\"]\n};\n// Helper function to get SVG paths for icons (static approach for reliability)\nconst getIconSVGPaths = (iconName)=>{\n    const iconPaths = {\n        \"lucide:heart\": '<path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"/>',\n        \"lucide:star\": '<polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"/>',\n        \"lucide:arrow-right\": '<line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"/><polyline points=\"12,5 19,12 12,19\"/>',\n        \"lucide:arrow-left\": '<line x1=\"19\" y1=\"12\" x2=\"5\" y2=\"12\"/><polyline points=\"12,19 5,12 12,5\"/>',\n        \"lucide:arrow-up\": '<line x1=\"12\" y1=\"19\" x2=\"12\" y2=\"5\"/><polyline points=\"5,12 12,5 19,12\"/>',\n        \"lucide:arrow-down\": '<line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"/><polyline points=\"19,12 12,19 5,12\"/>',\n        \"lucide:home\": '<path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/><polyline points=\"9,22 9,12 15,12 15,22\"/>',\n        \"lucide:user\": '<path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/><circle cx=\"12\" cy=\"7\" r=\"4\"/>',\n        \"lucide:settings\": '<circle cx=\"12\" cy=\"12\" r=\"3\"/><path d=\"M12 1v6m0 6v6m11-7h-6m-6 0H1\"/>',\n        \"lucide:mail\": '<path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"/><polyline points=\"22,6 12,13 2,6\"/>',\n        \"lucide:phone\": '<path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"/>',\n        \"lucide:car\": '<path d=\"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18.4 10H5.6L3.5 11.1C2.7 11.3 2 12.1 2 13v3c0 .6.4 1 1 1h2\"/><circle cx=\"7\" cy=\"17\" r=\"2\"/><path d=\"M9 17h6\"/><circle cx=\"17\" cy=\"17\" r=\"2\"/>',\n        \"lucide:camera\": '<path d=\"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z\"/><circle cx=\"12\" cy=\"13\" r=\"3\"/>',\n        \"lucide:music\": '<path d=\"M9 18V5l12-2v13\"/><circle cx=\"6\" cy=\"18\" r=\"3\"/><circle cx=\"18\" cy=\"16\" r=\"3\"/>',\n        \"lucide:video\": '<path d=\"M23 7l-7 5 7 5V7z\"/><rect x=\"1\" y=\"5\" width=\"15\" height=\"14\" rx=\"2\" ry=\"2\"/>',\n        \"lucide:image\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><circle cx=\"9\" cy=\"9\" r=\"2\"/><path d=\"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\"/>',\n        \"lucide:file\": '<path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/>',\n        \"lucide:folder\": '<path d=\"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\"/>',\n        \"lucide:search\": '<circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/>',\n        \"lucide:plus\": '<path d=\"M5 12h14\"/><path d=\"M12 5v14\"/>',\n        \"lucide:minus\": '<path d=\"M5 12h14\"/>',\n        \"lucide:x\": '<path d=\"M18 6 6 18\"/><path d=\"m6 6 12 12\"/>',\n        \"lucide:check\": '<polyline points=\"20,6 9,17 4,12\"/>',\n        \"lucide:edit\": '<path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"/><path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"/>',\n        \"lucide:trash\": '<path d=\"M3 6h18\"/><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/><path d=\"M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2\"/>',\n        \"lucide:download\": '<path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/><polyline points=\"7,10 12,15 17,10\"/><line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"/>',\n        \"lucide:upload\": '<path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"/><polyline points=\"17,8 12,3 7,8\"/><line x1=\"12\" y1=\"3\" x2=\"12\" y2=\"15\"/>',\n        \"lucide:share\": '<path d=\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\"/><polyline points=\"16,6 12,2 8,6\"/><line x1=\"12\" y1=\"2\" x2=\"12\" y2=\"15\"/>',\n        \"lucide:copy\": '<rect x=\"9\" y=\"9\" width=\"13\" height=\"13\" rx=\"2\" ry=\"2\"/><path d=\"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"/>',\n        \"lucide:save\": '<path d=\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\"/><polyline points=\"17,21 17,13 7,13 7,21\"/><polyline points=\"7,3 7,8 15,8\"/>',\n        \"lucide:print\": '<polyline points=\"6,9 6,2 18,2 18,9\"/><path d=\"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2\"/><rect x=\"6\" y=\"14\" width=\"12\" height=\"8\"/>',\n        \"lucide:calendar\": '<rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\"/><line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\"/><line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\"/>',\n        \"lucide:clock\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><polyline points=\"12,6 12,12 16,14\"/>',\n        \"lucide:map\": '<polygon points=\"1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2\"/>',\n        \"lucide:globe\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"/><path d=\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\"/>',\n        \"lucide:wifi\": '<path d=\"M5 12.55a11 11 0 0 1 14.08 0\"/><path d=\"M1.42 9a16 16 0 0 1 21.16 0\"/><path d=\"M8.53 16.11a6 6 0 0 1 6.95 0\"/><line x1=\"12\" y1=\"20\" x2=\"12.01\" y2=\"20\"/>',\n        \"lucide:battery\": '<rect x=\"1\" y=\"6\" width=\"18\" height=\"12\" rx=\"2\" ry=\"2\"/><line x1=\"23\" y1=\"13\" x2=\"23\" y2=\"11\"/>',\n        \"lucide:volume\": '<polygon points=\"11,5 6,9 2,9 2,15 6,15 11,19\"/><path d=\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\"/>',\n        \"lucide:play\": '<polygon points=\"5,3 19,12 5,21\"/>',\n        \"lucide:pause\": '<rect x=\"6\" y=\"4\" width=\"4\" height=\"16\"/><rect x=\"14\" y=\"4\" width=\"4\" height=\"16\"/>',\n        \"lucide:stop\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>',\n        \"lucide:skip-forward\": '<polygon points=\"5,4 15,12 5,20\"/><line x1=\"19\" y1=\"5\" x2=\"19\" y2=\"19\"/>',\n        \"lucide:skip-back\": '<polygon points=\"19,20 9,12 19,4\"/><line x1=\"5\" y1=\"19\" x2=\"5\" y2=\"5\"/>',\n        \"lucide:refresh\": '<polyline points=\"23,4 23,10 17,10\"/><polyline points=\"1,20 1,14 7,14\"/><path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4-4.64 4.36A9 9 0 0 1 3.51 15\"/>',\n        // Additional icons\n        \"lucide:circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/>',\n        \"lucide:triangle\": '<path d=\"M12 2 22 20H2z\"/>',\n        \"lucide:diamond\": '<path d=\"M12 2 22 12 12 22 2 12z\"/>',\n        \"lucide:hexagon\": '<path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"/>',\n        \"lucide:octagon\": '<polygon points=\"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86\"/>',\n        \"lucide:arrow-up-right\": '<line x1=\"7\" y1=\"17\" x2=\"17\" y2=\"7\"/><polyline points=\"7,7 17,7 17,17\"/>',\n        \"lucide:arrow-down-left\": '<line x1=\"17\" y1=\"7\" x2=\"7\" y2=\"17\"/><polyline points=\"17,17 7,17 7,7\"/>',\n        \"lucide:chevron-right\": '<polyline points=\"9,18 15,12 9,6\"/>',\n        \"lucide:chevron-left\": '<polyline points=\"15,18 9,12 15,6\"/>',\n        \"lucide:chevron-up\": '<polyline points=\"18,15 12,9 6,15\"/>',\n        \"lucide:chevron-down\": '<polyline points=\"6,9 12,15 18,9\"/>',\n        \"lucide:move\": '<polyline points=\"5,9 2,12 5,15\"/><polyline points=\"9,5 12,2 15,5\"/><polyline points=\"15,19 12,22 9,19\"/><polyline points=\"19,9 22,12 19,15\"/><line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\"/><line x1=\"12\" y1=\"2\" x2=\"12\" y2=\"22\"/>',\n        \"lucide:corner-down-right\": '<polyline points=\"15,10 20,15 15,20\"/><path d=\"M4 4v7a4 4 0 0 0 4 4h12\"/>',\n        \"lucide:menu\": '<line x1=\"4\" y1=\"12\" x2=\"20\" y2=\"12\"/><line x1=\"4\" y1=\"6\" x2=\"20\" y2=\"6\"/><line x1=\"4\" y1=\"18\" x2=\"20\" y2=\"18\"/>',\n        \"lucide:more-horizontal\": '<circle cx=\"12\" cy=\"12\" r=\"1\"/><circle cx=\"19\" cy=\"12\" r=\"1\"/><circle cx=\"5\" cy=\"12\" r=\"1\"/>',\n        \"lucide:more-vertical\": '<circle cx=\"12\" cy=\"12\" r=\"1\"/><circle cx=\"12\" cy=\"5\" r=\"1\"/><circle cx=\"12\" cy=\"19\" r=\"1\"/>',\n        \"lucide:filter\": '<polygon points=\"22,3 2,3 10,12.46 10,19 14,21 14,12.46\"/>',\n        \"lucide:grid\": '<rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"/><rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"/><rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"/><rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"/>',\n        \"lucide:list\": '<line x1=\"8\" y1=\"6\" x2=\"21\" y2=\"6\"/><line x1=\"8\" y1=\"12\" x2=\"21\" y2=\"12\"/><line x1=\"8\" y1=\"18\" x2=\"21\" y2=\"18\"/><line x1=\"3\" y1=\"6\" x2=\"3.01\" y2=\"6\"/><line x1=\"3\" y1=\"12\" x2=\"3.01\" y2=\"12\"/><line x1=\"3\" y1=\"18\" x2=\"3.01\" y2=\"18\"/>',\n        \"lucide:layout\": '<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/><line x1=\"3\" y1=\"9\" x2=\"21\" y2=\"9\"/><line x1=\"9\" y1=\"21\" x2=\"9\" y2=\"9\"/>',\n        \"lucide:check-circle\": '<path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"/><polyline points=\"22,4 12,14.01 9,11.01\"/>',\n        \"lucide:x-circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"/><line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"/>',\n        \"lucide:alert-circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"/><line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"/>',\n        \"lucide:info\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"12\"/><line x1=\"12\" y1=\"8\" x2=\"12.01\" y2=\"8\"/>',\n        \"lucide:help-circle\": '<circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\"/><line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\"/>',\n        \"lucide:rotate-ccw\": '<polyline points=\"1,4 1,10 7,10\"/><path d=\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\"/>',\n        \"lucide:undo\": '<polyline points=\"1,4 1,10 7,10\"/><path d=\"M3.51 15a9 9 0 1 0 2.13-9.36L1 10\"/>',\n        \"lucide:redo\": '<polyline points=\"23,4 23,10 17,10\"/><path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10\"/>',\n        \"lucide:file-text\": '<path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/><line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"/><line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"/><line x1=\"10\" y1=\"9\" x2=\"8\" y2=\"9\"/>',\n        \"lucide:folder-open\": '<path d=\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\"/><path d=\"M2 7h20\"/>',\n        \"lucide:clipboard\": '<rect x=\"8\" y=\"2\" width=\"8\" height=\"4\" rx=\"1\" ry=\"1\"/><path d=\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"/>',\n        \"lucide:paperclip\": '<path d=\"M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49\"/>'\n    };\n    return iconPaths[iconName] || '<rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"2\" ry=\"2\" stroke-dasharray=\"2,2\"/>';\n};\nconst buildEditor = (param)=>{\n    let { save, undo, redo, canRedo, canUndo, autoZoom, copy, paste, canvas, fillColor, fontFamily, setFontFamily, setFillColor, strokeColor, setStrokeColor, strokeWidth, setStrokeWidth, selectedObjects, strokeDashArray, setStrokeDashArray } = param;\n    const generateSaveOptions = ()=>{\n        const { width, height, left, top } = getWorkspace();\n        return {\n            name: \"Image\",\n            format: \"png\",\n            quality: 1,\n            width,\n            height,\n            left,\n            top\n        };\n    };\n    const savePng = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"png\");\n        autoZoom();\n    };\n    const saveSvg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"svg\");\n        autoZoom();\n    };\n    const saveJpg = ()=>{\n        const options = generateSaveOptions();\n        canvas.setViewportTransform([\n            1,\n            0,\n            0,\n            1,\n            0,\n            0\n        ]);\n        const dataUrl = canvas.toDataURL(options);\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(dataUrl, \"jpg\");\n        autoZoom();\n    };\n    const saveJson = async ()=>{\n        const dataUrl = canvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS);\n        await (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.transformText)(dataUrl.objects);\n        const fileString = \"data:text/json;charset=utf-8,\".concat(encodeURIComponent(JSON.stringify(dataUrl, null, \"\t\")));\n        (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.downloadFile)(fileString, \"json\");\n    };\n    const loadJson = (json)=>{\n        const data = JSON.parse(json);\n        canvas.loadFromJSON(data, ()=>{\n            autoZoom();\n        });\n    };\n    const getWorkspace = ()=>{\n        return canvas.getObjects().find((object)=>object.name === \"clip\");\n    };\n    const center = (object)=>{\n        const workspace = getWorkspace();\n        const center = workspace === null || workspace === void 0 ? void 0 : workspace.getCenterPoint();\n        if (!center) return;\n        // @ts-ignore\n        canvas._centerObject(object, center);\n    };\n    const addToCanvas = (object)=>{\n        center(object);\n        canvas.add(object);\n        canvas.setActiveObject(object);\n    };\n    return {\n        savePng,\n        saveJpg,\n        saveSvg,\n        saveJson,\n        loadJson,\n        canUndo,\n        canRedo,\n        autoZoom,\n        getWorkspace,\n        zoomIn: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio += 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio > 1 ? 1 : zoomRatio);\n        },\n        zoomOut: ()=>{\n            let zoomRatio = canvas.getZoom();\n            zoomRatio -= 0.05;\n            const center = canvas.getCenter();\n            canvas.zoomToPoint(new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Point(center.left, center.top), zoomRatio < 0.2 ? 0.2 : zoomRatio);\n        },\n        changeSize: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set(value);\n            autoZoom();\n            save();\n        },\n        changeBackground: (value)=>{\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.set({\n                fill: value\n            });\n            canvas.renderAll();\n            save();\n        },\n        enableDrawingMode: ()=>{\n            canvas.discardActiveObject();\n            canvas.renderAll();\n            canvas.isDrawingMode = true;\n            canvas.freeDrawingBrush.width = strokeWidth;\n            canvas.freeDrawingBrush.color = strokeColor;\n        },\n        disableDrawingMode: ()=>{\n            canvas.isDrawingMode = false;\n        },\n        onUndo: ()=>undo(),\n        onRedo: ()=>redo(),\n        onCopy: ()=>copy(),\n        onPaste: ()=>paste(),\n        changeImageFilter: (value)=>{\n            const objects = canvas.getActiveObjects();\n            objects.forEach((object)=>{\n                if (object.type === \"image\") {\n                    const imageObject = object;\n                    const effect = (0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.createFilter)(value);\n                    imageObject.filters = effect ? [\n                        effect\n                    ] : [];\n                    imageObject.applyFilters();\n                    canvas.renderAll();\n                }\n            });\n        },\n        addImage: (value)=>{\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(value, (image)=>{\n                const workspace = getWorkspace();\n                image.scaleToWidth((workspace === null || workspace === void 0 ? void 0 : workspace.width) || 0);\n                image.scaleToHeight((workspace === null || workspace === void 0 ? void 0 : workspace.height) || 0);\n                addToCanvas(image);\n            }, {\n                crossOrigin: \"anonymous\"\n            });\n        },\n        delete: ()=>{\n            canvas.getActiveObjects().forEach((object)=>canvas.remove(object));\n            canvas.discardActiveObject();\n            canvas.renderAll();\n        },\n        addText: (value, options)=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Textbox(value, {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TEXT_OPTIONS,\n                fill: fillColor,\n                ...options\n            });\n            addToCanvas(object);\n        },\n        getActiveOpacity: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return 1;\n            }\n            const value = selectedObject.get(\"opacity\") || 1;\n            return value;\n        },\n        changeFontSize: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontSize exists.\n                    object.set({\n                        fontSize: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontSize exists.\n            const value = selectedObject.get(\"fontSize\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_SIZE;\n            return value;\n        },\n        changeTextAlign: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, textAlign exists.\n                    object.set({\n                        textAlign: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveTextAlign: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"left\";\n            }\n            // @ts-ignore\n            // Faulty TS library, textAlign exists.\n            const value = selectedObject.get(\"textAlign\") || \"left\";\n            return value;\n        },\n        changeFontUnderline: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, underline exists.\n                    object.set({\n                        underline: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontUnderline: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, underline exists.\n            const value = selectedObject.get(\"underline\") || false;\n            return value;\n        },\n        changeFontLinethrough: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, linethrough exists.\n                    object.set({\n                        linethrough: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontLinethrough: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return false;\n            }\n            // @ts-ignore\n            // Faulty TS library, linethrough exists.\n            const value = selectedObject.get(\"linethrough\") || false;\n            return value;\n        },\n        changeFontStyle: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontStyle exists.\n                    object.set({\n                        fontStyle: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        getActiveFontStyle: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return \"normal\";\n            }\n            // @ts-ignore\n            // Faulty TS library, fontStyle exists.\n            const value = selectedObject.get(\"fontStyle\") || \"normal\";\n            return value;\n        },\n        changeFontWeight: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontWeight exists.\n                    object.set({\n                        fontWeight: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeOpacity: (value)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    opacity: value\n                });\n            });\n            canvas.renderAll();\n        },\n        bringForward: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.bringForward(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        sendBackwards: ()=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                canvas.sendBackwards(object);\n            });\n            canvas.renderAll();\n            const workspace = getWorkspace();\n            workspace === null || workspace === void 0 ? void 0 : workspace.sendToBack();\n        },\n        changeFontFamily: (value)=>{\n            setFontFamily(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    // @ts-ignore\n                    // Faulty TS library, fontFamily exists.\n                    object.set({\n                        fontFamily: value\n                    });\n                }\n            });\n            canvas.renderAll();\n        },\n        changeFillColor: (value)=>{\n            setFillColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    fill: value\n                });\n            });\n            canvas.renderAll();\n        },\n        changeStrokeColor: (value)=>{\n            setStrokeColor(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                // Text types don't have stroke\n                if ((0,_features_editor_utils__WEBPACK_IMPORTED_MODULE_4__.isTextType)(object.type)) {\n                    object.set({\n                        fill: value\n                    });\n                    return;\n                }\n                object.set({\n                    stroke: value\n                });\n            });\n            canvas.freeDrawingBrush.color = value;\n            canvas.renderAll();\n        },\n        changeStrokeWidth: (value)=>{\n            setStrokeWidth(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeWidth: value\n                });\n            });\n            canvas.freeDrawingBrush.width = value;\n            canvas.renderAll();\n        },\n        changeStrokeDashArray: (value)=>{\n            setStrokeDashArray(value);\n            canvas.getActiveObjects().forEach((object)=>{\n                object.set({\n                    strokeDashArray: value\n                });\n            });\n            canvas.renderAll();\n        },\n        addCircle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Circle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.CIRCLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addSoftRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                rx: 50,\n                ry: 50,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addRectangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.RECTANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addTriangle: ()=>{\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Triangle({\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addInverseTriangle: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: 0,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: 0\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.TRIANGLE_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        addDiamond: ()=>{\n            const HEIGHT = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.height;\n            const WIDTH = _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS.width;\n            const object = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Polygon([\n                {\n                    x: WIDTH / 2,\n                    y: 0\n                },\n                {\n                    x: WIDTH,\n                    y: HEIGHT / 2\n                },\n                {\n                    x: WIDTH / 2,\n                    y: HEIGHT\n                },\n                {\n                    x: 0,\n                    y: HEIGHT / 2\n                }\n            ], {\n                ..._features_editor_types__WEBPACK_IMPORTED_MODULE_2__.DIAMOND_OPTIONS,\n                fill: fillColor,\n                stroke: strokeColor,\n                strokeWidth: strokeWidth,\n                strokeDashArray: strokeDashArray\n            });\n            addToCanvas(object);\n        },\n        canvas,\n        getActiveFontWeight: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontWeight exists.\n            const value = selectedObject.get(\"fontWeight\") || _features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_WEIGHT;\n            return value;\n        },\n        getActiveFontFamily: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fontFamily;\n            }\n            // @ts-ignore\n            // Faulty TS library, fontFamily exists.\n            const value = selectedObject.get(\"fontFamily\") || fontFamily;\n            return value;\n        },\n        getActiveFillColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return fillColor;\n            }\n            const value = selectedObject.get(\"fill\") || fillColor;\n            // Currently, gradients & patterns are not supported\n            return value;\n        },\n        getActiveStrokeColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeColor;\n            }\n            const value = selectedObject.get(\"stroke\") || strokeColor;\n            return value;\n        },\n        getActiveStrokeWidth: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeWidth;\n            }\n            const value = selectedObject.get(\"strokeWidth\") || strokeWidth;\n            return value;\n        },\n        getActiveStrokeDashArray: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (!selectedObject) {\n                return strokeDashArray;\n            }\n            const value = selectedObject.get(\"strokeDashArray\") || strokeDashArray;\n            return value;\n        },\n        addIcon: (iconName)=>{\n            if (!canvas) return;\n            // Check if the icon exists in our comprehensive library\n            const IconComponent = ICON_COMPONENTS[iconName];\n            if (!IconComponent) {\n                console.warn(\"Icon \".concat(iconName, \" not found in library\"));\n                return;\n            }\n            // Create SVG string using the proper Lucide icon\n            const iconSize = 80;\n            const iconColor = strokeColor || \"#000000\";\n            // Create a more reliable SVG generation approach\n            const svgString = \"data:image/svg+xml;charset=utf-8,\".concat(encodeURIComponent('\\n        <svg width=\"'.concat(iconSize, '\" height=\"').concat(iconSize, '\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"').concat(iconColor, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" xmlns=\"http://www.w3.org/2000/svg\">\\n          ').concat(getIconSVGPaths(iconName), \"\\n        </svg>\\n      \")));\n            // Create fabric image from the SVG data URL\n            fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(svgString, (img)=>{\n                if (img && canvas) {\n                    img.set({\n                        left: 100,\n                        top: 100,\n                        scaleX: 1,\n                        scaleY: 1,\n                        // Allow full control over the icon\n                        lockScalingX: false,\n                        lockScalingY: false,\n                        lockUniScaling: false,\n                        lockMovementX: false,\n                        lockMovementY: false,\n                        lockRotation: false,\n                        // Ensure it's selectable and movable\n                        selectable: true,\n                        evented: true,\n                        // Add metadata for identification\n                        type: \"icon\",\n                        iconName: iconName,\n                        // Store original color for color changes\n                        originalColor: iconColor\n                    });\n                    addToCanvas(img);\n                }\n            });\n        },\n        // Icon-specific methods\n        changeIconColor: (color)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if (object.type === \"icon\" && object.iconName) {\n                    // Regenerate the SVG with new color\n                    const iconSize = Math.max(object.width || 80, object.height || 80);\n                    const svgString = \"data:image/svg+xml;charset=utf-8,\".concat(encodeURIComponent('\\n            <svg width=\"'.concat(iconSize, '\" height=\"').concat(iconSize, '\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"').concat(color, '\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" xmlns=\"http://www.w3.org/2000/svg\">\\n              ').concat(getIconSVGPaths(object.iconName), \"\\n            </svg>\\n          \")));\n                    // Update the image source\n                    fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Image.fromURL(svgString, (newImg)=>{\n                        if (newImg && canvas) {\n                            // Preserve the current transformation\n                            const currentTransform = {\n                                left: object.left,\n                                top: object.top,\n                                scaleX: object.scaleX,\n                                scaleY: object.scaleY,\n                                angle: object.angle,\n                                flipX: object.flipX,\n                                flipY: object.flipY\n                            };\n                            // Remove old object and add new one\n                            canvas.remove(object);\n                            newImg.set({\n                                ...currentTransform,\n                                type: \"icon\",\n                                iconName: object.iconName,\n                                originalColor: color,\n                                selectable: true,\n                                evented: true\n                            });\n                            canvas.add(newImg);\n                            canvas.setActiveObject(newImg);\n                            canvas.renderAll();\n                            save();\n                        }\n                    });\n                }\n            });\n        },\n        changeIconSize: (width, height)=>{\n            canvas.getActiveObjects().forEach((object)=>{\n                if (object.type === \"icon\") {\n                    object.set({\n                        scaleX: width / (object.width || 80),\n                        scaleY: height / (object.height || 80)\n                    });\n                }\n            });\n            canvas.renderAll();\n            save();\n        },\n        getActiveIconColor: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (selectedObject && selectedObject.type === \"icon\") {\n                return selectedObject.originalColor || strokeColor;\n            }\n            return strokeColor;\n        },\n        getActiveIconSize: ()=>{\n            const selectedObject = selectedObjects[0];\n            if (selectedObject && selectedObject.type === \"icon\") {\n                const width = (selectedObject.width || 80) * (selectedObject.scaleX || 1);\n                const height = (selectedObject.height || 80) * (selectedObject.scaleY || 1);\n                return {\n                    width: Math.round(width),\n                    height: Math.round(height)\n                };\n            }\n            return {\n                width: 80,\n                height: 80\n            };\n        },\n        selectedObjects\n    };\n};\nconst useEditor = (param)=>{\n    let { defaultState, defaultHeight, defaultWidth, clearSelectionCallback, saveCallback, setCanvasIsSelected } = param;\n    const initialState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultState);\n    const initialWidth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultWidth);\n    const initialHeight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(defaultHeight);\n    const [canvas, setCanvas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fontFamily, setFontFamily] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FONT_FAMILY);\n    const [fillColor, setFillColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.FILL_COLOR);\n    const [strokeColor, setStrokeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_COLOR);\n    const [strokeWidth, setStrokeWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_WIDTH);\n    const [strokeDashArray, setStrokeDashArray] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.STROKE_DASH_ARRAY);\n    (0,_features_editor_hooks_use_window_events__WEBPACK_IMPORTED_MODULE_10__.useWindowEvents)();\n    const { save, canRedo, canUndo, undo, redo, canvasHistory, setHistoryIndex } = (0,_features_editor_hooks_use_history__WEBPACK_IMPORTED_MODULE_3__.useHistory)({\n        canvas,\n        saveCallback\n    });\n    const { copy, paste } = (0,_features_editor_hooks_use_clipboard__WEBPACK_IMPORTED_MODULE_6__.useClipboard)({\n        canvas\n    });\n    const { autoZoom } = (0,_features_editor_hooks_use_auto_resize__WEBPACK_IMPORTED_MODULE_7__.useAutoResize)({\n        canvas,\n        container\n    });\n    (0,_features_editor_hooks_use_canvas_events__WEBPACK_IMPORTED_MODULE_8__.useCanvasEvents)({\n        save,\n        canvas,\n        setSelectedObjects,\n        clearSelectionCallback,\n        setCanvasIsSelected\n    });\n    (0,_features_editor_hooks_use_zoom_events__WEBPACK_IMPORTED_MODULE_9__.useZoomEvents)({\n        canvas\n    });\n    (0,_features_editor_hooks_use_hotkeys__WEBPACK_IMPORTED_MODULE_5__.useHotkeys)({\n        undo,\n        redo,\n        copy,\n        paste,\n        save,\n        canvas\n    });\n    (0,_features_editor_hooks_use_load_state__WEBPACK_IMPORTED_MODULE_11__.useLoadState)({\n        canvas,\n        autoZoom,\n        initialState,\n        canvasHistory,\n        setHistoryIndex\n    });\n    const editor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (canvas) {\n            return buildEditor({\n                save,\n                undo,\n                redo,\n                canUndo,\n                canRedo,\n                autoZoom,\n                copy,\n                paste,\n                canvas,\n                fillColor,\n                strokeWidth,\n                strokeColor,\n                setFillColor,\n                setStrokeColor,\n                setStrokeWidth,\n                strokeDashArray,\n                selectedObjects,\n                setStrokeDashArray,\n                fontFamily,\n                setFontFamily\n            });\n        }\n        return undefined;\n    }, [\n        canRedo,\n        canUndo,\n        undo,\n        redo,\n        save,\n        autoZoom,\n        copy,\n        paste,\n        canvas,\n        fillColor,\n        strokeWidth,\n        strokeColor,\n        selectedObjects,\n        strokeDashArray,\n        fontFamily\n    ]);\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((param)=>{\n        let { initialCanvas, initialContainer } = param;\n        fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Object.prototype.set({\n            cornerColor: \"#FFF\",\n            cornerStyle: \"circle\",\n            borderColor: \"#3b82f6\",\n            borderScaleFactor: 1.5,\n            transparentCorners: false,\n            borderOpacityWhenMoving: 1,\n            cornerStrokeColor: \"#3b82f6\"\n        });\n        const initialWorkspace = new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Rect({\n            width: initialWidth.current,\n            height: initialHeight.current,\n            name: \"clip\",\n            fill: \"white\",\n            selectable: false,\n            hasControls: false,\n            shadow: new fabric__WEBPACK_IMPORTED_MODULE_0__.fabric.Shadow({\n                color: \"rgba(0,0,0,0.8)\",\n                blur: 5\n            })\n        });\n        initialCanvas.setWidth(initialContainer.offsetWidth);\n        initialCanvas.setHeight(initialContainer.offsetHeight);\n        initialCanvas.add(initialWorkspace);\n        initialCanvas.centerObject(initialWorkspace);\n        initialCanvas.clipPath = initialWorkspace;\n        setCanvas(initialCanvas);\n        setContainer(initialContainer);\n        const currentState = JSON.stringify(initialCanvas.toJSON(_features_editor_types__WEBPACK_IMPORTED_MODULE_2__.JSON_KEYS));\n        canvasHistory.current = [\n            currentState\n        ];\n        setHistoryIndex(0);\n    }, [\n        canvasHistory,\n        setHistoryIndex\n    ]);\n    return {\n        init,\n        editor\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/hooks/use-editor.ts\n"));

/***/ })

});