"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[projectId]/page",{

/***/ "(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/editor/components/stroke-color-sidebar.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StrokeColorSidebar: function() { return /* binding */ StrokeColorSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _features_editor_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/editor/types */ \"(app-pages-browser)/./src/features/editor/types.ts\");\n/* harmony import */ var _features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-close */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-close.tsx\");\n/* harmony import */ var _features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/editor/components/tool-sidebar-header */ \"(app-pages-browser)/./src/features/editor/components/tool-sidebar-header.tsx\");\n/* harmony import */ var _features_editor_components_color_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/editor/components/color-picker */ \"(app-pages-browser)/./src/features/editor/components/color-picker.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n\n\n\n\n\n\n\nconst StrokeColorSidebar = (param)=>{\n    let { editor, activeTool, onChangeActiveTool } = param;\n    const selectedObject = editor === null || editor === void 0 ? void 0 : editor.selectedObjects[0];\n    const isIcon = (selectedObject === null || selectedObject === void 0 ? void 0 : selectedObject.type) === \"icon\";\n    // Get the appropriate color value based on object type\n    const value = isIcon ? (editor === null || editor === void 0 ? void 0 : editor.getActiveIconColor()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_1__.STROKE_COLOR : (editor === null || editor === void 0 ? void 0 : editor.getActiveStrokeColor()) || _features_editor_types__WEBPACK_IMPORTED_MODULE_1__.STROKE_COLOR;\n    const onClose = ()=>{\n        onChangeActiveTool(\"select\");\n    };\n    const onChange = (value)=>{\n        if (isIcon) {\n            editor === null || editor === void 0 ? void 0 : editor.changeIconColor(value);\n        } else {\n            editor === null || editor === void 0 ? void 0 : editor.changeStrokeColor(value);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"bg-white relative border-r z-[40] w-[360px] h-full flex flex-col\", activeTool === \"stroke-color\" ? \"visible\" : \"hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_header__WEBPACK_IMPORTED_MODULE_3__.ToolSidebarHeader, {\n                title: isIcon ? \"Icon Color\" : \"Stroke color\",\n                description: isIcon ? \"Change the color of your icon\" : \"Add stroke color to your element\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 space-y-6\",\n                    onClick: (e)=>e.stopPropagation(),\n                    onMouseDown: (e)=>e.stopPropagation(),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_color_picker__WEBPACK_IMPORTED_MODULE_4__.ColorPicker, {\n                        value: value,\n                        onChange: onChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_editor_components_tool_sidebar_close__WEBPACK_IMPORTED_MODULE_2__.ToolSidebarClose, {\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SKETTCHA\\\\canva-clone\\\\src\\\\features\\\\editor\\\\components\\\\stroke-color-sidebar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c = StrokeColorSidebar;\nvar _c;\n$RefreshReg$(_c, \"StrokeColorSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/editor/components/stroke-color-sidebar.tsx\n"));

/***/ })

});