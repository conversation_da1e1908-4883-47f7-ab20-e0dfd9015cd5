"use client";

import { useEffect, useState } from "react";
import { fabric } from "fabric";

interface AlignmentIndicatorsProps {
  canvas: fabric.Canvas | null;
  selectedObjects: fabric.Object[];
}

interface AlignmentState {
  horizontalCenter: boolean;
  verticalCenter: boolean;
}

export const AlignmentIndicators = ({ 
  canvas, 
  selectedObjects 
}: AlignmentIndicatorsProps) => {
  const [alignment, setAlignment] = useState<AlignmentState>({
    horizontalCenter: false,
    verticalCenter: false,
  });

  useEffect(() => {
    if (!canvas || selectedObjects.length === 0) {
      setAlignment({ horizontalCenter: false, verticalCenter: false });
      return;
    }

    const checkAlignment = () => {
      const workspace = canvas.getObjects().find((obj) => obj.name === "clip") as fabric.Rect;
      if (!workspace) return;

      const workspaceCenter = workspace.getCenterPoint();
      const snapTolerance = 15; // Pixel tolerance for snapping
      const showTolerance = 3; // Pixel tolerance for showing indicators

      let isHorizontalCenter = false;
      let isVerticalCenter = false;

      if (selectedObjects.length === 1) {
        // Single object alignment with snapping
        const obj = selectedObjects[0];
        const objCenter = obj.getCenterPoint();

        const horizontalDistance = Math.abs(objCenter.x - workspaceCenter.x);
        const verticalDistance = Math.abs(objCenter.y - workspaceCenter.y);

        // Snap to center if within snap tolerance
        if (horizontalDistance <= snapTolerance) {
          obj.set({ left: obj.left! + (workspaceCenter.x - objCenter.x) });
          isHorizontalCenter = true;
        } else {
          isHorizontalCenter = horizontalDistance <= showTolerance;
        }

        if (verticalDistance <= snapTolerance) {
          obj.set({ top: obj.top! + (workspaceCenter.y - objCenter.y) });
          isVerticalCenter = true;
        } else {
          isVerticalCenter = verticalDistance <= showTolerance;
        }

        // Update canvas if snapping occurred
        if ((horizontalDistance <= snapTolerance) || (verticalDistance <= snapTolerance)) {
          canvas.renderAll();
        }
      } else if (selectedObjects.length > 1) {
        // Multiple objects - check if their collective center is aligned
        let totalX = 0;
        let totalY = 0;

        selectedObjects.forEach((obj) => {
          const center = obj.getCenterPoint();
          totalX += center.x;
          totalY += center.y;
        });

        const groupCenterX = totalX / selectedObjects.length;
        const groupCenterY = totalY / selectedObjects.length;

        const horizontalDistance = Math.abs(groupCenterX - workspaceCenter.x);
        const verticalDistance = Math.abs(groupCenterY - workspaceCenter.y);

        // Snap group to center if within snap tolerance
        if (horizontalDistance <= snapTolerance) {
          const offsetX = workspaceCenter.x - groupCenterX;
          selectedObjects.forEach((obj) => {
            obj.set({ left: obj.left! + offsetX });
          });
          isHorizontalCenter = true;
        } else {
          isHorizontalCenter = horizontalDistance <= showTolerance;
        }

        if (verticalDistance <= snapTolerance) {
          const offsetY = workspaceCenter.y - groupCenterY;
          selectedObjects.forEach((obj) => {
            obj.set({ top: obj.top! + offsetY });
          });
          isVerticalCenter = true;
        } else {
          isVerticalCenter = verticalDistance <= showTolerance;
        }

        // Update canvas if snapping occurred
        if ((horizontalDistance <= snapTolerance) || (verticalDistance <= snapTolerance)) {
          canvas.renderAll();
        }
      }

      setAlignment({
        horizontalCenter: isHorizontalCenter,
        verticalCenter: isVerticalCenter,
      });
    };

    // Check alignment initially
    checkAlignment();

    // Listen for object movements
    const handleObjectMoving = () => checkAlignment();
    const handleObjectModified = () => checkAlignment();

    canvas.on("object:moving", handleObjectMoving);
    canvas.on("object:modified", handleObjectModified);

    return () => {
      canvas.off("object:moving", handleObjectMoving);
      canvas.off("object:modified", handleObjectModified);
    };
  }, [canvas, selectedObjects]);

  if (!canvas || selectedObjects.length === 0) {
    return null;
  }

  const workspace = canvas.getObjects().find((obj) => obj.name === "clip") as fabric.Rect;
  if (!workspace) return null;

  const canvasElement = canvas.getElement();
  const canvasRect = canvasElement.getBoundingClientRect();
  const zoom = canvas.getZoom();
  const viewportTransform = canvas.viewportTransform || [1, 0, 0, 1, 0, 0];

  // Calculate workspace position on screen
  const workspaceLeft = workspace.left || 0;
  const workspaceTop = workspace.top || 0;
  const workspaceWidth = workspace.width || 0;
  const workspaceHeight = workspace.height || 0;

  // Transform workspace coordinates to screen coordinates
  const screenLeft = (workspaceLeft * zoom + viewportTransform[4]);
  const screenTop = (workspaceTop * zoom + viewportTransform[5]);
  const screenWidth = workspaceWidth * zoom;
  const screenHeight = workspaceHeight * zoom;

  const centerX = screenLeft + screenWidth / 2;
  const centerY = screenTop + screenHeight / 2;

  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      {/* Horizontal center line */}
      {alignment.horizontalCenter && (
        <div
          className="absolute bg-blue-600 shadow-sm"
          style={{
            left: `${centerX - 1}px`,
            top: `${screenTop}px`,
            width: "2px",
            height: `${screenHeight}px`,
            opacity: 0.9,
          }}
        />
      )}

      {/* Vertical center line */}
      {alignment.verticalCenter && (
        <div
          className="absolute bg-blue-600 shadow-sm"
          style={{
            left: `${screenLeft}px`,
            top: `${centerY - 1}px`,
            width: `${screenWidth}px`,
            height: "2px",
            opacity: 0.9,
          }}
        />
      )}
    </div>
  );
};
